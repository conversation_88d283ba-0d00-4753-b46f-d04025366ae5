<!DOCTYPE html>
<html>
<head>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script type="text/javascript" src="charts.js"></script>
    <script type="text/javascript" src="logic.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css">
    <title>XY6020 Monitoring</title>
    <style>
        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
            margin-bottom: 20px;
        }
        .chart-controls {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .chart-controls select, .chart-controls input {
            background-color: #363f4f;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            margin-left: 10px;
        }
        .chart-controls label {
            color: #ccc;
            margin-right: 5px;
        }
        .data-logging-panel {
            background-color: #2a3441;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            border: 1px solid #4a5568;
        }
        .logging-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .logging-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #e53e3e;
        }
        .status-indicator.active {
            background-color: #38a169;
        }
        .logging-buttons {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body onload="initCharts()">
    <center>
        <h1 style="font-size: 50px; color: gray">XY6020 Monitoring <span id="connection-state"
                style="display: inline; color: lightgreen;">&#10003;</span>
        </h1>
        
        <div class="my-container" style="width: 80%;">
            <span>Real-time Monitoring</span>
            
            <div class="chart-controls">
                <div>
                    <label for="update-interval">Update Interval:</label>
                    <select id="update-interval" onchange="updateChartSettings()">
                        <option value="500">0.5 seconds</option>
                        <option value="1000" selected>1 second</option>
                        <option value="2000">2 seconds</option>
                        <option value="5000">5 seconds</option>
                    </select>
                </div>
                <div>
                    <label for="time-range">Time Range:</label>
                    <select id="time-range" onchange="updateChartSettings()">
                        <option value="30">30 seconds</option>
                        <option value="60" selected>1 minute</option>
                        <option value="300">5 minutes</option>
                        <option value="600">10 minutes</option>
                    </select>
                </div>
                <div>
                    <button class="small-button" onclick="clearChartData()">Clear Data</button>
                </div>
            </div>
            
            <div class="chart-container">
                <canvas id="voltageChart"></canvas>
            </div>
            
            <div class="chart-container">
                <canvas id="currentChart"></canvas>
            </div>
            
            <div class="chart-container">
                <canvas id="powerChart"></canvas>
            </div>
        </div>

        <!-- Data Logging Panel -->
        <div class="my-container data-logging-panel" style="width: 80%;">
            <span>Data Logging</span>

            <div class="logging-controls">
                <div class="logging-status">
                    <div id="logging-indicator" class="status-indicator"></div>
                    <span id="logging-status-text">Disabled</span>
                    <span id="logging-info" style="color: #888; font-size: 12px;">0 entries</span>
                </div>

                <div class="logging-buttons">
                    <button class="small-button" onclick="toggleDataLogging()" id="toggle-logging-btn">Enable</button>
                    <button class="small-button" onclick="showLoggingConfig()">Config</button>
                    <button class="small-button" onclick="downloadLogs('csv')">Download CSV</button>
                    <button class="small-button" onclick="downloadLogs('json')">Download JSON</button>
                    <button class="small-button" onclick="clearLogs()">Clear</button>
                </div>
            </div>

            <!-- Logging Configuration Panel (Hidden by default) -->
            <div id="logging-config-panel" style="display: none; margin-top: 15px; padding-top: 15px; border-top: 1px solid #4a5568;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label for="log-interval">Interval (seconds):</label>
                        <input type="number" id="log-interval" min="0.5" max="300" step="0.5" value="5"
                               style="width: 100%; background-color: #363f4f; border: none; color: white; padding: 5px; border-radius: 3px;">
                    </div>
                    <div>
                        <label for="max-entries">Max Entries:</label>
                        <input type="number" id="max-entries" min="10" max="1000" step="10" value="100"
                               style="width: 100%; background-color: #363f4f; border: none; color: white; padding: 5px; border-radius: 3px;">
                    </div>
                    <div>
                        <label>
                            <input type="checkbox" id="auto-save" style="margin-right: 5px;">
                            Auto Save
                        </label>
                    </div>
                    <div>
                        <label for="save-interval">Save Interval (minutes):</label>
                        <input type="number" id="save-interval" min="1" max="60" step="1" value="5"
                               style="width: 100%; background-color: #363f4f; border: none; color: white; padding: 5px; border-radius: 3px;">
                    </div>
                </div>
                <div style="margin-top: 10px; text-align: right;">
                    <button class="small-button" onclick="saveLoggingConfig()">Save Config</button>
                    <button class="small-button" onclick="hideLoggingConfig()">Cancel</button>
                </div>
            </div>
        </div>
        
        <br>
        <div style="width: 60%; margin:0px; padding: 0px;">
            <button class="my-button small" id="back-button" onclick="goBack()">Back</button>
            <button class="my-button small" id="refresh-button" onclick="togglePause()">Pause</button>
        </div>
    </center>
</body>
</html>
