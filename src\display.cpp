#include "display.h"
#include "electrowinning_state.h"
#include "data_logger.h"

// Pin definitions (shared with main.cpp)
#define PH_SENSOR_PIN    36      // GPIO36 (VP) - pH Sensor Board P0 pin (Analog)

// pH Calibration global variables
PHCalibrationData pHCalibration;
CalibrationState calibrationState;

// External data logger instance
extern DataLogger dataLogger;

// Implementasi fungsi display
String formatFloat(float value, int decimalPlaces) {
    String formatted = String(value, decimalPlaces);
    return formatted;
}

void displayMainScreen() {
    // SESUAI menu_hierarchy_navigation_guide.md - LEVEL_STATUS display
    if (menuState.currentLevel == LEVEL_STATUS) {
        // Show all sensor data and navigation - SESUAI refrensicodedisplaycpp.md
        lcd.clear();
        lcd.setCursor(0, 0);
        // Line 1: pH, Temperature, Hydrogen (H2)
        lcd.print("pH:"); lcd.print(formatFloat(pH, 1));
        lcd.print(" T:"); lcd.print(formatFloat(temp, 1)); lcd.print("C");
        lcd.print(" H:"); lcd.print(formatFloat(gas, 0)); lcd.print("%");

        // Get power data for voltage and current display
        PowerData powerData = electrowinningState.getPowerData();

        lcd.setCursor(0, 1);
        // Line 2: Pump dan Fan di baris yang sama
        if (isRunning) {
            const auto& outputControl = electrowinningState.getOutputControl();
            if (outputControl.pumpEnabled) {
                lcd.print("Pump:"); lcd.print(outputControl.pumpSpeed); lcd.print("%");
            } else {
                lcd.print("Pump:OFF");
            }
        } else {
            lcd.print("Pump:"); lcd.print(pumpSpeedSetting); lcd.print("%");
        }

        // Fan di baris yang sama dengan Pump (posisi tengah)
        lcd.setCursor(11, 1);
        if (isRunning) {
            const auto& outputControl = electrowinningState.getOutputControl();
            if (outputControl.fanEnabled) {
                lcd.print("Fan:"); lcd.print(outputControl.fanSpeed); lcd.print("%");
            } else {
                lcd.print("Fan:OFF");
            }
        } else {
            lcd.print("Fan:"); lcd.print(fanSpeed); lcd.print("%");
        }

        lcd.setCursor(0, 2);
        // Line 3: Voltage dan Current di baris yang sama
        if (powerData.isConnected && isRunning) {
            lcd.print("Volt:"); lcd.print(formatFloat(powerData.actualVoltage, 1));
        } else if (powerData.isConnected && !isRunning) {
            lcd.print("Volt:"); lcd.print(formatFloat(powerData.targetVoltage, 1));
        } else {
            lcd.print("Volt:0.0");
        }

        // Current di baris yang sama dengan Voltage (posisi kanan)
        lcd.setCursor(11, 2);
        if (powerData.isConnected && isRunning) {
            lcd.print("Amp:"); lcd.print(formatFloat(powerData.actualCurrent, 1)); lcd.print("A");
        } else if (powerData.isConnected && !isRunning) {
            lcd.print("Amp:"); lcd.print(formatFloat(powerData.targetCurrent, 1)); lcd.print("A");
        } else {
            lcd.print("Amp:0.0A");
        }

        lcd.setCursor(0, 3);
        lcd.print(menuState.currentItem == 0 ? (isRunning ? "-> Stop" : "-> Start") : (isRunning ? "   Stop" : "   Start"));
        lcd.print(menuState.currentItem == 1 ? "  -> Setup" : "     Setup");
    } else {
        // Use standard menu display for other levels
        displayMenuTemplate();
    }
}

// Standard menu display template - SESUAI menu_hierarchy_navigation_guide.md Section 3
void displayMenuTemplate() {
    const MenuItem* currentMenu = getCurrentMenu();
    int menuSize = getCurrentMenuSize();

    Serial.printf("displayMenuTemplate: Level=%d, MenuSize=%d, Menu=%s\n",
                  menuState.currentLevel, menuSize, currentMenu ? "OK" : "NULL");

    lcd.clear();
    lcd.setCursor(0, 0);

    // Display menu title based on current level
    switch (menuState.currentLevel) {
        case LEVEL_START: lcd.print("Start Menu"); break;
        case LEVEL_SETUP: lcd.print("Setup Menu"); break;
        case LEVEL_MANUAL_SETTINGS: lcd.print("Manual Settings"); break;
        case LEVEL_SENSOR_SETTINGS: lcd.print("Sensor Settings"); break;
        case LEVEL_OUTPUT_SETTINGS: lcd.print("Output Settings"); break;
        case LEVEL_EDIT_pH: lcd.print("pH Settings"); break;
        case LEVEL_EDIT_Temp: lcd.print("Temp Settings"); break;
        case LEVEL_EDIT_Gas: lcd.print("Gas Settings"); break;
        case LEVEL_CALIBRATION: lcd.print("Calibration"); break;
        case LEVEL_PH_CALIBRATION: lcd.print("pH Calibration"); break;
        case LEVEL_PH_CAL_4: lcd.print("pH 4.00 Cal"); break;
        case LEVEL_PH_CAL_7: lcd.print("pH 6.86 Cal"); break;
        case LEVEL_PH_CAL_9: lcd.print("pH 9.18 Cal"); break;
        case LEVEL_PH_CAL_VIEW: lcd.print("Cal Data"); break;
        case LEVEL_PH_CAL_RESET: lcd.print("Reset Cal"); break;
        case LEVEL_DATA_LOGGING: lcd.print("Data Logging"); break;
        case LEVEL_LOG_CONFIG: lcd.print("Log Config"); break;
        case LEVEL_LOG_VIEW: lcd.print("View Logs"); break;
        case LEVEL_LOG_DOWNLOAD: lcd.print("Download Logs"); break;
        case LEVEL_LOG_CLEAR: lcd.print("Clear Logs"); break;
        case PumpSetting: lcd.print("Pump Setting"); break;
        case FanSetting: lcd.print("Fan Setting"); break;
        case VoltageControl: lcd.print("Voltage Control"); break;
        case CurrentControl: lcd.print("Current Control"); break;
        case LEVEL_CONFIG_PROFILES: lcd.print("Config Profiles"); break;
        case userA: lcd.print("User Profile"); break;
        case NewUser: lcd.print("New User"); break;
        case LEVEL_ADD_USER: lcd.print("Add User"); break;
        case LEVEL_chooseProfile: lcd.print("Choose Profile"); break;
        default: lcd.print("Menu"); break;
    }

    updateScrollPosition();

    // Display 3 visible items
    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);

            // Show cursor or edit indicator
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            }

            // Display item text and value
            if (currentMenu != nullptr) {
                lcd.print(currentMenu[itemIndex].text);
                if (currentMenu[itemIndex].isEditable) {
                    // Display current value for editable items
                    lcd.print(getCurrentValue(itemIndex));
                }
            }
        }
    }
}

void displaySetupMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Setup Menu");
    int menuSize = sizeof(setupMenu) / sizeof(setupMenu[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(setupMenu[itemIndex].text);
        }
    }
}

void displayManualSettingsMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Manual Settings");
    int menuSize = sizeof(manualSettingsMenu) / sizeof(manualSettingsMenu[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(manualSettingsMenu[itemIndex].text);
        }
    }
}

void displaySensorSettingsMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Sensor Settings");
    int menuSize = sizeof(sensorSettingsMenu) / sizeof(sensorSettingsMenu[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(sensorSettingsMenu[itemIndex].text);
        }
    }
}

void displayOutputSettingsMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Output Settings");
    int menuSize = sizeof(outputSettings) / sizeof(outputSettings[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(outputSettings[itemIndex].text);
        }
    }
}

void displayConfigProfilesMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Config Profiles");
    int menuSize = profileCount + 2;

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            if (itemIndex < profileCount) {
                lcd.print("User ");
                lcd.print(profiles[itemIndex].nameIndex);
                lcd.print(": ");
                lcd.print(profileTypes[profiles[itemIndex].typeIndex]);
            } else if (itemIndex == profileCount) {
                lcd.print("Create New User");
            } else {
                lcd.print("Back");
            }
        }
    }
}

void displayUserAMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("User ");
    lcd.print(profiles[currentProfileIndex].nameIndex);
    lcd.print(": ");
    lcd.print(profileTypes[profiles[currentProfileIndex].typeIndex]);
    int menuSize = sizeof(UserA) / sizeof(UserA[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            }
            lcd.print(UserA[itemIndex].text);
            if (itemIndex == 0) lcd.print(formatFloat(profiles[currentProfileIndex].pHMin, 1));
            else if (itemIndex == 1) lcd.print(formatFloat(profiles[currentProfileIndex].pHMax, 1));
            else if (itemIndex == 2) lcd.print(profiles[currentProfileIndex].pumpSpeed);
            else if (itemIndex == 3) lcd.print(profiles[currentProfileIndex].pumpTime);
            else if (itemIndex == 4) lcd.print(profiles[currentProfileIndex].FanSpeed);
            else if (itemIndex == 5) lcd.print(profiles[currentProfileIndex].FanTime);
            else if (itemIndex == 6) lcd.print(formatFloat(profiles[currentProfileIndex].Volt, 1));
            else if (itemIndex == 7) lcd.print(formatFloat(profiles[currentProfileIndex].Current, 1));
        }
    }
}

void displayNewUserMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("New User Settings");
    int menuSize = sizeof(newUser) / sizeof(newUser[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            }
            lcd.print(newUser[itemIndex].text);
            if (itemIndex == 0) lcd.print(formatFloat(tempProfile.pHMin, 1));
            else if (itemIndex == 1) lcd.print(formatFloat(tempProfile.pHMax, 1));
            else if (itemIndex == 2) lcd.print(tempProfile.pumpSpeed);
            else if (itemIndex == 3) lcd.print(tempProfile.pumpTime);
            else if (itemIndex == 4) lcd.print(tempProfile.FanSpeed);
            else if (itemIndex == 5) lcd.print(tempProfile.FanTime);
            else if (itemIndex == 6) lcd.print(formatFloat(tempProfile.Volt, 1));
            else if (itemIndex == 7) lcd.print(formatFloat(tempProfile.Current, 1));
        }
    }
}

void displayPumpSettingMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Pump Setting");
    int menuSize = sizeof(pumpSetting) / sizeof(pumpSetting[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Speed (%): "); lcd.print(pumpSpeedSetting);
                } else {
                    lcd.print(pumpSetting[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(pumpSetting[itemIndex].text);
                if (itemIndex == 0) lcd.print(pumpSpeedSetting);
            }
        }
    }
}

void displayFanSettingMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Fan Setting");
    int menuSize = sizeof(fanSetting) / sizeof(fanSetting[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Speed (%): "); lcd.print(fanSpeed);
                } else {
                    lcd.print(fanSetting[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(fanSetting[itemIndex].text);
                if (itemIndex == 0) lcd.print(fanSpeed);
            }
        }
    }
}

void displayVoltageControlMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Voltage Control");
    int menuSize = sizeof(voltControl) / sizeof(voltControl[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Volt (V): "); lcd.print(formatFloat(voltageSetting, 1));
                } else {
                    lcd.print(voltControl[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(voltControl[itemIndex].text);
                if (itemIndex == 0) lcd.print(formatFloat(voltageSetting, 1));
            }
        }
    }
}

void displayCurrentControlMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Current Control");
    int menuSize = sizeof(currentControl) / sizeof(currentControl[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Current (A): "); lcd.print(formatFloat(currentSetting, 1));
                } else {
                    lcd.print(currentControl[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(currentControl[itemIndex].text);
                if (itemIndex == 0) lcd.print(formatFloat(currentSetting, 1));
            }
        }
    }
}

void displayEditSensorMenu() {
    const MenuItem* currentMenu = nullptr;
    int menuSize = 0;
    String title = "";
    float minVal = 0.0, maxVal = 0.0;

    if (menuState.currentLevel == LEVEL_EDIT_pH) {
        currentMenu = pHsettings;
        menuSize = sizeof(pHsettings) / sizeof(pHsettings[0]);
        title = "pH Settings";
        minVal = pHMin;
        maxVal = pHMax;
    } else if (menuState.currentLevel == LEVEL_EDIT_Temp) {
        currentMenu = Tempsettings;
        menuSize = sizeof(Tempsettings) / sizeof(Tempsettings[0]);
        title = "Temperature Settings";
        minVal = tempMin;
        maxVal = tempMax;
    } else if (menuState.currentLevel == LEVEL_EDIT_Gas) {
        currentMenu = Gassettings;
        menuSize = sizeof(Gassettings) / sizeof(Gassettings[0]);
        title = "Gas Settings";
        minVal = gasMin;
        maxVal = gasMax;
    }

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print(title);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Set Min: "); lcd.print(formatFloat(minVal, 1));
                } else if (itemIndex == 1) {
                    lcd.print("Set Max: "); lcd.print(formatFloat(maxVal, 1));
                } else {
                    lcd.print(currentMenu[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(currentMenu[itemIndex].text);
                if (itemIndex == 0) lcd.print(formatFloat(minVal, 1));
                else if (itemIndex == 1) lcd.print(formatFloat(maxVal, 1));
            }
        }
    }
}

void displayAddUserMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Add New User");
    int menuSize = sizeof(addUser) / sizeof(addUser[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            }
            lcd.print(addUser[itemIndex].text);
            if (itemIndex == 0) {
                lcd.print("User ");
                lcd.print(userNameIndex);
            } else if (itemIndex == 1) {
                lcd.print(profileTypes[profileTypeIndex]);
            }
        }
    }
}

void displayStartMenu() {
    // SESUAI REFERENSI: LEVEL_START Menu
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Start Menu");
    int menuSize = sizeof(startMenu) / sizeof(startMenu[0]);

    // Auto-scroll logic sesuai referensi
    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    // Display 3 visible items
    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(startMenu[itemIndex].text);
        }
    }
}

void displayChooseProfileMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Choose Profile");
    int menuSize = profileCount + 1;

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            if (itemIndex < profileCount) {
                lcd.print("User ");
                lcd.print(profiles[itemIndex].nameIndex);
                lcd.print(": ");
                lcd.print(profileTypes[profiles[itemIndex].typeIndex]);
            } else {
                lcd.print("Back");
            }
        }
    }
}

// Implementasi fungsi rotary encoder
void IRAM_ATTR readEncoderISR() {
    rotaryEncoder.readEncoder_ISR();
}

void initializeProfiles() {
    profiles[0] = {4.0, 7.0, 70, 10, 60, 5, 5.0, 2.0, 1, 0}; // Current changed from 200mA to 2A
    for (int i = 1; i < 10; i++) {
        profiles[i] = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
    }
    tempProfile = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
}

void saveProfileToPreferences(int index) {
    if (index >= 0 && index < 10) {
        preferences.begin("electrowinning", false);
        String key = "profile" + String(index);
        preferences.putBytes(key.c_str(), &profiles[index], sizeof(UserProfile));
        preferences.end();
        Serial.print("Saved profile "); Serial.print(index); Serial.println(" to Preferences");
    }
}

void loadProfilesFromPreferences() {
    // Use the same namespace as main.cpp to avoid conflicts
    preferences.begin("electrowinning", true);
    profileCount = preferences.getInt("profileCount", 1);

    for (int i = 0; i < profileCount && i < 10; i++) {
        String key = "profile" + String(i);
        size_t len = preferences.getBytesLength(key.c_str());
        if (len == sizeof(UserProfile)) {
            preferences.getBytes(key.c_str(), &profiles[i], sizeof(UserProfile));
        } else {
            // Initialize default profile
            profiles[i].pHMin = 2.0;
            profiles[i].pHMax = 12.0;
            profiles[i].pumpSpeed = 50;
            profiles[i].pumpTime = 30;
            profiles[i].FanSpeed = 70;
            profiles[i].FanTime = 15;
            profiles[i].Volt = 12.0;
            profiles[i].Current = 5.0;
            profiles[i].nameIndex = i + 1;
            profiles[i].typeIndex = 0; // Gold
        }
    }

    if (profileCount == 0) {
        initializeProfiles();
        profileCount = 1;
    }
    preferences.end();
}

void saveProfileCountToPreferences() {
    preferences.begin("electrowinning", false);
    preferences.putInt("profileCount", profileCount);
    preferences.end();
}

void loadVoltageCurrentSettings() {
    preferences.begin("electrowinning", true);
    voltageSetting = preferences.getFloat("voltageSetting", 5.0); // Default 5V
    currentSetting = preferences.getFloat("currentSetting", 1.0); // Default 1A
    pumpSpeedSetting = preferences.getInt("pumpSpeedSetting", 50); // Default 50%
    fanSpeed = preferences.getInt("fanSpeed", 70); // Default 70%

    // Load sensor limits
    pHMin = preferences.getFloat("pHMin", 2.0); // Default 2.0
    pHMax = preferences.getFloat("pHMax", 12.0); // Default 12.0
    tempMin = preferences.getFloat("tempMin", 10.0); // Default 10°C
    tempMax = preferences.getFloat("tempMax", 60.0); // Default 60°C
    gasMin = preferences.getFloat("gasMin", 20.0); // Default 20%
    gasMax = preferences.getFloat("gasMax", 80.0); // Default 80%

    Serial.printf("Loaded sensor limits from preferences - pH: %.1f-%.1f, Temp: %.1f-%.1f°C, Gas: %.0f-%.0f%%\n",
                 pHMin, pHMax, tempMin, tempMax, gasMin, gasMax);

    // Load pH calibration data
    loadPHCalibration();

    // Also load display values for pump and fan (these are settings, not actual motor status)
    pumpSpeed = pumpSpeedSetting; // Use setting value for display
    // fanSpeed is already loaded above

    preferences.end();
    Serial.printf("Loaded settings - Voltage: %.1fV, Current: %.1fA, Pump: %d%%, Fan: %d%%\n",
                 voltageSetting, currentSetting, pumpSpeedSetting, fanSpeed);
    Serial.printf("Loaded sensor limits - pH: %.1f-%.1f, Temp: %.1f-%.1f°C, Gas: %.0f-%.0f%%\n",
                 pHMin, pHMax, tempMin, tempMax, gasMin, gasMax);
}

void saveVoltageCurrentSettings() {
    preferences.begin("electrowinning", false);
    preferences.putFloat("voltageSetting", voltageSetting);
    preferences.putFloat("currentSetting", currentSetting);
    preferences.putInt("pumpSpeedSetting", pumpSpeedSetting);
    preferences.putInt("fanSpeed", fanSpeed);
    preferences.end();
    Serial.printf("Saved settings - Voltage: %.1fV, Current: %.1fA, Pump: %d%%, Fan: %d%%\n",
                 voltageSetting, currentSetting, pumpSpeedSetting, fanSpeed);
}

void loadDataLoggerConfig() {
    preferences.begin("datalogger", true);

    DataLogger::LogConfig config;
    config.enabled = preferences.getBool("enabled", false); // Default disabled
    config.intervalMs = preferences.getULong("intervalMs", 5000); // Default 5 seconds
    config.maxEntries = preferences.getInt("maxEntries", 100); // Default 100 entries
    config.autoSave = preferences.getBool("autoSave", false); // Default disabled
    config.saveIntervalMs = preferences.getULong("saveIntervalMs", 300000); // Default 5 minutes

    preferences.end();

    // Apply loaded configuration to data logger
    dataLogger.setConfig(config);

    Serial.printf("Data Logger Config Loaded - Enabled: %s, Interval: %lums, Max: %d, AutoSave: %s\n",
                 config.enabled ? "YES" : "NO", config.intervalMs, config.maxEntries,
                 config.autoSave ? "YES" : "NO");
}



// Helper functions - SESUAI menu_hierarchy_navigation_guide.md
int getCurrentMenuSize() {
    switch (menuState.currentLevel) {
        case LEVEL_STATUS: return sizeof(statusMenu) / sizeof(statusMenu[0]);
        case LEVEL_START: return sizeof(startMenu) / sizeof(startMenu[0]);
        case LEVEL_SETUP: return sizeof(setupMenu) / sizeof(setupMenu[0]);
        case LEVEL_MANUAL_SETTINGS: return sizeof(manualSettingsMenu) / sizeof(manualSettingsMenu[0]);
        case LEVEL_SENSOR_SETTINGS: return sizeof(sensorSettingsMenu) / sizeof(sensorSettingsMenu[0]);
        case LEVEL_EDIT_pH: return sizeof(pHsettings) / sizeof(pHsettings[0]);
        case LEVEL_EDIT_Temp: return sizeof(Tempsettings) / sizeof(Tempsettings[0]);
        case LEVEL_EDIT_Gas: return sizeof(Gassettings) / sizeof(Gassettings[0]);
        case LEVEL_OUTPUT_SETTINGS: return sizeof(outputSettings) / sizeof(outputSettings[0]);
        case LEVEL_CALIBRATION: return sizeof(calibrationMenu) / sizeof(calibrationMenu[0]);
        case LEVEL_PH_CALIBRATION: return sizeof(pHCalibrationMenu) / sizeof(pHCalibrationMenu[0]);
        case LEVEL_PH_CAL_4: return sizeof(pHCal4Menu) / sizeof(pHCal4Menu[0]);
        case LEVEL_PH_CAL_7: return sizeof(pHCal7Menu) / sizeof(pHCal7Menu[0]);
        case LEVEL_PH_CAL_9: return sizeof(pHCal9Menu) / sizeof(pHCal9Menu[0]);
        case LEVEL_PH_CAL_VIEW: return sizeof(pHCalViewMenu) / sizeof(pHCalViewMenu[0]);
        case LEVEL_PH_CAL_RESET: return sizeof(pHCalResetMenu) / sizeof(pHCalResetMenu[0]);
        case LEVEL_DATA_LOGGING: return sizeof(dataLoggingMenu) / sizeof(dataLoggingMenu[0]);
        case LEVEL_LOG_CONFIG: return sizeof(logConfigMenu) / sizeof(logConfigMenu[0]);
        case LEVEL_LOG_VIEW: return sizeof(logViewMenu) / sizeof(logViewMenu[0]);
        case LEVEL_LOG_DOWNLOAD: return sizeof(logDownloadMenu) / sizeof(logDownloadMenu[0]);
        case LEVEL_LOG_CLEAR: return sizeof(logClearMenu) / sizeof(logClearMenu[0]);
        case PumpSetting: return sizeof(pumpSetting) / sizeof(pumpSetting[0]);
        case FanSetting: return sizeof(fanSetting) / sizeof(fanSetting[0]);
        case VoltageControl: return sizeof(voltControl) / sizeof(voltControl[0]);
        case CurrentControl: return sizeof(currentControl) / sizeof(currentControl[0]);
        case LEVEL_CONFIG_PROFILES: return profileCount + 2; // profiles + create new + back
        case LEVEL_chooseProfile: return profileCount + 1; // profiles + back
        case userA: return sizeof(UserA) / sizeof(UserA[0]);
        case NewUser: return sizeof(newUser) / sizeof(newUser[0]);
        case LEVEL_ADD_USER: return sizeof(addUser) / sizeof(addUser[0]);
        // Handle individual setting levels - these should not navigate to separate menus
        case PumpSpeed:
        case PumpTime:
        case FanSpeed:
        case FanTime:
        case voltSet:
        case curSet:
        case SetMinpH:
        case SetMaxpH:
        case SetMinTemp:
        case SetMaxTemp:
        case SetMinGas:
        case SetMaxGas:
            return 1; // These are individual settings, not menus
        default: return 1;
    }
}

const MenuItem* getCurrentMenu() {
    switch (menuState.currentLevel) {
        case LEVEL_STATUS: return statusMenu;
        case LEVEL_START: return startMenu;
        case LEVEL_SETUP: return setupMenu;
        case LEVEL_MANUAL_SETTINGS: return manualSettingsMenu;
        case LEVEL_SENSOR_SETTINGS: return sensorSettingsMenu;
        case LEVEL_EDIT_pH: return pHsettings;
        case LEVEL_EDIT_Temp: return Tempsettings;
        case LEVEL_EDIT_Gas: return Gassettings;
        case LEVEL_OUTPUT_SETTINGS: return outputSettings;
        case LEVEL_CALIBRATION: return calibrationMenu;
        case LEVEL_PH_CALIBRATION: return pHCalibrationMenu;
        case LEVEL_PH_CAL_4: return pHCal4Menu;
        case LEVEL_PH_CAL_7: return pHCal7Menu;
        case LEVEL_PH_CAL_9: return pHCal9Menu;
        case LEVEL_PH_CAL_VIEW: return pHCalViewMenu;
        case LEVEL_PH_CAL_RESET: return pHCalResetMenu;
        case LEVEL_DATA_LOGGING: return dataLoggingMenu;
        case LEVEL_LOG_CONFIG: return logConfigMenu;
        case LEVEL_LOG_VIEW: return logViewMenu;
        case LEVEL_LOG_DOWNLOAD: return logDownloadMenu;
        case LEVEL_LOG_CLEAR: return logClearMenu;
        case PumpSetting: return pumpSetting;
        case FanSetting: return fanSetting;
        case VoltageControl: return voltControl;
        case CurrentControl: return currentControl;
        case userA: return UserA;
        case NewUser: return newUser;
        case LEVEL_ADD_USER: return addUser;
        case LEVEL_CONFIG_PROFILES: return configProfiles;
        case LEVEL_chooseProfile: return chooseProfile;
        // Handle individual setting levels - these should not have separate menus
        case PumpSpeed:
        case PumpTime:
        case FanSpeed:
        case FanTime:
        case voltSet:
        case curSet:
        case SetMinpH:
        case SetMaxpH:
        case SetMinTemp:
        case SetMaxTemp:
        case SetMinGas:
        case SetMaxGas:
            return nullptr; // These are individual settings, not menus
        default: return nullptr;
    }
}

// Boundary Management - SESUAI menu_hierarchy_navigation_guide.md Section 4
void updateEncoderBoundaries() {
    if (menuState.isEditing) {
        // Edit mode: wide range for value editing
        rotaryEncoder.setBoundaries(-999999, 999999, false);
    } else {
        // Navigation mode: limit to menu size
        int menuSize = getCurrentMenuSize();
        rotaryEncoder.setBoundaries(0, menuSize - 1, false);
        currentMenuSize = menuSize;
    }
}

// Navigation Helper Functions - SESUAI menu_hierarchy_navigation_guide.md Section 2
void navigateToLevel(MenuLevel newLevel) {
    menuState.currentLevel = newLevel;
    menuState.currentItem = 0;
    menuState.scrollPosition = 0;
    menuState.isEditing = false;
    updateEncoderBoundaries();
    displayNeedsUpdate = true;

    // Sync back to electrowinningState
    electrowinningState.getMenuState() = menuState;
    Serial.printf("Navigate to level %d\n", newLevel);
}

MenuLevel determineBackTarget() {
    switch (menuState.currentLevel) {
        case LEVEL_START: return LEVEL_STATUS;
        case LEVEL_SETUP: return LEVEL_STATUS;
        case LEVEL_MANUAL_SETTINGS: return LEVEL_START;
        case LEVEL_SENSOR_SETTINGS: return LEVEL_MANUAL_SETTINGS;
        case LEVEL_OUTPUT_SETTINGS: return LEVEL_MANUAL_SETTINGS;
        case LEVEL_EDIT_pH:
        case LEVEL_EDIT_Temp:
        case LEVEL_EDIT_Gas: return LEVEL_SENSOR_SETTINGS;
        case PumpSetting:
        case FanSetting:
        case VoltageControl:
        case CurrentControl: return LEVEL_OUTPUT_SETTINGS;
        case LEVEL_CONFIG_PROFILES: return LEVEL_SETUP;
        case LEVEL_CALIBRATION: return LEVEL_SETUP;
        case LEVEL_DATA_LOGGING: return LEVEL_SETUP;
        case LEVEL_LOG_CONFIG:
        case LEVEL_LOG_VIEW:
        case LEVEL_LOG_DOWNLOAD:
        case LEVEL_LOG_CLEAR: return LEVEL_DATA_LOGGING;
        case LEVEL_PH_CALIBRATION: return LEVEL_CALIBRATION;
        case LEVEL_PH_CAL_4:
        case LEVEL_PH_CAL_7:
        case LEVEL_PH_CAL_9:
        case LEVEL_PH_CAL_VIEW:
        case LEVEL_PH_CAL_RESET: return LEVEL_PH_CALIBRATION;
        case LEVEL_chooseProfile: return LEVEL_START;
        case userA:
        case NewUser: return LEVEL_CONFIG_PROFILES;
        case LEVEL_ADD_USER: return NewUser;
        default: return LEVEL_STATUS;
    }
}

void navigateBack() {
    MenuLevel targetLevel = determineBackTarget();
    navigateToLevel(targetLevel);
}

void toggleEditMode() {
    Serial.printf("toggleEditMode CALLED: Before - isEditing = %s, currentLevel = %d, currentItem = %d\n",
                 menuState.isEditing ? "true" : "false", menuState.currentLevel, menuState.currentItem);

    // Debug enum values
    Serial.printf("Enum values: LEVEL_EDIT_pH=%d, LEVEL_EDIT_Temp=%d, LEVEL_EDIT_Gas=%d\n",
                 LEVEL_EDIT_pH, LEVEL_EDIT_Temp, LEVEL_EDIT_Gas);

    menuState.isEditing = !menuState.isEditing;

    Serial.printf("toggleEditMode: After - isEditing = %s, currentLevel = %d, currentItem = %d\n",
                 menuState.isEditing ? "true" : "false", menuState.currentLevel, menuState.currentItem);

    // Save values when exiting edit mode
    if (!menuState.isEditing) {
        // Save voltage/current settings to electrowinning state and preferences
        if (menuState.currentLevel == VoltageControl) {
            electrowinningState.setTargetVoltage(voltageSetting);
            // Save to preferences for persistence
            preferences.begin("electrowinning", false);
            preferences.putFloat("voltageSetting", voltageSetting);
            preferences.end();
            Serial.printf("Voltage saved: %.1fV\n", voltageSetting);
        } else if (menuState.currentLevel == CurrentControl) {
            electrowinningState.setTargetCurrent(currentSetting); // Already in Amperes
            // Save to preferences for persistence
            preferences.begin("electrowinning", false);
            preferences.putFloat("currentSetting", currentSetting);
            preferences.end();
            Serial.printf("Current saved: %.1fA\n", currentSetting);
        } else if (menuState.currentLevel == PumpSetting) {
            // Only save setting, don't apply to hardware yet
            preferences.begin("electrowinning", false);
            preferences.putInt("pumpSpeedSetting", pumpSpeedSetting);
            preferences.end();
            Serial.printf("Pump speed setting saved: %d%% (will apply when process starts)\n", pumpSpeedSetting);
        } else if (menuState.currentLevel == FanSetting) {
            // Only save setting, don't apply to hardware yet
            preferences.begin("electrowinning", false);
            preferences.putInt("fanSpeed", fanSpeed);
            preferences.end();
            Serial.printf("Fan speed setting saved: %d%% (will apply when process starts)\n", fanSpeed);
        } else if (menuState.currentLevel == LEVEL_EDIT_pH) {
            // Save pH limits to electrowinning state and preferences
            ProcessLimits limits;
            limits.pHMin = pHMin;
            limits.pHMax = pHMax;
            limits.tempMin = tempMin;
            limits.tempMax = tempMax;
            limits.gasMin = gasMin;
            limits.gasMax = gasMax;
            electrowinningState.setLimits(limits);
            preferences.begin("electrowinning", false);
            preferences.putFloat("pHMin", pHMin);
            preferences.putFloat("pHMax", pHMax);
            preferences.end();
            Serial.printf("pH limits saved - Min: %.1f, Max: %.1f\n", pHMin, pHMax);
        } else if (menuState.currentLevel == LEVEL_EDIT_Temp) {
            // Save temperature limits to electrowinning state and preferences
            ProcessLimits limits;
            limits.pHMin = pHMin;
            limits.pHMax = pHMax;
            limits.tempMin = tempMin;
            limits.tempMax = tempMax;
            limits.gasMin = gasMin;
            limits.gasMax = gasMax;
            electrowinningState.setLimits(limits);
            preferences.begin("electrowinning", false);
            preferences.putFloat("tempMin", tempMin);
            preferences.putFloat("tempMax", tempMax);
            preferences.end();
            Serial.printf("Temperature limits saved - Min: %.1f°C, Max: %.1f°C\n", tempMin, tempMax);
        } else if (menuState.currentLevel == LEVEL_EDIT_Gas) {
            // Save gas limits to electrowinning state and preferences
            ProcessLimits limits;
            limits.pHMin = pHMin;
            limits.pHMax = pHMax;
            limits.tempMin = tempMin;
            limits.tempMax = tempMax;
            limits.gasMin = gasMin;
            limits.gasMax = gasMax;
            electrowinningState.setLimits(limits);
            preferences.begin("electrowinning", false);
            preferences.putFloat("gasMin", gasMin);
            preferences.putFloat("gasMax", gasMax);
            preferences.end();
            Serial.printf("Gas limits saved - Min: %.0f%%, Max: %.0f%%\n", gasMin, gasMax);
        } else if (menuState.currentLevel == LEVEL_LOG_CONFIG) {
            // Save data logging configuration
            DataLogger::LogConfig config = dataLogger.getConfig();
            preferences.begin("datalogger", false);
            preferences.putBool("enabled", config.enabled);
            preferences.putULong("intervalMs", config.intervalMs);
            preferences.putInt("maxEntries", config.maxEntries);
            preferences.putBool("autoSave", config.autoSave);
            preferences.putULong("saveIntervalMs", config.saveIntervalMs);
            preferences.end();
            Serial.printf("Data logger config saved - Enabled: %s, Interval: %lums\n",
                         config.enabled ? "YES" : "NO", config.intervalMs);
        }

        // Save profile values when editing user profiles
        if (menuState.currentLevel == userA && currentProfileIndex >= 0) {
            saveProfileToPreferences(currentProfileIndex);
            Serial.println("Profile saved to preferences");
        } else if (menuState.currentLevel == NewUser) {
            Serial.println("Temporary profile updated");
        }
    }
    
    updateEncoderBoundaries();

    // Reset encoder position appropriately
    if (menuState.isEditing) {
        // Entering edit mode: reset encoder to 0 for value editing
        rotaryEncoder.setEncoderValue(0);
        Serial.println("Entering edit mode - encoder reset to 0");
    } else {
        // Exiting edit mode: reset encoder to current menu item
        rotaryEncoder.setEncoderValue(menuState.currentItem);
        Serial.printf("Exiting edit mode - encoder reset to menu item: %d\n", menuState.currentItem);
    }

    displayNeedsUpdate = true;
    electrowinningState.getMenuState() = menuState;
    Serial.printf("Edit mode %s\n", menuState.isEditing ? "ON" : "OFF");
}

// Scrolling Logic - SESUAI menu_hierarchy_navigation_guide.md Section 2
void updateScrollPosition() {
    // Scroll up if selection above visible area
    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    }
    // Scroll down if selection below visible area
    else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }
}

// Value Display Helper - SESUAI menu_hierarchy_navigation_guide.md Section 3
String getCurrentValue(int itemIndex) {
    const MenuItem* currentMenu = getCurrentMenu();
    if (currentMenu == nullptr) return "";

    // Use current menu level and item index to determine value
    switch (menuState.currentLevel) {
        case PumpSetting:
            if (itemIndex == 0) return String(pumpSpeedSetting);
            break;
        case FanSetting:
            if (itemIndex == 0) return String(fanSpeed);
            break;
        case VoltageControl:
            if (itemIndex == 0) return String(voltageSetting, 1);
            break;
        case CurrentControl:
            if (itemIndex == 0) return String(currentSetting, 1);
            break;
        case LEVEL_EDIT_pH:
            if (itemIndex == 0) return String(pHMin, 1);
            if (itemIndex == 1) return String(pHMax, 1);
            break;
        case LEVEL_EDIT_Temp:
            if (itemIndex == 0) return String(tempMin, 1);
            if (itemIndex == 1) return String(tempMax, 1);
            break;
        case LEVEL_EDIT_Gas:
            if (itemIndex == 0) {
                Serial.printf("Display Gas Min: gasMin=%.1f\n", gasMin);
                return String(gasMin, 0) + "%"; // Tampilkan sebagai persentase tanpa desimal
            }
            if (itemIndex == 1) {
                Serial.printf("Display Gas Max: gasMax=%.1f\n", gasMax);
                return String(gasMax, 0) + "%"; // Tampilkan sebagai persentase tanpa desimal
            }
            break;
        case userA:
            if (currentProfileIndex >= 0) {
                if (itemIndex == 0) return String(profiles[currentProfileIndex].pHMin, 1);
                if (itemIndex == 1) return String(profiles[currentProfileIndex].pHMax, 1);
                if (itemIndex == 2) return String(profiles[currentProfileIndex].pumpSpeed);
                if (itemIndex == 3) return String(profiles[currentProfileIndex].pumpTime);
                if (itemIndex == 4) return String(profiles[currentProfileIndex].FanSpeed);
                if (itemIndex == 5) return String(profiles[currentProfileIndex].FanTime);
                if (itemIndex == 6) return String(profiles[currentProfileIndex].Volt, 1);
                if (itemIndex == 7) return String(profiles[currentProfileIndex].Current, 1);
            }
            break;
        case NewUser:
            if (itemIndex == 0) return String(tempProfile.pHMin, 1);
            if (itemIndex == 1) return String(tempProfile.pHMax, 1);
            if (itemIndex == 2) return String(tempProfile.pumpSpeed);
            if (itemIndex == 3) return String(tempProfile.pumpTime);
            if (itemIndex == 4) return String(tempProfile.FanSpeed);
            if (itemIndex == 5) return String(tempProfile.FanTime);
            if (itemIndex == 6) return String(tempProfile.Volt, 1);
            if (itemIndex == 7) return String(tempProfile.Current, 1);
            break;
        case LEVEL_ADD_USER:
            if (itemIndex == 0) return String(userNameIndex);
            if (itemIndex == 1) return String(profileTypeIndex);
            break;
        case LEVEL_LOG_CONFIG:
            {
                DataLogger::LogConfig config = dataLogger.getConfig();
                if (itemIndex == 0) return config.enabled ? "ON" : "OFF";
                if (itemIndex == 1) return String(config.intervalMs / 1000.0, 1) + "s";
                if (itemIndex == 2) return String(config.maxEntries);
                if (itemIndex == 3) return config.autoSave ? "ON" : "OFF";
                if (itemIndex == 4) return String(config.saveIntervalMs / 60000.0, 1) + "min";
            }
            break;
        default:
            break;
    }
    return "";
}

// Click Processing - SESUAI menu_hierarchy_navigation_guide.md Section 3
void rotary_onButtonClick() {
    // Debounce protection
    static unsigned long lastTimePressed = 0;
    if (millis() - lastTimePressed < 500) return;
    lastTimePressed = millis();

    // Sync encoder position only if not in edit mode
    int encoderPos = rotaryEncoder.readEncoder();
    if (!menuState.isEditing) {
        menuState.currentItem = encoderPos;
    }
    Serial.printf("CLICK: L%d I%d Edit:%s\n", menuState.currentLevel, menuState.currentItem, menuState.isEditing ? "ON" : "OFF");

    // Handle calibration button press
    if (calibrationState.isCalibrating) {
        if (calibrationState.sampleCount >= 10) {
            // Enough samples, finish calibration
            finishPHCalibration();
            return;
        } else {
            // Not enough samples, ignore button press
            Serial.println("Not enough samples for calibration (minimum 10)");
            return;
        }
    }

    // Get current menu context
    const MenuItem* currentMenu = getCurrentMenu();
    int menuSize = getCurrentMenuSize();

    // Handle special case for LEVEL_STATUS - SESUAI refrensicodedisplaycpp.md
    if (menuState.currentLevel == LEVEL_STATUS) {
        if (menuState.currentItem == 0) {
            // Start/Stop button with debouncing
            static unsigned long lastStartStopPress = 0;
            if (millis() - lastStartStopPress < 2000) {
                Serial.println("Start/Stop button debounced - ignoring press");
                return;
            }
            lastStartStopPress = millis();
            if (isRunning) {
                // Stop the process properly - this will set processStatus.isRunning = false
                electrowinningState.stopProcess();

                // Stop pump and fan when process stops
                electrowinningState.enablePump(false);
                electrowinningState.enableFan(false);

                Serial.println("Stop button pressed - Process and motors stopping...");

                // CRITICAL: Force multiple state updates to ensure synchronization
                for (int i = 0; i < 5; i++) {
                    electrowinningState.update();
                    delay(50); // Allow state to propagate
                }

                // Update legacy variables immediately to sync isRunning
                updateLegacyVariables();

                // Verify the stop was successful
                bool currentRunningState = electrowinningState.getProcessStatus().isRunning;
                Serial.printf("STOP VERIFICATION - electrowinningState.isRunning: %s\n", currentRunningState ? "true" : "false");
                Serial.printf("STOP VERIFICATION - global isRunning: %s\n", isRunning ? "true" : "false");

                // Show stop confirmation
                lcd.clear();
                lcd.setCursor(0, 0);
                lcd.print("Process Stopped");
                lcd.setCursor(0, 1);
                lcd.print("Motors OFF");
                delay(1000);

                // Force display update immediately
                displayNeedsUpdate = true;
                Serial.println("Stop process completed - Display will update");
            } else {
                navigateToLevel(LEVEL_START);
            }
        } else if (menuState.currentItem == 1) {
            // Setup button
            navigateToLevel(LEVEL_SETUP);
        }
        displayNeedsUpdate = true;
        return;
    }

    // Handle special cases for dynamic menus
    if (menuState.currentLevel == LEVEL_CONFIG_PROFILES) {
        if (menuState.currentItem < profileCount) {
            currentProfileIndex = menuState.currentItem;
            navigateToLevel(userA);
        } else if (menuState.currentItem == profileCount) {
            currentProfileIndex = -1;
            tempProfile = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
            navigateToLevel(NewUser);
        } else {
            navigateBack();
        }
        return;
    }

    if (menuState.currentLevel == LEVEL_chooseProfile) {
        if (menuState.currentItem < profileCount) {
            currentProfileIndex = menuState.currentItem;
            // Auto mode: directly start the selected profile

            // Apply profile settings to electrowinning state
            electrowinningState.setTargetVoltage(profiles[currentProfileIndex].Volt);
            electrowinningState.setTargetCurrent(profiles[currentProfileIndex].Current);

            // Apply pump and fan settings from current configuration
            electrowinningState.setPumpSpeed(pumpSpeedSetting);
            electrowinningState.setFanSpeed(fanSpeed);
            electrowinningState.enablePump(true);
            electrowinningState.enableFan(true);

            // Start the process in auto mode
            electrowinningState.startProcess(false, currentProfileIndex);

            isRunning = true;
            Serial.printf("Auto Mode: Starting Profile %d - Pump: %d%%, Fan: %d%%\n",
                         currentProfileIndex, pumpSpeedSetting, fanSpeed);
            lcd.clear();
            lcd.setCursor(0, 0);
            lcd.print("Auto Mode Started");
            lcd.setCursor(0, 1);
            lcd.print("Profile: User ");
            lcd.print(profiles[currentProfileIndex].nameIndex);
            delay(2000);
            navigateToLevel(LEVEL_STATUS);
        } else {
            navigateBack();
        }
        return;
    }

    // Handle normal menu navigation
    // Process action based on menu item - SESUAI menu_hierarchy_navigation_guide.md
    if (currentMenu != nullptr && menuState.currentItem < menuSize) {
        switch(currentMenu[menuState.currentItem].action) {
            case ACTION_NONE:
                if (currentMenu[menuState.currentItem].isEditable) {
                    Serial.printf("DEBUG: Item is editable, calling toggleEditMode() - Level: %d, Item: %d\n",
                                 menuState.currentLevel, menuState.currentItem);
                    toggleEditMode();
                } else {
                    // Handle special calibration actions
                    if (menuState.currentLevel == LEVEL_PH_CAL_4 && menuState.currentItem == 0) {
                        // Start pH 4.00 calibration
                        Serial.println("Starting pH 4.00 calibration");
                        startPHCalibration(1);
                    } else if (menuState.currentLevel == LEVEL_PH_CAL_7 && menuState.currentItem == 0) {
                        // Start pH 6.86 calibration
                        Serial.println("Starting pH 6.86 calibration");
                        startPHCalibration(2);
                    } else if (menuState.currentLevel == LEVEL_PH_CAL_9 && menuState.currentItem == 0) {
                        // Start pH 9.18 calibration
                        Serial.println("Starting pH 9.18 calibration");
                        startPHCalibration(3);
                    } else if (menuState.currentLevel == LEVEL_PH_CAL_RESET && menuState.currentItem == 0) {
                        // Reset calibration
                        Serial.println("Resetting pH calibration");
                        resetPHCalibration();
                    } else {
                        Serial.printf("DEBUG: Item not editable, navigating to level %d\n",
                                     currentMenu[menuState.currentItem].nextLevel);
                        navigateToLevel(currentMenu[menuState.currentItem].nextLevel);
                    }
                }
                break;

            case ACTION_BACK:
                navigateBack();
                break;

            case ACTION_START:
                {
                    // Start process implementation - Manual Mode
                    Serial.printf("ACTION_START: Setting voltage %.1fV and current %.1fA\n", voltageSetting, currentSetting);

                    // Apply current voltage and current settings to XY6020
                    electrowinningState.setTargetVoltage(voltageSetting);
                    electrowinningState.setTargetCurrent(currentSetting); // Already in Amperes

                    // Apply pump and fan settings when process starts
                    electrowinningState.setPumpSpeed(pumpSpeedSetting);
                    electrowinningState.setFanSpeed(fanSpeed);
                    electrowinningState.enablePump(true);
                    electrowinningState.enableFan(true);

                    Serial.printf("Applied motor settings - Pump: %d%%, Fan: %d%%\n", pumpSpeedSetting, fanSpeed);

                    // Small delay to ensure settings are applied
                    delay(100);

                    // Start the process in manual mode
                    electrowinningState.startProcess(true, -1);

                    // CRITICAL: Force multiple state updates to ensure synchronization
                    for (int i = 0; i < 5; i++) {
                        electrowinningState.update();
                        delay(50); // Allow state to propagate
                    }

                    // Update legacy variables immediately to sync isRunning
                    updateLegacyVariables();

                    // Verify the start was successful
                    bool currentRunningState = electrowinningState.getProcessStatus().isRunning;
                    Serial.printf("START VERIFICATION - electrowinningState.isRunning: %s\n", currentRunningState ? "true" : "false");
                    Serial.printf("START VERIFICATION - global isRunning: %s\n", isRunning ? "true" : "false");

                    Serial.printf("Manual Process Started - Voltage: %.1fV, Current: %.1fA\n",
                                 voltageSetting, currentSetting);
                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Manual Process");
                    lcd.setCursor(0, 1);
                    lcd.print("Started");
                    lcd.setCursor(0, 2);
                    lcd.printf("V:%.1f C:%.1fA", voltageSetting, currentSetting);
                    delay(2000);
                    navigateToLevel(LEVEL_STATUS);
                }
                break;

            case ACTION_SAVE:
                // Save profile implementation
                if (menuState.currentLevel == LEVEL_ADD_USER) {
                    if (profileCount < 10) {
                        profiles[profileCount] = tempProfile;
                        profiles[profileCount].nameIndex = userNameIndex;
                        profiles[profileCount].typeIndex = profileTypeIndex;
                        saveProfileToPreferences(profileCount);
                        profileCount++;
                        saveProfileCountToPreferences();
                        Serial.println("New Profile Saved");
                        navigateToLevel(LEVEL_CONFIG_PROFILES);
                    }
                }
                break;

            case ACTION_RESET:
                // Reset implementation
                if (menuState.currentLevel == LEVEL_SETUP) {
                    // Reset all profiles to default
                    profileCount = 1;
                    profiles[0] = {4.0, 7.0, 70, 10, 60, 5, 5.0, 2.0, 1, 0}; // Current in Amperes
                    saveProfileToPreferences(0);
                    saveProfileCountToPreferences();
                    Serial.println("Reset to Default");
                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Reset Complete");
                    delay(2000);
                    displayNeedsUpdate = true;
                }
                break;

            case ACTION_TEST_MOTORS:
                // Test Motors implementation
                if (menuState.currentLevel == LEVEL_SETUP) {
                    Serial.println("Starting Motor Test from Menu");
                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Testing Motors...");
                    lcd.setCursor(0, 1);
                    lcd.print("Check Serial Monitor");

                    // Call the test function from main.cpp
                    testMotorControl();

                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Motor Test");
                    lcd.setCursor(0, 1);
                    lcd.print("Completed!");
                    delay(2000);
                    displayNeedsUpdate = true;
                }
                break;

            case ACTION_UPDATE:
            case ACTION_DELETE:
                // Additional actions can be implemented here
                break;

            // Data Logging Actions
            case ACTION_LOG_TOGGLE:
                {
                    if (menuState.currentLevel == LEVEL_LOG_CONFIG) {
                        if (menuState.currentItem == 0) { // Enable/Disable
                            DataLogger::LogConfig config = dataLogger.getConfig();
                            config.enabled = !config.enabled;
                            dataLogger.setConfig(config);

                            lcd.clear();
                            lcd.setCursor(0, 0);
                            lcd.print("Data Logging:");
                            lcd.setCursor(0, 1);
                            lcd.print(config.enabled ? "ENABLED" : "DISABLED");
                            delay(1500);
                            displayNeedsUpdate = true;
                        } else if (menuState.currentItem == 3) { // Auto Save
                            DataLogger::LogConfig config = dataLogger.getConfig();
                            config.autoSave = !config.autoSave;
                            dataLogger.setConfig(config);

                            lcd.clear();
                            lcd.setCursor(0, 0);
                            lcd.print("Auto Save:");
                            lcd.setCursor(0, 1);
                            lcd.print(config.autoSave ? "ENABLED" : "DISABLED");
                            delay(1500);
                            displayNeedsUpdate = true;
                        }
                    }
                }
                break;

            case ACTION_LOG_VIEW:
                {
                    lcd.clear();
                    lcd.setCursor(0, 0);

                    if (menuState.currentItem == 0) { // Recent 10
                        auto logs = dataLogger.getRecentLogs(10);
                        lcd.print("Recent 10 Logs:");
                        lcd.setCursor(0, 1);
                        lcd.printf("Count: %d", logs.size());
                    } else if (menuState.currentItem == 1) { // Recent 50
                        auto logs = dataLogger.getRecentLogs(50);
                        lcd.print("Recent 50 Logs:");
                        lcd.setCursor(0, 1);
                        lcd.printf("Count: %d", logs.size());
                    } else if (menuState.currentItem == 2) { // All Entries
                        lcd.print("All Log Entries:");
                        lcd.setCursor(0, 1);
                        lcd.printf("Total: %d", dataLogger.getEntryCount());
                    } else if (menuState.currentItem == 3) { // Log Summary
                        lcd.print("Log Summary:");
                        lcd.setCursor(0, 1);
                        lcd.printf("Entries: %d", dataLogger.getEntryCount());
                        lcd.setCursor(0, 2);
                        lcd.printf("Status: %s", dataLogger.isEnabled() ? "ON" : "OFF");
                    }

                    delay(3000);
                    displayNeedsUpdate = true;
                }
                break;

            case ACTION_LOG_SAVE_CSV:
                {
                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Saving to CSV...");

                    bool success = dataLogger.saveToCSV();

                    lcd.setCursor(0, 1);
                    if (success) {
                        lcd.print("CSV Saved!");
                    } else {
                        lcd.print("Save Failed!");
                    }
                    delay(2000);
                    displayNeedsUpdate = true;
                }
                break;

            case ACTION_LOG_SAVE_JSON:
                {
                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Saving to JSON...");

                    bool success = dataLogger.saveToJSON();

                    lcd.setCursor(0, 1);
                    if (success) {
                        lcd.print("JSON Saved!");
                    } else {
                        lcd.print("Save Failed!");
                    }
                    delay(2000);
                    displayNeedsUpdate = true;
                }
                break;

            case ACTION_LOG_CLEAR:
                {
                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Clearing logs...");

                    dataLogger.clearBuffer();

                    lcd.setCursor(0, 1);
                    lcd.print("Logs Cleared!");
                    delay(2000);
                    navigateToLevel(LEVEL_DATA_LOGGING);
                }
                break;

            case ACTION_LOG_CONFIG:
                {
                    if (menuState.currentLevel == LEVEL_LOG_DOWNLOAD && menuState.currentItem == 2) {
                        // Auto Save Now
                        lcd.clear();
                        lcd.setCursor(0, 0);
                        lcd.print("Auto Saving...");

                        bool success = dataLogger.saveToFile();

                        lcd.setCursor(0, 1);
                        if (success) {
                            lcd.print("Auto Save Done!");
                        } else {
                            lcd.print("Auto Save Failed!");
                        }
                        delay(2000);
                        displayNeedsUpdate = true;
                    }
                }
                break;
        }
    }
}

// Rotation Processing - SESUAI menu_hierarchy_navigation_guide.md Section 2
void rotary_loop() {
    if (rotaryEncoder.encoderChanged()) {
        int currentPosition = rotaryEncoder.readEncoder();
        int delta = currentPosition - previousEncoderPosition;
        previousEncoderPosition = currentPosition;

        Serial.printf("Encoder changed! Position: %d, Delta: %d, isEditing: %s, Level: %d, Item: %d\n",
                     currentPosition, delta, menuState.isEditing ? "true" : "false",
                     menuState.currentLevel, menuState.currentItem);

        UserProfile* targetProfile = (menuState.currentLevel == userA && currentProfileIndex >= 0) ? &profiles[currentProfileIndex] : &tempProfile;

        if (menuState.isEditing) {
            // Debug output untuk troubleshooting
            Serial.printf("EDIT MODE ACTIVE - Level: %d, Item: %d, Delta: %d\n",
                         menuState.currentLevel, menuState.currentItem, delta);
            Serial.printf("Checking edit conditions...\n");

            float increment = (delta > 0) ? 0.1 : -0.1;
            int intIncrement = (delta > 0) ? 1 : -1;
            if (menuState.currentLevel == LEVEL_EDIT_pH) {
                Serial.printf("pH EDIT DETECTED - Level: %d, Item: %d\n", menuState.currentLevel, menuState.currentItem);
                if (menuState.currentItem == 0) {
                    Serial.printf("Editing pH Min: before=%.1f, increment=%.1f\n", pHMin, increment);
                    pHMin += increment;
                    pHMin = constrain(pHMin, 0.0, 14.0);

                    // IMMEDIATELY update electrowinning state to prevent override
                    ProcessLimits limits = electrowinningState.getLimits();
                    limits.pHMin = pHMin;
                    electrowinningState.setLimits(limits);
                    Serial.printf("pH Min updated in electrowinning state: %.1f\n", pHMin);

                    Serial.printf("pH Min: %.1f (after edit)\n", pHMin);
                    displayNeedsUpdate = true; // Force display update
                } else if (menuState.currentItem == 1) {
                    Serial.printf("Editing pH Max: before=%.1f, increment=%.1f\n", pHMax, increment);
                    pHMax += increment;
                    pHMax = constrain(pHMax, 0.0, 14.0);

                    // IMMEDIATELY update electrowinning state to prevent override
                    ProcessLimits limits = electrowinningState.getLimits();
                    limits.pHMax = pHMax;
                    electrowinningState.setLimits(limits);
                    Serial.printf("pH Max updated in electrowinning state: %.1f\n", pHMax);

                    Serial.printf("pH Max: %.1f (after edit)\n", pHMax);
                    displayNeedsUpdate = true; // Force display update
                } else {
                    Serial.printf("pH edit: Invalid item index %d\n", menuState.currentItem);
                }
            } else if (menuState.currentLevel == LEVEL_EDIT_Temp) {
                Serial.printf("TEMP EDIT DETECTED - Level: %d, Item: %d\n", menuState.currentLevel, menuState.currentItem);
                if (menuState.currentItem == 0) {
                    Serial.printf("Editing Temp Min: before=%.1f, increment=%.1f\n", tempMin, increment);
                    tempMin += increment;
                    tempMin = constrain(tempMin, -10.0, 100.0);

                    // IMMEDIATELY update electrowinning state to prevent override
                    ProcessLimits limits = electrowinningState.getLimits();
                    limits.tempMin = tempMin;
                    electrowinningState.setLimits(limits);
                    Serial.printf("Temp Min updated in electrowinning state: %.1f\n", tempMin);

                    Serial.printf("Temp Min: %.1f (after edit)\n", tempMin);
                    displayNeedsUpdate = true; // Force display update
                } else if (menuState.currentItem == 1) {
                    Serial.printf("Editing Temp Max: before=%.1f, increment=%.1f\n", tempMax, increment);
                    tempMax += increment;
                    tempMax = constrain(tempMax, -10.0, 100.0);

                    // IMMEDIATELY update electrowinning state to prevent override
                    ProcessLimits limits = electrowinningState.getLimits();
                    limits.tempMax = tempMax;
                    electrowinningState.setLimits(limits);
                    Serial.printf("Temp Max updated in electrowinning state: %.1f\n", tempMax);

                    Serial.printf("Temp Max: %.1f (after edit)\n", tempMax);
                    displayNeedsUpdate = true; // Force display update
                } else {
                    Serial.printf("Temp edit: Invalid item index %d\n", menuState.currentItem);
                }
            } else if (menuState.currentLevel == LEVEL_EDIT_Gas) {
                Serial.printf("GAS EDIT DETECTED - Level: %d, Item: %d\n", menuState.currentLevel, menuState.currentItem);
                if (menuState.currentItem == 0) {
                    Serial.printf("Editing Gas Min: before=%.1f, increment=%.1f\n", gasMin, increment);
                    float oldValue = gasMin;
                    gasMin += increment;
                    gasMin = constrain(gasMin, 0.0, 100.0); // 0-100% untuk persentase
                    Serial.printf("Gas Min UPDATE: %.1f -> %.1f (increment=%.1f)\n", oldValue, gasMin, increment);

                    // IMMEDIATELY update electrowinning state to prevent override
                    ProcessLimits limits = electrowinningState.getLimits();
                    limits.gasMin = gasMin;
                    electrowinningState.setLimits(limits);
                    Serial.printf("Gas Min updated in electrowinning state: %.1f\n", gasMin);

                    Serial.printf("Gas Min (%%): %.1f (after edit)\n", gasMin);
                    displayNeedsUpdate = true; // Force display update
                } else if (menuState.currentItem == 1) {
                    Serial.printf("Editing Gas Max: before=%.1f, increment=%.1f\n", gasMax, increment);
                    float oldValue = gasMax;
                    gasMax += increment;
                    gasMax = constrain(gasMax, 0.0, 100.0); // 0-100% untuk persentase
                    Serial.printf("Gas Max UPDATE: %.1f -> %.1f (increment=%.1f)\n", oldValue, gasMax, increment);

                    // IMMEDIATELY update electrowinning state to prevent override
                    ProcessLimits limits = electrowinningState.getLimits();
                    limits.gasMax = gasMax;
                    electrowinningState.setLimits(limits);
                    Serial.printf("Gas Max updated in electrowinning state: %.1f\n", gasMax);

                    Serial.printf("Gas Max (%%): %.1f (after edit)\n", gasMax);
                    displayNeedsUpdate = true; // Force display update
                } else {
                    Serial.printf("Gas edit: Invalid item index %d\n", menuState.currentItem);
                }
            } else if (menuState.currentLevel == LEVEL_LOG_CONFIG) {
                Serial.printf("LOG CONFIG EDIT DETECTED - Level: %d, Item: %d\n", menuState.currentLevel, menuState.currentItem);
                DataLogger::LogConfig config = dataLogger.getConfig();

                if (menuState.currentItem == 1) { // Set Interval
                    float intervalSeconds = config.intervalMs / 1000.0;
                    intervalSeconds += increment;
                    intervalSeconds = constrain(intervalSeconds, 0.5, 300.0); // 0.5s to 5 minutes
                    config.intervalMs = intervalSeconds * 1000;
                    dataLogger.setConfig(config);
                    Serial.printf("Log Interval: %.1fs\n", intervalSeconds);
                    displayNeedsUpdate = true;
                } else if (menuState.currentItem == 2) { // Max Entries
                    config.maxEntries += intIncrement * 10; // Increment by 10
                    config.maxEntries = constrain(config.maxEntries, 10, 1000);
                    dataLogger.setConfig(config);
                    Serial.printf("Max Entries: %d\n", config.maxEntries);
                    displayNeedsUpdate = true;
                } else if (menuState.currentItem == 4) { // Save Interval
                    float saveMinutes = config.saveIntervalMs / 60000.0;
                    saveMinutes += increment * 10; // Increment by 1 minute
                    saveMinutes = constrain(saveMinutes, 1.0, 60.0); // 1 to 60 minutes
                    config.saveIntervalMs = saveMinutes * 60000;
                    dataLogger.setConfig(config);
                    Serial.printf("Save Interval: %.1f minutes\n", saveMinutes);
                    displayNeedsUpdate = true;
                }
            } else {
                Serial.printf("EDIT MODE: No matching level found! Current level: %d\n", menuState.currentLevel);
                Serial.printf("Expected levels: LEVEL_EDIT_pH=%d, LEVEL_EDIT_Temp=%d, LEVEL_EDIT_Gas=%d, LEVEL_LOG_CONFIG=%d\n",
                             LEVEL_EDIT_pH, LEVEL_EDIT_Temp, LEVEL_EDIT_Gas, LEVEL_LOG_CONFIG);
            }

            if (menuState.currentLevel == PumpSetting) {
                if (menuState.currentItem == 0) {
                    pumpSpeedSetting += intIncrement;
                    pumpSpeedSetting = constrain(pumpSpeedSetting, 0, 100);
                    // Only update the setting, don't apply to hardware yet
                    Serial.printf("Pump Speed Setting: %d%% (will apply when process starts)\n", pumpSpeedSetting);
                }
            } else if (menuState.currentLevel == FanSetting) {
                if (menuState.currentItem == 0) {
                    fanSpeed += intIncrement;
                    fanSpeed = constrain(fanSpeed, 0, 100);
                    // Only update the setting, don't apply to hardware yet
                    Serial.printf("Fan Speed Setting: %d%% (will apply when process starts)\n", fanSpeed);
                }
            } else if (menuState.currentLevel == VoltageControl) {
                if (menuState.currentItem == 0) {
                    voltageSetting += increment;
                    voltageSetting = constrain(voltageSetting, 0.0, 60.0); // XY6020 max voltage is 60V
                    Serial.printf("Voltage Setting Updated: %.1fV\n", voltageSetting);
                    // Update electrowinning state immediately
                    electrowinningState.setTargetVoltage(voltageSetting);
                }
            } else if (menuState.currentLevel == CurrentControl) {
                if (menuState.currentItem == 0) {
                    currentSetting += increment;
                    currentSetting = constrain(currentSetting, 0.0, 20.0); // Max 20A
                    Serial.printf("Current Setting Updated: %.1fA\n", currentSetting);
                    // Update electrowinning state immediately
                    electrowinningState.setTargetCurrent(currentSetting);
                }
            } else if (menuState.currentLevel == userA || menuState.currentLevel == NewUser) {
                if (menuState.currentItem == 0) {
                    targetProfile->pHMin += increment;
                    targetProfile->pHMin = constrain(targetProfile->pHMin, 0.0, 14.0);
                    Serial.print("pH Min: "); Serial.println(targetProfile->pHMin);
                } else if (menuState.currentItem == 1) {
                    targetProfile->pHMax += increment;
                    targetProfile->pHMax = constrain(targetProfile->pHMax, 0.0, 14.0);
                    Serial.print("pH Max: "); Serial.println(targetProfile->pHMax);
                } else if (menuState.currentItem == 2) {
                    targetProfile->pumpSpeed += intIncrement;
                    targetProfile->pumpSpeed = constrain(targetProfile->pumpSpeed, 0, 100);
                    Serial.print("Pump Speed: "); Serial.println(targetProfile->pumpSpeed);
                } else if (menuState.currentItem == 3) {
                    targetProfile->pumpTime += intIncrement;
                    targetProfile->pumpTime = constrain(targetProfile->pumpTime, 0, 60);
                    Serial.print("Pump Time: "); Serial.println(targetProfile->pumpTime);
                } else if (menuState.currentItem == 4) {
                    targetProfile->FanSpeed += intIncrement;
                    targetProfile->FanSpeed = constrain(targetProfile->FanSpeed, 0, 100);
                    Serial.print("Fan Speed: "); Serial.println(targetProfile->FanSpeed);
                } else if (menuState.currentItem == 5) {
                    targetProfile->FanTime += intIncrement;
                    targetProfile->FanTime = constrain(targetProfile->FanTime, 0, 60);
                    Serial.print("Fan Time: "); Serial.println(targetProfile->FanTime);
                } else if (menuState.currentItem == 6) {
                    targetProfile->Volt += increment;
                    targetProfile->Volt = constrain(targetProfile->Volt, 0.0, 12.0);
                    Serial.print("Voltage: "); Serial.println(targetProfile->Volt);
                } else if (menuState.currentItem == 7) {
                    targetProfile->Current += increment;
                    targetProfile->Current = constrain(targetProfile->Current, 0.0, 20.0); // Max 20A
                    Serial.print("Current: "); Serial.println(targetProfile->Current);
                }
            } else if (menuState.currentLevel == LEVEL_ADD_USER) {
                if (menuState.currentItem == 0) {
                    userNameIndex += intIncrement;
                    userNameIndex = constrain(userNameIndex, 1, 10);
                    Serial.print("User Name: User "); Serial.println(userNameIndex);
                } else if (menuState.currentItem == 1) {
                    profileTypeIndex += intIncrement;
                    profileTypeIndex = constrain(profileTypeIndex, 0, 3);
                    Serial.print("Profile Type: "); Serial.println(profileTypes[profileTypeIndex]);
                }
            }
            displayNeedsUpdate = true;
        } else {
            // Navigation mode: change selection
            menuState.currentItem = currentPosition;
            menuState.currentItem = constrain(menuState.currentItem, 0, currentMenuSize - 1);
            // Update scroll position if needed
            updateScrollPosition();
            displayNeedsUpdate = true;

            // Sync back to electrowinningState
            electrowinningState.getMenuState() = menuState;
        }
        Serial.print("Current Level: "); Serial.println(menuState.currentLevel);
        Serial.print("Current Item: "); Serial.println(menuState.currentItem);
    }

    // Debug button state every 10 seconds (diperlambat)
    static unsigned long lastButtonDebug = 0;
    if (millis() - lastButtonDebug > 10000) {
        bool buttonState = digitalRead(25);
        Serial.printf("BTN: %s\n", buttonState == 0 ? "PRESSED" : "READY");
        lastButtonDebug = millis();
    }

    // Manual button detection
    static bool lastButtonState = HIGH;
    static unsigned long lastButtonTime = 0;
    bool currentButtonState = digitalRead(25);

    // Detect button press (falling edge)
    if (lastButtonState == HIGH && currentButtonState == LOW) {
        if (millis() - lastButtonTime > 300) { // 300ms debounce
            Serial.println("Button pressed (manual detection)!");
            rotary_onButtonClick();
            lastButtonTime = millis();
        }
    }
    lastButtonState = currentButtonState;

    // Library button detection
    if (rotaryEncoder.isEncoderButtonClicked()) {
        Serial.println("Encoder button clicked (library detection)!");
        rotary_onButtonClick();
    }
}

// ========================================
// pH CALIBRATION IMPLEMENTATION
// ========================================

void loadPHCalibration() {
    preferences.begin("ph_cal", true); // Read-only mode

    pHCalibration.isCalibrated = preferences.getBool("isCalibrated", false);
    pHCalibration.pH4_voltage = preferences.getFloat("pH4V", 1.8);
    pHCalibration.pH7_voltage = preferences.getFloat("pH7V", 2.5);
    pHCalibration.pH9_voltage = preferences.getFloat("pH9V", 3.2);
    pHCalibration.slope = preferences.getFloat("slope", 0.18);
    pHCalibration.offset = preferences.getFloat("offset", 0.0);
    pHCalibration.point1_done = preferences.getBool("p1_done", false);
    pHCalibration.point2_done = preferences.getBool("p2_done", false);
    pHCalibration.point3_done = preferences.getBool("p3_done", false);
    pHCalibration.calibrationTime = preferences.getULong64("calTime", 0);

    preferences.end();

    Serial.printf("pH Calibration loaded - Calibrated: %s, pH4: %.3fV, pH7: %.3fV, pH9: %.3fV, Slope: %.3f\n",
                 pHCalibration.isCalibrated ? "YES" : "NO",
                 pHCalibration.pH4_voltage, pHCalibration.pH7_voltage, pHCalibration.pH9_voltage, pHCalibration.slope);
}

void savePHCalibration() {
    preferences.begin("ph_cal", false); // Read-write mode

    preferences.putBool("isCalibrated", pHCalibration.isCalibrated);
    preferences.putFloat("pH4V", pHCalibration.pH4_voltage);
    preferences.putFloat("pH7V", pHCalibration.pH7_voltage);
    preferences.putFloat("pH9V", pHCalibration.pH9_voltage);
    preferences.putFloat("slope", pHCalibration.slope);
    preferences.putFloat("offset", pHCalibration.offset);
    preferences.putBool("p1_done", pHCalibration.point1_done);
    preferences.putBool("p2_done", pHCalibration.point2_done);
    preferences.putBool("p3_done", pHCalibration.point3_done);
    preferences.putULong64("calTime", pHCalibration.calibrationTime);

    preferences.end();

    Serial.printf("pH Calibration saved - pH4: %.3fV, pH7: %.3fV, pH9: %.3fV, Slope: %.3f\n",
                 pHCalibration.pH4_voltage, pHCalibration.pH7_voltage, pHCalibration.pH9_voltage, pHCalibration.slope);
}

void startPHCalibration(int step) {
    calibrationState.isCalibrating = true;
    calibrationState.currentStep = step;
    calibrationState.currentReading = 0.0;
    calibrationState.sampleCount = 0;
    calibrationState.sampleSum = 0.0;
    calibrationState.startTime = millis();

    const char* stepName = "";
    switch(step) {
        case 1: stepName = "pH 4.00"; break;
        case 2: stepName = "pH 6.86"; break;
        case 3: stepName = "pH 9.18"; break;
        default: stepName = "Unknown"; break;
    }

    Serial.printf("Starting pH calibration step %d (%s)\n", step, stepName);

    // Reset encoder for calibration navigation
    rotaryEncoder.setBoundaries(0, 1, false); // 0=Cancel, 1=Confirm
    rotaryEncoder.reset(0);

    displayNeedsUpdate = true;
}

void processPHCalibration() {
    if (!calibrationState.isCalibrating) return;

    // Read current pH sensor voltage
    int sensorValue = analogRead(PH_SENSOR_PIN);
    float voltage = (sensorValue / 4095.0) * 3.3;

    // Add to running average
    calibrationState.sampleSum += voltage;
    calibrationState.sampleCount++;
    calibrationState.currentReading = calibrationState.sampleSum / calibrationState.sampleCount;

    // Debug output every 2 seconds
    static unsigned long lastDebug = 0;
    if (millis() - lastDebug > 2000) {
        Serial.printf("Calibration Step %d: Samples=%d, Avg=%.3fV, Current=%.3fV\n",
                     calibrationState.currentStep, calibrationState.sampleCount,
                     calibrationState.currentReading, voltage);
        lastDebug = millis();
        displayNeedsUpdate = true;
    }
}

void finishPHCalibration() {
    if (!calibrationState.isCalibrating) return;

    if (calibrationState.sampleCount < 10) {
        Serial.println("ERROR: Not enough samples for calibration (minimum 10)");
        calibrationState.isCalibrating = false;
        return;
    }

    float avgVoltage = calibrationState.currentReading;

    if (calibrationState.currentStep == 1) {
        // pH 4.00 calibration
        pHCalibration.pH4_voltage = avgVoltage;
        pHCalibration.point1_done = true;
        Serial.printf("pH 4.00 calibration completed: %.3fV (samples: %d)\n",
                     avgVoltage, calibrationState.sampleCount);

    } else if (calibrationState.currentStep == 2) {
        // pH 6.86 calibration
        pHCalibration.pH7_voltage = avgVoltage;
        pHCalibration.point2_done = true;
        Serial.printf("pH 6.86 calibration completed: %.3fV (samples: %d)\n",
                     avgVoltage, calibrationState.sampleCount);

    } else if (calibrationState.currentStep == 3) {
        // pH 9.18 calibration
        pHCalibration.pH9_voltage = avgVoltage;
        pHCalibration.point3_done = true;
        Serial.printf("pH 9.18 calibration completed: %.3fV (samples: %d)\n",
                     avgVoltage, calibrationState.sampleCount);
    }

    // Check if all three points are calibrated
    if (pHCalibration.point1_done && pHCalibration.point2_done && pHCalibration.point3_done) {
        // Calculate linear regression for three points
        // Points: (pH4_voltage, 4.00), (pH7_voltage, 6.86), (pH9_voltage, 9.18)

        float x1 = pHCalibration.pH4_voltage, y1 = 4.00;
        float x2 = pHCalibration.pH7_voltage, y2 = 6.86;
        float x3 = pHCalibration.pH9_voltage, y3 = 9.18;

        // Linear regression: y = mx + b
        // Calculate slope (m) and intercept (b)
        float n = 3.0;
        float sum_x = x1 + x2 + x3;
        float sum_y = y1 + y2 + y3;
        float sum_xy = x1*y1 + x2*y2 + x3*y3;
        float sum_x2 = x1*x1 + x2*x2 + x3*x3;

        float slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x);
        float intercept = (sum_y - slope * sum_x) / n;

        pHCalibration.slope = slope;
        pHCalibration.offset = intercept;
        pHCalibration.isCalibrated = true;
        pHCalibration.calibrationTime = millis();

        Serial.printf("Three-point calibration complete!\n");
        Serial.printf("Linear regression: pH = %.3f * V + %.3f\n", slope, intercept);
        Serial.printf("Slope: %.3f pH/V, Intercept: %.3f\n", slope, intercept);

        // Calculate and display R-squared for quality check
        float y_mean = sum_y / n;
        float ss_tot = (y1-y_mean)*(y1-y_mean) + (y2-y_mean)*(y2-y_mean) + (y3-y_mean)*(y3-y_mean);
        float y1_pred = slope * x1 + intercept;
        float y2_pred = slope * x2 + intercept;
        float y3_pred = slope * x3 + intercept;
        float ss_res = (y1-y1_pred)*(y1-y1_pred) + (y2-y2_pred)*(y2-y2_pred) + (y3-y3_pred)*(y3-y3_pred);
        float r_squared = 1.0 - (ss_res / ss_tot);

        Serial.printf("Calibration quality (R²): %.4f\n", r_squared);

        // Save calibration data
        savePHCalibration();
    } else {
        Serial.printf("Calibration progress: pH4.00=%s, pH6.86=%s, pH9.18=%s\n",
                     pHCalibration.point1_done ? "DONE" : "PENDING",
                     pHCalibration.point2_done ? "DONE" : "PENDING",
                     pHCalibration.point3_done ? "DONE" : "PENDING");
    }

    calibrationState.isCalibrating = false;
    displayNeedsUpdate = true;
}

void resetPHCalibration() {
    pHCalibration.isCalibrated = false;
    pHCalibration.pH4_voltage = 1.8;       // Default pH 4.00 voltage
    pHCalibration.pH7_voltage = 2.5;       // Default pH 6.86 voltage
    pHCalibration.pH9_voltage = 3.2;       // Default pH 9.18 voltage
    pHCalibration.slope = 0.18;            // Default slope
    pHCalibration.offset = 0.0;            // Default offset
    pHCalibration.point1_done = false;     // Reset all calibration points
    pHCalibration.point2_done = false;
    pHCalibration.point3_done = false;
    pHCalibration.calibrationTime = 0;

    // Save reset calibration
    savePHCalibration();

    Serial.println("pH Calibration reset to defaults - All three points cleared");
    displayNeedsUpdate = true;
}

float calculateCalibratedPH(float rawVoltage) {
    if (!pHCalibration.isCalibrated) {
        // Use default calibration if not calibrated
        // Default: pH = 7.0 - ((voltage - 2.5) / 0.18)
        return 7.0 - ((rawVoltage - 2.5) / 0.18);
    }

    // Use three-point calibrated values with linear regression
    // pH = slope * voltage + offset
    float pH = pHCalibration.slope * rawVoltage + pHCalibration.offset;

    return constrain(pH, 0.0, 14.0);
}

void displayCalibrationScreen() {
    if (!calibrationState.isCalibrating) return;

    lcd.clear();

    // Line 1: Calibration step
    lcd.setCursor(0, 0);
    switch(calibrationState.currentStep) {
        case 1: lcd.print("pH 4.00 Cal"); break;
        case 2: lcd.print("pH 6.86 Cal"); break;
        case 3: lcd.print("pH 9.18 Cal"); break;
        default: lcd.print("pH Cal"); break;
    }

    // Line 2: Current reading
    lcd.setCursor(0, 1);
    lcd.print("Voltage: ");
    lcd.print(calibrationState.currentReading, 3);
    lcd.print("V");

    // Line 3: Sample count and time
    lcd.setCursor(0, 2);
    lcd.print("Samples: ");
    lcd.print(calibrationState.sampleCount);
    lcd.setCursor(12, 2);
    unsigned long elapsed = (millis() - calibrationState.startTime) / 1000;
    lcd.print(elapsed);
    lcd.print("s");

    // Line 4: Instructions
    lcd.setCursor(0, 3);
    if (calibrationState.sampleCount < 10) {
        lcd.print("Wait for samples...");
    } else {
        lcd.print("Press to confirm");
    }
}

void displayCalibrationData() {
    lcd.clear();

    // Line 1: Calibration status and progress
    lcd.setCursor(0, 0);
    if (pHCalibration.isCalibrated) {
        lcd.print("Calibrated: YES");
    } else {
        lcd.print("Cal: ");
        lcd.print(pHCalibration.point1_done ? "4" : "-");
        lcd.print(pHCalibration.point2_done ? "7" : "-");
        lcd.print(pHCalibration.point3_done ? "9" : "-");
    }

    // Line 2: pH 4.00 voltage
    lcd.setCursor(0, 1);
    lcd.print("pH4: ");
    lcd.print(pHCalibration.pH4_voltage, 2);
    lcd.print("V");
    if (pHCalibration.point1_done) {
        lcd.print(" OK");
    }

    // Line 3: pH 6.86 voltage
    lcd.setCursor(0, 2);
    lcd.print("pH7: ");
    lcd.print(pHCalibration.pH7_voltage, 2);
    lcd.print("V");
    if (pHCalibration.point2_done) {
        lcd.print(" OK");
    }

    // Line 4: pH 9.18 voltage or slope
    lcd.setCursor(0, 3);
    if (pHCalibration.isCalibrated) {
        lcd.print("Slope: ");
        lcd.print(pHCalibration.slope, 2);
    } else {
        lcd.print("pH9: ");
        lcd.print(pHCalibration.pH9_voltage, 2);
        lcd.print("V");
        if (pHCalibration.point3_done) {
            lcd.print(" OK");
        }
    }
}
