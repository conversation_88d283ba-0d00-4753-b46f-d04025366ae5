# 📊 Moving Average Filters Implementation

## 🎯 **Overview**

Moving Average Filters adalah komponen fundamental untuk smart automation yang menyediakan data smoothing, noise reduction, dan stability detection. Sistem ini menggunakan dua jenis filter: Simple Moving Average (SMA) dan Exponential Moving Average (EMA).

---

## 🔧 **Filter Types**

### **1. Simple Moving Average (SMA)**
```cpp
class MovingAverageFilter {
private:
    float* buffer;          // Circular buffer for samples
    int bufferSize;         // Number of samples to average
    int currentIndex;       // Current position in buffer
    float sum;              // Running sum for efficiency
    bool bufferFull;        // Flag indicating buffer is full
    
public:
    MovingAverageFilter(int size);
    ~MovingAverageFilter();
    
    float addSample(float value);
    float getAverage();
    bool isStable(float threshold = 0.05);
    void reset();
    int getSampleCount();
};
```

### **2. Exponential Moving Average (EMA)**
```cpp
class EMAFilter {
private:
    float alpha;            // Smoothing factor (0-1)
    float value;            // Current EMA value
    bool initialized;       // Initialization flag
    
public:
    EMAFilter(float smoothingFactor = 0.1);
    
    float addSample(float newValue);
    float getValue();
    bool isStable(float threshold = 0.05);
    void reset();
    void setAlpha(float newAlpha);
};
```

---

## 📐 **Mathematical Implementation**

### **SMA Formula**
```
SMA(t) = (x₁ + x₂ + ... + xₙ) / n

Where:
- x₁, x₂, ..., xₙ are the last n samples
- n is the buffer size
- All samples have equal weight
```

### **EMA Formula**
```
EMA(t) = α × x(t) + (1-α) × EMA(t-1)

Where:
- α (alpha) is the smoothing factor (0 < α < 1)
- x(t) is the current sample
- EMA(t-1) is the previous EMA value
- Higher α = more responsive to recent changes
- Lower α = more smoothing, less responsive
```

### **Stability Detection**
```cpp
bool isStable(float threshold) {
    float avg = getAverage();
    float maxDeviation = 0;
    
    // Check all samples against average
    for (int i = 0; i < sampleCount; i++) {
        float deviation = abs(buffer[i] - avg) / avg;
        if (deviation > maxDeviation) {
            maxDeviation = deviation;
        }
    }
    
    return maxDeviation <= threshold;
}
```

---

## 🔨 **Implementation Code**

### **MovingAverageFilter.h**
```cpp
#ifndef MOVING_AVERAGE_FILTER_H
#define MOVING_AVERAGE_FILTER_H

class MovingAverageFilter {
private:
    float* buffer;
    int bufferSize;
    int currentIndex;
    float sum;
    bool bufferFull;
    
public:
    MovingAverageFilter(int size);
    ~MovingAverageFilter();
    
    float addSample(float value);
    float getAverage();
    bool isStable(float threshold = 0.05);
    void reset();
    int getSampleCount();
    float getVariance();
    float getStandardDeviation();
};

class EMAFilter {
private:
    float alpha;
    float value;
    bool initialized;
    float previousValues[10];  // For stability checking
    int valueIndex;
    
public:
    EMAFilter(float smoothingFactor = 0.1);
    
    float addSample(float newValue);
    float getValue();
    bool isStable(float threshold = 0.05);
    void reset();
    void setAlpha(float newAlpha);
    float getAlpha();
};

#endif
```

### **MovingAverageFilter.cpp**
```cpp
#include "MovingAverageFilter.h"
#include <cmath>

// SMA Implementation
MovingAverageFilter::MovingAverageFilter(int size) {
    bufferSize = size;
    buffer = new float[bufferSize];
    reset();
}

MovingAverageFilter::~MovingAverageFilter() {
    delete[] buffer;
}

float MovingAverageFilter::addSample(float value) {
    // Remove old value from sum if buffer is full
    if (bufferFull) {
        sum -= buffer[currentIndex];
    }
    
    // Add new value
    buffer[currentIndex] = value;
    sum += value;
    
    // Update index
    currentIndex = (currentIndex + 1) % bufferSize;
    if (currentIndex == 0) {
        bufferFull = true;
    }
    
    return getAverage();
}

float MovingAverageFilter::getAverage() {
    int count = bufferFull ? bufferSize : currentIndex;
    return count > 0 ? sum / count : 0;
}

bool MovingAverageFilter::isStable(float threshold) {
    int count = getSampleCount();
    if (count < bufferSize) return false;  // Need full buffer for stability
    
    float avg = getAverage();
    if (avg == 0) return false;  // Avoid division by zero
    
    for (int i = 0; i < bufferSize; i++) {
        float deviation = abs(buffer[i] - avg) / avg;
        if (deviation > threshold) {
            return false;
        }
    }
    return true;
}

void MovingAverageFilter::reset() {
    currentIndex = 0;
    sum = 0;
    bufferFull = false;
    for (int i = 0; i < bufferSize; i++) {
        buffer[i] = 0;
    }
}

int MovingAverageFilter::getSampleCount() {
    return bufferFull ? bufferSize : currentIndex;
}

// EMA Implementation
EMAFilter::EMAFilter(float smoothingFactor) {
    alpha = smoothingFactor;
    reset();
}

float EMAFilter::addSample(float newValue) {
    if (!initialized) {
        value = newValue;
        initialized = true;
    } else {
        value = alpha * newValue + (1 - alpha) * value;
    }
    
    // Store for stability checking
    previousValues[valueIndex] = value;
    valueIndex = (valueIndex + 1) % 10;
    
    return value;
}

float EMAFilter::getValue() {
    return value;
}

bool EMAFilter::isStable(float threshold) {
    if (!initialized) return false;
    
    // Check stability over last 10 values
    float avg = 0;
    for (int i = 0; i < 10; i++) {
        avg += previousValues[i];
    }
    avg /= 10;
    
    if (avg == 0) return false;
    
    for (int i = 0; i < 10; i++) {
        float deviation = abs(previousValues[i] - avg) / avg;
        if (deviation > threshold) {
            return false;
        }
    }
    return true;
}

void EMAFilter::reset() {
    value = 0;
    initialized = false;
    valueIndex = 0;
    for (int i = 0; i < 10; i++) {
        previousValues[i] = 0;
    }
}
```

---

## 🎯 **Filter Applications**

### **Sensor-Specific Configurations**
```cpp
// Power measurements - need high stability
MovingAverageFilter powerFilter(10);     // 10 samples
float powerStabilityThreshold = 0.05;    // 5% variation

// pH measurements - moderate filtering
MovingAverageFilter pHFilter(8);         // 8 samples  
float pHStabilityThreshold = 0.10;       // 10% variation

// Gas measurements - fast response needed
MovingAverageFilter gasFilter(5);        // 5 samples
float gasStabilityThreshold = 0.05;      // 5% variation

// Temperature - slow changes, heavy filtering
EMAFilter tempFilter(0.05);              // Very smooth
float tempStabilityThreshold = 0.15;     // 15% variation
```

### **Integration with Main System**
```cpp
// In main loop (every 100ms)
void updateSensorFilters() {
    // Read raw sensor values
    float rawPower = voltage * current;
    float rawPH = readPHSensor();
    float rawGas = readGasSensor();
    float rawTemp = readTempSensor();
    
    // Apply filters
    float filteredPower = powerFilter.addSample(rawPower);
    float filteredPH = pHFilter.addSample(rawPH);
    float filteredGas = gasFilter.addSample(rawGas);
    float filteredTemp = tempFilter.addSample(rawTemp);
    
    // Check stability for optimization
    bool systemStable = powerFilter.isStable(0.05) && 
                       pHFilter.isStable(0.10) &&
                       gasFilter.isStable(0.05);
    
    // Use filtered values for control decisions
    if (systemStable) {
        // System is stable, can proceed with optimization
        enableOptimization = true;
    }
}
```

---

## 📊 **Performance Characteristics**

### **SMA Characteristics**
- **Pros**: Simple, predictable, equal weight to all samples
- **Cons**: Slower response to changes, requires more memory
- **Best for**: Power measurements, stability detection
- **Memory usage**: O(n) where n is buffer size

### **EMA Characteristics**  
- **Pros**: Fast response, low memory usage, configurable responsiveness
- **Cons**: More complex, exponential weighting may not suit all applications
- **Best for**: Temperature, efficiency calculations
- **Memory usage**: O(1) constant memory

### **Performance Comparison**
```
Filter Type | Response Time | Memory Usage | Stability | Best Use Case
------------|---------------|--------------|-----------|---------------
SMA         | Slow          | High         | Excellent | Power, Critical measurements
EMA         | Fast          | Low          | Good      | Temperature, Trends
```

---

## 🧪 **Testing & Validation**

### **Unit Tests**
```cpp
void testMovingAverageFilter() {
    MovingAverageFilter filter(5);
    
    // Test basic functionality
    assert(filter.addSample(10.0) == 10.0);
    assert(filter.addSample(20.0) == 15.0);
    assert(filter.addSample(30.0) == 20.0);
    
    // Test stability detection
    for (int i = 0; i < 5; i++) {
        filter.addSample(25.0);  // Stable values
    }
    assert(filter.isStable(0.05) == true);
    
    filter.addSample(50.0);  // Unstable value
    assert(filter.isStable(0.05) == false);
}

void testEMAFilter() {
    EMAFilter filter(0.1);
    
    // Test initialization
    assert(filter.addSample(100.0) == 100.0);
    
    // Test smoothing
    float result = filter.addSample(200.0);
    assert(result == 110.0);  // 0.1 * 200 + 0.9 * 100
}
```

### **Integration Tests**
```cpp
void testFilterIntegration() {
    // Test with real sensor data simulation
    MovingAverageFilter powerFilter(10);
    
    // Simulate noisy power readings
    float noisyPower[] = {98.5, 101.2, 99.8, 100.5, 99.2, 100.8, 99.5, 100.2};
    
    for (int i = 0; i < 8; i++) {
        float filtered = powerFilter.addSample(noisyPower[i]);
        Serial.printf("Raw: %.1f, Filtered: %.1f\n", noisyPower[i], filtered);
    }
    
    // Should converge to ~100W with reduced noise
    assert(abs(powerFilter.getAverage() - 100.0) < 1.0);
}
```

---

## 🎉 **Expected Results**

### **Noise Reduction**
- **50-80% reduction** in sensor noise
- **Smoother data** for control algorithms
- **More stable** system behavior

### **Stability Detection**
- **Accurate detection** of stable operating conditions
- **Prevents premature optimization** during unstable periods
- **Improves convergence** of P&O algorithm

### **System Performance**
- **Better control accuracy** due to cleaner data
- **Reduced false alarms** in safety systems
- **Improved optimization results** with stable data foundation

**Moving Average Filters menyediakan foundation yang solid untuk semua algoritma smart automation dengan data yang bersih dan stabil.** 📊✨

---

## ✅ **Implementation Status**

### **Completed Tasks**
- [x] **Header file creation** (`src/moving_average_filter.h`)
- [x] **Implementation file creation** (`src/moving_average_filter.cpp`)
- [x] **Integration with main system** (`src/main.cpp`)
- [x] **Basic testing and validation** (compilation successful)

### **Implementation Details**

#### **Files Created:**
1. **`src/moving_average_filter.h`** - Complete header with three main classes
2. **`src/moving_average_filter.cpp`** - Full implementation of all filter algorithms
3. **Integration in `src/main.cpp`** - Added filter manager and sensor integration

#### **Key Features Implemented:**
- **MovingAverageFilter Class**: Circular buffer-based SMA with stability detection
- **EMAFilter Class**: Exponential moving average with configurable smoothing
- **FilterManager Class**: Centralized management of all sensor filters
- **Raw Sensor Functions**: Separated raw readings from filtered outputs
- **System Integration**: Automatic filter updates every 100ms in main loop

#### **Filter Configuration:**
- **Power Filter**: 10-sample SMA (5% stability threshold)
- **pH Filter**: 8-sample SMA (10% stability threshold)
- **Gas Filter**: 5-sample SMA (5% stability threshold)
- **Temperature Filter**: EMA with α=0.05 (15% stability threshold)
- **Efficiency Filter**: EMA with α=0.1 (10% stability threshold)

### **Next Steps**
- [ ] **Integrate filters with sensor readings** - Apply filters to all sensor data
- [ ] **Real-world testing** - Test with actual sensor hardware
- [ ] **Performance optimization** - Fine-tune filter parameters
- [ ] **Documentation updates** - Add usage examples and troubleshooting
