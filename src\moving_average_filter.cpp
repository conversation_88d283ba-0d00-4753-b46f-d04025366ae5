#include "moving_average_filter.h"
#include <cmath>

// ============================================================================
// MovingAverageFilter Implementation
// ============================================================================

MovingAverageFilter::MovingAverageFilter(int size) {
    bufferSize = size;
    buffer = new float[bufferSize];
    reset();
}

MovingAverageFilter::~MovingAverageFilter() {
    delete[] buffer;
}

float MovingAverageFilter::addSample(float value) {
    // Remove old value from sum if buffer is full
    if (bufferFull) {
        sum -= buffer[currentIndex];
    }
    
    // Add new value
    buffer[currentIndex] = value;
    sum += value;
    
    // Update index
    currentIndex = (currentIndex + 1) % bufferSize;
    if (currentIndex == 0) {
        bufferFull = true;
    }
    
    return getAverage();
}

float MovingAverageFilter::getAverage() {
    int count = bufferFull ? bufferSize : currentIndex;
    return count > 0 ? sum / count : 0;
}

bool MovingAverageFilter::isStable(float threshold) {
    int count = getSampleCount();
    if (count < bufferSize) return false;  // Need full buffer for stability
    
    float avg = getAverage();
    if (avg == 0) return false;  // Avoid division by zero
    
    for (int i = 0; i < bufferSize; i++) {
        float deviation = abs(buffer[i] - avg) / avg;
        if (deviation > threshold) {
            return false;
        }
    }
    return true;
}

void MovingAverageFilter::reset() {
    currentIndex = 0;
    sum = 0;
    bufferFull = false;
    for (int i = 0; i < bufferSize; i++) {
        buffer[i] = 0;
    }
}

int MovingAverageFilter::getSampleCount() {
    return bufferFull ? bufferSize : currentIndex;
}

float MovingAverageFilter::getVariance() {
    int count = getSampleCount();
    if (count < 2) return 0;
    
    float avg = getAverage();
    float variance = 0;
    
    for (int i = 0; i < count; i++) {
        float diff = buffer[i] - avg;
        variance += diff * diff;
    }
    
    return variance / count;
}

float MovingAverageFilter::getStandardDeviation() {
    return sqrt(getVariance());
}

bool MovingAverageFilter::isBufferFull() {
    return bufferFull;
}

// ============================================================================
// EMAFilter Implementation
// ============================================================================

EMAFilter::EMAFilter(float smoothingFactor) {
    alpha = smoothingFactor;
    reset();
}

float EMAFilter::addSample(float newValue) {
    if (!initialized) {
        value = newValue;
        initialized = true;
    } else {
        value = alpha * newValue + (1 - alpha) * value;
    }
    
    // Store for stability checking
    previousValues[valueIndex] = value;
    valueIndex = (valueIndex + 1) % 10;
    if (valueCount < 10) {
        valueCount++;
    }
    
    return value;
}

float EMAFilter::getValue() {
    return value;
}

bool EMAFilter::isStable(float threshold) {
    if (!initialized || valueCount < 10) return false;
    
    // Check stability over last 10 values
    float avg = 0;
    for (int i = 0; i < 10; i++) {
        avg += previousValues[i];
    }
    avg /= 10;
    
    if (avg == 0) return false;
    
    for (int i = 0; i < 10; i++) {
        float deviation = abs(previousValues[i] - avg) / avg;
        if (deviation > threshold) {
            return false;
        }
    }
    return true;
}

void EMAFilter::reset() {
    value = 0;
    initialized = false;
    valueIndex = 0;
    valueCount = 0;
    for (int i = 0; i < 10; i++) {
        previousValues[i] = 0;
    }
}

void EMAFilter::setAlpha(float newAlpha) {
    if (newAlpha > 0 && newAlpha <= 1) {
        alpha = newAlpha;
    }
}

float EMAFilter::getAlpha() {
    return alpha;
}

bool EMAFilter::isInitialized() {
    return initialized;
}

// ============================================================================
// FilterManager Implementation
// ============================================================================

FilterManager::FilterManager() {
    // Initialize filters with optimal parameters for each sensor type
    powerFilter = new MovingAverageFilter(10);     // 10 samples for power stability
    pHFilter = new MovingAverageFilter(8);         // 8 samples for pH
    gasFilter = new MovingAverageFilter(5);        // 5 samples for gas (faster response)
    tempFilter = new EMAFilter(0.05);              // Very smooth for temperature
    efficiencyFilter = new EMAFilter(0.1);         // Moderate smoothing for efficiency

    // Set stability thresholds for each sensor type
    powerStabilityThreshold = 0.05;        // 5% for power measurements
    pHStabilityThreshold = 0.10;           // 10% for pH measurements
    gasStabilityThreshold = 0.05;          // 5% for gas measurements
    tempStabilityThreshold = 0.15;         // 15% for temperature (slower changes)
    efficiencyStabilityThreshold = 0.10;   // 10% for efficiency calculations
}

FilterManager::~FilterManager() {
    delete powerFilter;
    delete pHFilter;
    delete gasFilter;
    delete tempFilter;
    delete efficiencyFilter;
}

void FilterManager::updateFilters(float power, float pH, float gas, float temperature, float efficiency) {
    powerFilter->addSample(power);
    pHFilter->addSample(pH);
    gasFilter->addSample(gas);
    tempFilter->addSample(temperature);
    efficiencyFilter->addSample(efficiency);
}

float FilterManager::getFilteredPower() {
    return powerFilter->getAverage();
}

float FilterManager::getFilteredPH() {
    return pHFilter->getAverage();
}

float FilterManager::getFilteredGas() {
    return gasFilter->getAverage();
}

float FilterManager::getFilteredTemperature() {
    return tempFilter->getValue();
}

float FilterManager::getFilteredEfficiency() {
    return efficiencyFilter->getValue();
}

bool FilterManager::isSystemStable() {
    // System is stable if power, pH, and gas are all stable
    // Temperature is not included as it changes slowly
    return isPowerStable() && isPHStable() && isGasStable();
}

bool FilterManager::isPowerStable() {
    return powerFilter->isStable(powerStabilityThreshold);
}

bool FilterManager::isPHStable() {
    return pHFilter->isStable(pHStabilityThreshold);
}

bool FilterManager::isGasStable() {
    return gasFilter->isStable(gasStabilityThreshold);
}

bool FilterManager::isTemperatureStable() {
    return tempFilter->isStable(tempStabilityThreshold);
}

void FilterManager::resetAllFilters() {
    powerFilter->reset();
    pHFilter->reset();
    gasFilter->reset();
    tempFilter->reset();
    efficiencyFilter->reset();
}

void FilterManager::getFilterStatus(char* buffer, int bufferSize) {
    snprintf(buffer, bufferSize,
        "Filter Status:\n"
        "Power: %.2fW (Stable: %s, Samples: %d)\n"
        "pH: %.2f (Stable: %s, Samples: %d)\n"
        "Gas: %.1f%% (Stable: %s, Samples: %d)\n"
        "Temp: %.1f°C (Stable: %s, Init: %s)\n"
        "Efficiency: %.1f%% (Stable: %s, Init: %s)\n"
        "System Stable: %s",
        getFilteredPower(), isPowerStable() ? "YES" : "NO", powerFilter->getSampleCount(),
        getFilteredPH(), isPHStable() ? "YES" : "NO", pHFilter->getSampleCount(),
        getFilteredGas(), isGasStable() ? "YES" : "NO", gasFilter->getSampleCount(),
        getFilteredTemperature(), isTemperatureStable() ? "YES" : "NO", tempFilter->isInitialized() ? "YES" : "NO",
        getFilteredEfficiency(), efficiencyFilter->isStable(efficiencyStabilityThreshold) ? "YES" : "NO", efficiencyFilter->isInitialized() ? "YES" : "NO",
        isSystemStable() ? "YES" : "NO"
    );
}
