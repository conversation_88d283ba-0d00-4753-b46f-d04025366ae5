#include <Arduino.h>
#include <WiFi.h>
#include <LiquidCrystal_I2C.h>
#include <Preferences.h>
#include <ESP_MultiResetDetector.h>
#include <time.h>

// XY6020 WiFi Controller includes
#include "buttonctrl.h"
#include "mqttclient.h"
#include "settings.h"
#include "webserver_esp32.h"
#include "xy6020.h"

// Electrowinning onsite operation includes
#include "AiEsp32RotaryEncoder.h"
#include "menu.h"
#include "display.h"
#include "electrowinning_state.h"
#include "data_logger.h"
#include "moving_average_filter.h"
#include "po_algorithm.h"
#include "efficiency_calculator.h"
#include "smart_automation_test.h"

// DS18B20 Temperature Sensor Libraries
#include <OneWire.h>
#include <DallasTemperature.h>

// Forward declaration for pH calibration function
extern float calculateCalibratedPH(float rawVoltage);

// Configuration defines
#define ACCESS_POINT_NAME "Electrowinning_Config"
#define EEPROM_SIZE 4096
#define ESP_MRD_USE_EEPROM true
#define MRD_TIMES 5
#define MRD_TIMEOUT 5
#define MRD_ADDRESS 4000

// Pin definitions
#ifdef ESP32
#define XY_RX_PIN 16  // GPIO16 untuk ESP32 (Modbus)
#define XY_TX_PIN 17  // GPIO17 untuk ESP32 (Modbus)
#else
#define XY_RX_PIN 4   // D1 untuk ESP8266
#define XY_TX_PIN 5   // D2 untuk ESP8266
#endif

#define ROTARY_ENCODER_A_PIN 32
#define ROTARY_ENCODER_B_PIN 33
#define ROTARY_ENCODER_BUTTON_PIN 25
#define ROTARY_ENCODER_VCC_PIN -1
#define ROTARY_ENCODER_STEPS 4

// L298N Motor Driver Pin Definitions
#define PUMP_PWM_PIN    26    // ENA - PWM untuk Water Pump (Channel A)
#define PUMP_IN1_PIN    27    // IN1 - Direction control (always HIGH untuk forward)
#define PUMP_IN2_PIN    14    // IN2 - Direction control (always LOW untuk forward)

#define FAN_PWM_PIN     18    // ENB - PWM untuk Fan (Channel B)
#define FAN_IN3_PIN     19    // IN3 - Direction control (always HIGH untuk forward)
#define FAN_IN4_PIN     23    // IN4 - Direction control (always LOW untuk forward)

// PWM Settings
#define PWM_FREQUENCY    1000    // 1kHz PWM frequency
#define PWM_RESOLUTION   8       // 8-bit resolution (0-255)
#define PWM_PUMP_CHANNEL 0       // PWM channel untuk pump
#define PWM_FAN_CHANNEL  1       // PWM channel untuk fan

// Sensor Pin Definitions
#define MQ8_SENSOR_PIN   39      // GPIO39 (VN) - MQ-8 Hydrogen Gas Sensor A0 (Analog Output)
#define PH_SENSOR_PIN    36      // GPIO36 (VP) - pH Sensor Board P0 pin (Analog)
#define ONE_WIRE_BUS     34      // GPIO34 - pH Sensor Board T2 pin - DS18B20 (Digital 1-Wire)

// Global objects
Settings settings;
auto &cfg = settings.data();
Xy6020 xy(cfg, XY_RX_PIN, XY_TX_PIN);
XyWebServer webserver(&xy, settings);
MqttClient mqtt(xy, settings);
MultiResetDetector mrd(MRD_TIMEOUT, MRD_ADDRESS);

AiEsp32RotaryEncoder rotaryEncoder(ROTARY_ENCODER_A_PIN, ROTARY_ENCODER_B_PIN, ROTARY_ENCODER_BUTTON_PIN, ROTARY_ENCODER_VCC_PIN, ROTARY_ENCODER_STEPS);
LiquidCrystal_I2C lcd(0x27, 20, 4);
Preferences preferences;

// DS18B20 Temperature Sensor Setup
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature sensors(&oneWire);  // Menggunakan nama 'sensors' seperti contoh

// Timing variables
unsigned long ts;
unsigned long lastDisplayUpdate = 0;
unsigned long lastStateUpdate = 0;
bool displayNeedsUpdate = true;

// Legacy variables for compatibility
float pH = 0.0, temp = 0.0, current = 0.0, gas = 0.0;
int pumpSpeed = 0, fanSpeed = 0, voltage = 0;
float pHMin = 0.0, pHMax = 0.0, tempMin = 0.0, tempMax = 0.0, gasMin = 0.0, gasMax = 0.0;
int pumpSpeedSetting = 0, fanTimeSetting = 0;
float voltageSetting = 0.0, currentSetting = 0.0;
int currentMenuSize = 0, previousEncoderPosition = 0;
int userNameIndex = 1, profileTypeIndex = 0;
UserProfile profiles[10], tempProfile;
int profileCount = 1, currentProfileIndex = -1;
bool isRunning = false;
MenuState menuState = {LEVEL_STATUS, 0, 0, 0, false, 0};
const char* profileTypes[] = {"Gold", "Silver", "Perak", "Perunggu"};

// Data Logger instance
DataLogger dataLogger;

// Smart Automation instances
FilterManager filterManager;
POAlgorithm poAlgorithm;
EfficiencyCalculator efficiencyCalculator;
SmartAutomationTest smartTest;

// Raw sensor reading functions (without filtering)
float readRawMQ8HydrogenLevel();
float readRawPHSensor();
float readRawTemperatureSensor();

// Filtered sensor reading functions
float getFilteredPower();
float getFilteredPH();
float getFilteredGas();
float getFilteredTemperature();
float getFilteredEfficiency();

// System stability checking
bool isSystemStableForOptimization();

// Filter update function
void updateAllFilters();

// P&O Algorithm control functions
void enablePOAlgorithm();
void disablePOAlgorithm();
void resetPOAlgorithm();
void printPOStatus();

// Time helper functions
String getCurrentTimeString() {
    time_t now = time(nullptr);
    if (now > 1000000000) {
        struct tm* timeinfo = localtime(&now);
        char buffer[32];
        strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);
        return String(buffer);
    } else {
        return "Time not synced";
    }
}

bool isTimeSync() {
    time_t now = time(nullptr);
    return (now > 1000000000); // Valid Unix timestamp
}

// Motor control functions
int percentageToPWM(int percentage) {
    return map(constrain(percentage, 0, 100), 0, 100, 0, 255);
}

void setPumpSpeed(int percentage) {
    int pwmValue = percentageToPWM(percentage);
    ledcWrite(PWM_PUMP_CHANNEL, pwmValue);

    // Set direction (always forward)
    digitalWrite(PUMP_IN1_PIN, HIGH);
    digitalWrite(PUMP_IN2_PIN, LOW);

    Serial.printf("Pump speed set to %d%% (PWM: %d)\n", percentage, pwmValue);
}

void setFanSpeed(int percentage) {
    int pwmValue = percentageToPWM(percentage);
    ledcWrite(PWM_FAN_CHANNEL, pwmValue);

    // Set direction (always forward)
    digitalWrite(FAN_IN3_PIN, HIGH);
    digitalWrite(FAN_IN4_PIN, LOW);

    Serial.printf("Fan speed set to %d%% (PWM: %d)\n", percentage, pwmValue);
}

void initializeMotorControl() {
    // Initialize PWM channels
    ledcSetup(PWM_PUMP_CHANNEL, PWM_FREQUENCY, PWM_RESOLUTION);
    ledcSetup(PWM_FAN_CHANNEL, PWM_FREQUENCY, PWM_RESOLUTION);

    // Attach PWM channels to pins
    ledcAttachPin(PUMP_PWM_PIN, PWM_PUMP_CHANNEL);
    ledcAttachPin(FAN_PWM_PIN, PWM_FAN_CHANNEL);

    // Initialize direction control pins
    pinMode(PUMP_IN1_PIN, OUTPUT);
    pinMode(PUMP_IN2_PIN, OUTPUT);
    pinMode(FAN_IN3_PIN, OUTPUT);
    pinMode(FAN_IN4_PIN, OUTPUT);

    // Set initial state (motors off)
    setPumpSpeed(0);
    setFanSpeed(0);

    Serial.println("Motor control initialized - L298N driver ready");
    Serial.printf("Pin assignments - Pump: PWM=%d, IN1=%d, IN2=%d | Fan: PWM=%d, IN3=%d, IN4=%d\n",
                 PUMP_PWM_PIN, PUMP_IN1_PIN, PUMP_IN2_PIN, FAN_PWM_PIN, FAN_IN3_PIN, FAN_IN4_PIN);
}

// Sensor Functions
void initializeSensors() {
    // Initialize analog sensor pins
    pinMode(MQ8_SENSOR_PIN, INPUT);
    pinMode(PH_SENSOR_PIN, INPUT);

    // Set ADC attenuation for better range (0-3.3V)
    analogSetAttenuation(ADC_11db);

    // Initialize DS18B20 temperature sensor
    sensors.begin();  // Start the DS18B20 Library

    // Try parasitic power mode if normal mode fails
    if (sensors.getDeviceCount() == 0) {
        Serial.println("No DS18B20 devices found in normal mode, trying parasitic power mode...");
        sensors.setWaitForConversion(true);
        sensors.setCheckForConversion(true);
        sensors.begin();
    }

    Serial.println("Sensors initialized");
    Serial.printf("MQ-8 Hydrogen sensor A0 connected to GPIO%d (VN) - Analog mode\n", MQ8_SENSOR_PIN);
    Serial.printf("pH Sensor Board P0 pin connected to GPIO%d (VP/ADC1_CH0) - Analog mode\n", PH_SENSOR_PIN);
    Serial.printf("pH Sensor Board T2 pin connected to GPIO%d - DS18B20 Digital 1-Wire mode\n", ONE_WIRE_BUS);

    // Check DS18B20 sensor count
    int deviceCount = sensors.getDeviceCount();
    Serial.printf("Found %d DS18B20 temperature sensor(s)\n", deviceCount);

    if (deviceCount == 0) {
        Serial.println("WARNING: No DS18B20 sensors found! Check wiring.");
    } else {
        // Set resolution to 12-bit (0.0625°C precision)
        sensors.setResolution(12);
        Serial.println("DS18B20 sensor configured with 12-bit resolution");
    }

    Serial.println("Sensor pins configured - diagnostic will run after sensor functions are defined");
}

float readRawMQ8HydrogenLevel() {
    // Read analog value from MQ-8 sensor A0 output
    int sensorValue = analogRead(MQ8_SENSOR_PIN);

    // MQ-8 A0 output characteristics:
    // - Clean air: Lower voltage (lower ADC value)
    // - High H2 concentration: Higher voltage (higher ADC value)
    // - ESP32 ADC: 0-4095 (12-bit) for 0-3.3V range

    // Convert to percentage (0-100%)
    float percentage = (sensorValue / 4095.0) * 100.0;

    // Ensure value is within valid range
    percentage = constrain(percentage, 0.0, 100.0);

    return percentage;
}

float readMQ8HydrogenLevel() {
    // For backward compatibility, return filtered value
    return getFilteredGas();
}

float readRawPHSensor() {
    // Read analog value from pH sensor board P0 pin
    int sensorValue = analogRead(PH_SENSOR_PIN);

    // Convert ADC to voltage (0-3.3V)
    float voltage = (sensorValue / 4095.0) * 3.3;

    // Convert voltage to pH value using calibration data
    // Use calibrated values if available, otherwise use defaults
    float pH = calculateCalibratedPH(voltage);

    // Debug output untuk kalibrasi
    static unsigned long lastDebug = 0;
    if (millis() - lastDebug > 10000) { // Debug every 10 seconds
        Serial.printf("pH Debug - ADC: %d, Voltage: %.3fV, pH: %.2f\n",
                     sensorValue, voltage, pH);
        lastDebug = millis();
    }

    // Constrain to valid pH range
    pH = constrain(pH, 0.0, 14.0);

    return pH;
}

float readPHSensor() {
    // For backward compatibility, return filtered value
    return getFilteredPH();
}

float readRawTemperatureSensor() {
    // Check if any devices are connected
    int deviceCount = sensors.getDeviceCount();
    if (deviceCount == 0) {
        static unsigned long lastWarning = 0;
        if (millis() - lastWarning > 10000) { // Warning every 10 seconds
            Serial.println("WARNING: No DS18B20 devices found during reading");
            lastWarning = millis();
        }
        return 25.0; // Return default temperature
    }

    // Request temperature reading from DS18B20 (seperti contoh kode)
    sensors.requestTemperatures(); // Send the command to get temperatures

    // Get temperature in Celsius dengan multiple readings untuk stability
    float tempTotal = 0;
    int validReadings = 0;

    // Ambil beberapa reading untuk averaging (seperti contoh kode)
    for(int i = 0; i < 5; i++) {
        float temp = sensors.getTempCByIndex(0);
        if (temp != DEVICE_DISCONNECTED_C && temp != -127.0) {
            tempTotal += temp;
            validReadings++;
        }
        delay(100); // Small delay between readings
    }

    float temperature;
    if (validReadings > 0) {
        temperature = tempTotal / validReadings; // Average temperature
    } else {
        temperature = DEVICE_DISCONNECTED_C; // No valid readings
    }

    // Debug output for troubleshooting
    static unsigned long lastDebug = 0;
    if (millis() - lastDebug > 5000) { // Debug every 5 seconds
        Serial.printf("DS18B20 Debug - Devices: %d, Raw temp: %.2f°C\n", deviceCount, temperature);
        lastDebug = millis();
    }

    // Check if reading is valid
    if (temperature == DEVICE_DISCONNECTED_C || temperature == -127.0) {
        Serial.println("ERROR: DS18B20 sensor disconnected during reading!");
        return 25.0; // Return default temperature
    }

    // Constrain to reasonable temperature range
    temperature = constrain(temperature, -55.0, 125.0); // DS18B20 operating range

    return temperature;
}

float readTemperatureSensor() {
    // For backward compatibility, return filtered value
    return getFilteredTemperature();
}

void testHardwareConnections() {
    Serial.println("\n=== HARDWARE CONNECTION TEST ===");

    // Test GPIO 34 basic functionality
    pinMode(ONE_WIRE_BUS, INPUT_PULLUP);
    delay(100);
    int pinState = digitalRead(ONE_WIRE_BUS);
    Serial.printf("GPIO 34 state with internal pull-up: %s\n", pinState ? "HIGH" : "LOW");

    if (pinState == LOW) {
        Serial.println("ERROR: GPIO 34 is stuck LOW - possible short to ground!");
        Serial.println("Check: DQ pin not shorted to GND");
    } else {
        Serial.println("GPIO 34 pull-up test: PASSED");
    }

    // Test external pull-up resistor
    pinMode(ONE_WIRE_BUS, INPUT);  // Disable internal pull-up
    delay(100);
    pinState = digitalRead(ONE_WIRE_BUS);
    Serial.printf("GPIO 34 state without internal pull-up: %s\n", pinState ? "HIGH" : "LOW");

    if (pinState == LOW) {
        Serial.println("ERROR: No external pull-up resistor detected!");
        Serial.println("SOLUTION: Add 4.7kΩ resistor between GPIO 34 and 3.3V");
    } else {
        Serial.println("External pull-up resistor test: PASSED");
    }

    Serial.println("=== END HARDWARE TEST ===\n");
}

void debugDS18B20() {
    Serial.println("\n=== DS18B20 DETAILED DEBUG ===");

    // First test hardware connections
    testHardwareConnections();

    // Check device count
    int deviceCount = sensors.getDeviceCount();
    Serial.printf("Device count: %d\n", deviceCount);

    if (deviceCount == 0) {
        Serial.println("ERROR: No DS18B20 devices found!");
        Serial.println("Troubleshooting steps:");
        Serial.println("1. Check VDD connection to 3.3V");
        Serial.println("2. Check GND connection");
        Serial.println("3. Check DQ connection to GPIO 34");
        Serial.println("4. Check 4.7kΩ pull-up resistor between DQ and VDD");
        Serial.println("5. Try different DS18B20 sensor");

        // Test raw 1-Wire communication
        Serial.println("\nTesting raw 1-Wire communication...");
        oneWire.reset_search();
        uint8_t addr[8];
        if (oneWire.search(addr)) {
            Serial.print("Found 1-Wire device with address: ");
            for (int i = 0; i < 8; i++) {
                Serial.printf("%02X ", addr[i]);
            }
            Serial.println();

            if (OneWire::crc8(addr, 7) != addr[7]) {
                Serial.println("ERROR: CRC is not valid!");
            } else {
                Serial.println("CRC is valid");
                if (addr[0] == 0x28) {
                    Serial.println("Device family: DS18B20");
                } else {
                    Serial.printf("Unknown device family: 0x%02X\n", addr[0]);
                }
            }
        } else {
            Serial.println("No 1-Wire devices found on bus");
            Serial.println("This indicates hardware connection problem");
        }

    } else {
        Serial.printf("Found %d DS18B20 device(s)\n", deviceCount);

        // Get device addresses
        DeviceAddress deviceAddress;
        for (int i = 0; i < deviceCount; i++) {
            if (sensors.getAddress(deviceAddress, i)) {
                Serial.printf("Device %d address: ", i);
                for (int j = 0; j < 8; j++) {
                    Serial.printf("%02X ", deviceAddress[j]);
                }
                Serial.println();

                // Test temperature reading (seperti contoh kode)
                sensors.requestTemperatures(); // Send the command to get temperatures
                float temp = sensors.getTempCByIndex(i);

                if (temp == DEVICE_DISCONNECTED_C || temp == -127.0) {
                    Serial.printf("Device %d: DISCONNECTED\n", i);
                } else {
                    Serial.printf("Device %d: %.2f°C\n", i, temp);
                }
            }
        }
    }

    Serial.println("=== END DS18B20 DEBUG ===\n");

    // If no devices found, suggest alternative pins
    if (deviceCount == 0) {
        Serial.println("=== ALTERNATIVE PIN SUGGESTIONS ===");
        Serial.println("If GPIO 34 has issues, try these pins:");
        Serial.println("- GPIO 32 (but move rotary encoder CLK)");
        Serial.println("- GPIO 33 (but move rotary encoder DT)");
        Serial.println("- GPIO 35 (input only, good for sensors)");
        Serial.println("- GPIO 4 (general purpose)");
        Serial.println("- GPIO 2 (general purpose)");
        Serial.println("=====================================\n");
    }
}

void manualOneWireTest() {
    Serial.println("\n=== MANUAL 1-WIRE TEST ===");

    // Manual 1-Wire reset test
    Serial.println("Testing 1-Wire reset pulse...");

    // Configure pin as output and pull low
    pinMode(ONE_WIRE_BUS, OUTPUT);
    digitalWrite(ONE_WIRE_BUS, LOW);
    delayMicroseconds(480);  // Reset pulse

    // Release line and wait for presence pulse
    pinMode(ONE_WIRE_BUS, INPUT);
    delayMicroseconds(70);   // Wait for presence pulse

    int presence = digitalRead(ONE_WIRE_BUS);
    Serial.printf("Presence pulse detected: %s\n", presence == LOW ? "YES" : "NO");

    if (presence == LOW) {
        Serial.println("SUCCESS: DS18B20 is responding to reset pulse!");
        Serial.println("This means hardware connection is working");
    } else {
        Serial.println("FAILED: No presence pulse detected");
        Serial.println("Possible causes:");
        Serial.println("1. No DS18B20 connected");
        Serial.println("2. Wrong wiring");
        Serial.println("3. No pull-up resistor");
        Serial.println("4. Faulty DS18B20 sensor");
    }

    delayMicroseconds(410);  // Complete the reset cycle
    Serial.println("=== END MANUAL TEST ===\n");
}

void runSensorDiagnostic() {
    // Test initial reading dan diagnostic untuk semua sensor
    Serial.println("=== SENSOR DIAGNOSTIC ===");
    for (int i = 0; i < 5; i++) {
        // MQ-8 Gas Sensor
        int gasReading = analogRead(MQ8_SENSOR_PIN);
        float gasVoltage = (gasReading / 4095.0) * 3.3;
        float gasPercentage = (gasReading / 4095.0) * 100.0;

        // pH Sensor
        int pHReading = analogRead(PH_SENSOR_PIN);
        float pHVoltage = (pHReading / 4095.0) * 3.3;
        float pHValue = readPHSensor();

        // DS18B20 Temperature Sensor (Digital)
        float tempValue = readTemperatureSensor();

        Serial.printf("Test %d:\n", i+1);
        Serial.printf("  Gas: ADC=%d, V=%.2f, H2=%.1f%%\n", gasReading, gasVoltage, gasPercentage);
        Serial.printf("  pH:  ADC=%d, V=%.2f, pH=%.1f\n", pHReading, pHVoltage, pHValue);
        Serial.printf("  Temp: DS18B20=%.2f°C (Digital)\n", tempValue);
        Serial.println();
        delay(1000);
    }
    Serial.println("=== End Diagnostic ===");

    // Instruksi testing untuk user
    Serial.println("\n=== TESTING INSTRUCTIONS ===");
    Serial.println("GAS SENSOR (MQ-8):");
    Serial.println("1. Normal air: Should read 5-20%");
    Serial.println("2. Use lighter near sensor: Should increase significantly");
    Serial.println("pH SENSOR BOARD (P0 pin):");
    Serial.println("3. Test with pH 7 buffer: Should read ~7.0");
    Serial.println("4. Test with pH 4 buffer: Should read ~4.0");
    Serial.println("5. Adjust offset potentiometer if needed");
    Serial.println("DS18B20 TEMPERATURE SENSOR (T2 pin):");
    Serial.println("6. Room temperature: Should read 20-30°C");
    Serial.println("7. Dip waterproof sensor in water: Should change");
    Serial.println("8. Ice water: Should read ~0°C");
    Serial.println("9. Hot water: Should read 40-80°C");
    Serial.println("=============================\n");
}

void testMotorControl() {
    Serial.println("\n=== MOTOR CONTROL TEST SEQUENCE ===");

    // Test pump at different speeds
    Serial.println("Testing Water Pump...");
    for (int speed = 0; speed <= 100; speed += 25) {
        Serial.printf("Pump speed: %d%%\n", speed);
        setPumpSpeed(speed);
        delay(2000); // Run for 2 seconds at each speed
    }
    setPumpSpeed(0); // Stop pump

    delay(1000);

    // Test fan at different speeds
    Serial.println("Testing Fan Exhaust...");
    for (int speed = 0; speed <= 100; speed += 25) {
        Serial.printf("Fan speed: %d%%\n", speed);
        setFanSpeed(speed);
        delay(2000); // Run for 2 seconds at each speed
    }
    setFanSpeed(0); // Stop fan

    Serial.println("Motor test sequence completed!");
}

void testPumpVoltageOutput() {
    Serial.println("\n=== PUMP VOLTAGE OUTPUT TEST ===");
    Serial.println("This test will step through pump speeds from 0% to 100%");
    Serial.println("Measure voltage at L298N output terminals during each step");
    Serial.println("Expected: 0V to 12V proportional to percentage");
    Serial.println("=========================================");

    for (int percentage = 0; percentage <= 100; percentage += 10) {
        int pwmValue = map(constrain(percentage, 0, 100), 0, 100, 0, 255);
        float expectedVoltage = (pwmValue / 255.0) * 12.0; // Assuming 12V supply

        setPumpSpeed(percentage);

        Serial.printf("Step %d: %d%% | PWM: %d/255 | Expected: %.2fV\n",
                     (percentage/10 + 1), percentage, pwmValue, expectedVoltage);
        Serial.println("  -> Measure actual voltage at L298N output now");
        Serial.println("  -> Record: Voltage = _____ V, RPM = _____ (if tachometer available)");
        Serial.println("  -> Press any key to continue to next step...");

        delay(5000); // 5 seconds for measurement
        Serial.println("");
    }

    setPumpSpeed(0); // Stop pump
    Serial.println("Test completed. Pump stopped.");
    Serial.println("Compare measured voltages with expected values above.");
}

void testFanVoltageOutput() {
    Serial.println("\n=== FAN VOLTAGE OUTPUT TEST ===");
    Serial.println("This test will step through fan speeds from 0% to 100%");
    Serial.println("Measure voltage at L298N output terminals during each step");
    Serial.println("Expected: 0V to 12V proportional to percentage");
    Serial.println("=========================================");

    for (int percentage = 0; percentage <= 100; percentage += 10) {
        int pwmValue = map(constrain(percentage, 0, 100), 0, 100, 0, 255);
        float expectedVoltage = (pwmValue / 255.0) * 12.0; // Assuming 12V supply

        setFanSpeed(percentage);

        Serial.printf("Step %d: %d%% | PWM: %d/255 | Expected: %.2fV\n",
                     (percentage/10 + 1), percentage, pwmValue, expectedVoltage);
        Serial.println("  -> Measure actual voltage at L298N output now");
        Serial.println("  -> Record: Voltage = _____ V, RPM = _____ (if tachometer available)");
        Serial.println("  -> Press any key to continue to next step...");

        delay(5000); // 5 seconds for measurement
        Serial.println("");
    }

    setFanSpeed(0); // Stop fan
    Serial.println("Test completed. Fan stopped.");
    Serial.println("Compare measured voltages with expected values above.");
}

void updateLegacyVariables() {
    // Update legacy variables from electrowinningState
    const auto& sensorData = electrowinningState.getSensorData();
    const auto& outputControl = electrowinningState.getOutputControl();
    const auto& powerData = electrowinningState.getPowerData();
    const auto& processStatus = electrowinningState.getProcessStatus();
    const auto& limits = electrowinningState.getLimits();
    
    pH = sensorData.pH;
    temp = sensorData.temperature;
    gas = sensorData.gasLevel;
    // Don't overwrite pumpSpeed and fanSpeed - these are display settings loaded from preferences
    // Only update them if they are 0 (not loaded yet)
    if (pumpSpeed == 0) pumpSpeed = pumpSpeedSetting;
    if (fanSpeed == 0) fanSpeed = 70; // Default if not loaded
    voltage = (int)powerData.actualVoltage;
    current = powerData.actualCurrent;
    voltageSetting = powerData.targetVoltage;
    currentSetting = powerData.targetCurrent; // Keep in Amperes for consistency
    // Debug: Track isRunning state changes
    static bool lastIsRunning = false;
    static unsigned long lastDebugTime = 0;

    if (isRunning != processStatus.isRunning) {
        Serial.printf("*** isRunning STATE CHANGE: %s -> %s ***\n",
                     isRunning ? "true" : "false",
                     processStatus.isRunning ? "true" : "false");
        Serial.printf("    Source: updateLegacyVariables() at %lu ms\n", millis());
    }

    // Periodic debug every 5 seconds when running state is active
    if (millis() - lastDebugTime > 5000 && (isRunning || processStatus.isRunning)) {
        Serial.printf("State Monitor - Global isRunning: %s, ElectrowinningState: %s\n",
                     isRunning ? "true" : "false",
                     processStatus.isRunning ? "true" : "false");
        lastDebugTime = millis();
    }

    lastIsRunning = isRunning;
    isRunning = processStatus.isRunning;
    currentProfileIndex = processStatus.currentProfileIndex;
    profileCount = electrowinningState.getProfileCount();
    
    for (int i = 0; i < profileCount && i < 10; i++) {
        profiles[i] = electrowinningState.getProfile(i);
    }
    menuState = electrowinningState.getMenuState();

    // Update limits
    pHMin = limits.pHMin;
    pHMax = limits.pHMax;
    tempMin = limits.tempMin;
    tempMax = limits.tempMax;
    gasMin = limits.gasMin;
    gasMax = limits.gasMax;
}

void setup() {
    Serial.begin(115200);
    Serial.println("\n=== Electrowinning System with Remote Control ===");
    
    // Initialize preferences
    preferences.begin("electrowinning", false);
    settings.load();
    
    // Initialize electrowinning state
    electrowinningState.init(&xy, &preferences);

    // Initialize P&O Algorithm
    Serial.println("Initializing P&O Algorithm...");
    poAlgorithm.init(&filterManager, &electrowinningState, &xy);

    // Configure P&O parameters for electrowinning
    POParameters poParams;
    poParams.minVoltage = 5.0;          // Safe minimum voltage
    poParams.maxVoltage = 25.0;         // Safe maximum voltage
    poParams.initialStepSize = 0.5;     // Conservative initial step
    poParams.minStepSize = 0.1;         // Fine-tuning precision
    poParams.maxStepSize = 1.5;         // Maximum step for faster convergence
    poParams.baseEfficiency = 80.0;     // Expected base efficiency
    poParams.gasImpactFactor = 0.003;   // 0.3% efficiency loss per 1% gas
    poParams.convergenceThreshold = 0.3; // 0.3% efficiency change threshold
    poParams.convergenceCount = 5;      // 5 consecutive stable readings
    poParams.maxIterations = 30;        // Maximum optimization cycles
    poParams.perturbInterval = 8000;    // 8 seconds between perturbations
    poParams.observeInterval = 5000;    // 5 seconds observation time
    poParams.maxCurrentLimit = 18.0;    // Safety current limit
    poParams.maxPowerLimit = 400.0;     // Safety power limit
    poParams.maxGasLevel = 70.0;        // Safety gas level limit
    poParams.minPHLevel = 2.5;          // Safety pH minimum
    poParams.maxPHLevel = 11.5;         // Safety pH maximum
    poAlgorithm.setParameters(poParams);

    Serial.println("P&O Algorithm configured with electrowinning-optimized parameters");

    // Initialize Efficiency Calculator
    EfficiencyCalculator::EfficiencyParameters effParams;
    effParams.optimalGasLevel = 25.0;        // Optimal gas level for electrowinning
    effParams.maxGasLevel = 75.0;            // Maximum safe gas level
    effParams.gasImpactFactor = 0.02;        // 2% efficiency loss per 1% excess gas
    effParams.optimalPowerRange[0] = 150.0;  // Minimum optimal power
    effParams.optimalPowerRange[1] = 350.0;  // Maximum optimal power
    effParams.maxPowerLimit = 450.0;         // Maximum power limit
    effParams.optimalTempRange[0] = 20.0;    // Minimum optimal temperature
    effParams.optimalTempRange[1] = 40.0;    // Maximum optimal temperature
    effParams.optimalPHRange[0] = 6.0;       // Minimum optimal pH
    effParams.optimalPHRange[1] = 8.0;       // Maximum optimal pH
    effParams.gasWeight = 0.6;               // Gas level is most important (60%)
    effParams.powerWeight = 0.25;            // Power efficiency (25%)
    effParams.thermalWeight = 0.1;           // Temperature effect (10%)
    effParams.pHWeight = 0.05;               // pH effect (5%)
    efficiencyCalculator.setParameters(effParams);

    Serial.println("Efficiency Calculator configured for electrowinning optimization");

    // Initialize LCD and rotary encoder
    Serial.println("Initializing LCD...");
    lcd.init();
    lcd.backlight();
    lcd.setCursor(0, 0);
    lcd.print("Initializing...");

    Serial.println("Initializing Rotary Encoder...");
    Serial.printf("Encoder pins: A=%d, B=%d, Button=%d\n", ROTARY_ENCODER_A_PIN, ROTARY_ENCODER_B_PIN, ROTARY_ENCODER_BUTTON_PIN);

    rotaryEncoder.begin();
    rotaryEncoder.setup(readEncoderISR);
    rotaryEncoder.setAcceleration(250);

    // Test encoder reading
    int initialPos = rotaryEncoder.readEncoder();
    Serial.printf("Rotary encoder initialized successfully! Initial position: %d\n", initialPos);

    // Test pin states
    Serial.printf("Pin states - A(GPIO%d): %d, B(GPIO%d): %d, Button(GPIO%d): %d\n",
                 ROTARY_ENCODER_A_PIN, digitalRead(ROTARY_ENCODER_A_PIN),
                 ROTARY_ENCODER_B_PIN, digitalRead(ROTARY_ENCODER_B_PIN),
                 ROTARY_ENCODER_BUTTON_PIN, digitalRead(ROTARY_ENCODER_BUTTON_PIN));
    
    // Initialize motor control (L298N)
    Serial.println("Initializing Motor Control...");
    initializeMotorControl();

    // Initialize sensors (MQ-8, pH, Temperature)
    Serial.println("Initializing Sensors...");
    initializeSensors();

    // Debug DS18B20 specifically
    debugDS18B20();

    // Manual 1-Wire test
    manualOneWireTest();

    // Run sensor diagnostic
    runSensorDiagnostic();

    // Load profiles and settings
    loadProfilesFromPreferences();
    loadVoltageCurrentSettings();

    // Load data logger configuration
    loadDataLoggerConfig();

    // Sync loaded settings to electrowinning state
    electrowinningState.setTargetVoltage(voltageSetting);
    electrowinningState.setTargetCurrent(currentSetting);

    // Sync sensor limits to electrowinning state
    ProcessLimits limits;
    limits.pHMin = pHMin;
    limits.pHMax = pHMax;
    limits.tempMin = tempMin;
    limits.tempMax = tempMax;
    limits.gasMin = gasMin;
    limits.gasMax = gasMax;
    electrowinningState.setLimits(limits);

    Serial.printf("Synced settings to electrowinning state - Voltage: %.1fV, Current: %.1fA\n",
                 voltageSetting, currentSetting);
    Serial.printf("Synced sensor limits - pH: %.1f-%.1f, Temp: %.1f-%.1f°C, Gas: %.0f-%.0f%%\n",
                 pHMin, pHMax, tempMin, tempMax, gasMin, gasMax);

    updateEncoderBoundaries();
    
    // Initialize WiFi
    bool admin_mode = false;
    auto mr_detected = mrd.detectMultiReset();
    if (String(cfg.wifi_ssid) == "MY-SSID" || mr_detected) {
        Serial.println("ADMIN MODE!");
        WiFi.mode(WIFI_AP);
        WiFi.softAP(ACCESS_POINT_NAME, NULL);
        admin_mode = true;
        lcd.setCursor(0, 0);
        lcd.print("ADMIN MODE");
        lcd.setCursor(0, 1);
        lcd.print("AP: Electrowinning");
    } else {
        lcd.setCursor(0, 0);
        lcd.print("Connecting WiFi...");
        WiFi.begin(cfg.wifi_ssid, cfg.wifi_password);
        ts = millis();
        while (WiFi.status() != WL_CONNECTED) {
            mrd.loop();
            if (millis() > ts + 1000) {
                ts = millis();
                Serial.print(".");
            }
            yield();
        }
        Serial.println("WiFi connected!");
        lcd.clear();
        lcd.setCursor(0, 0);
        lcd.print("WiFi Connected");
        lcd.setCursor(0, 1);
        lcd.print(WiFi.localIP());
        delay(2000);

        // Configure NTP for accurate time
        Serial.println("Configuring NTP...");
        lcd.clear();
        lcd.setCursor(0, 0);
        lcd.print("Syncing Time...");

        // Configure timezone for Indonesia (WIB = UTC+7)
        configTime(7 * 3600, 0, "pool.ntp.org", "time.nist.gov");

        // Wait for time to be set
        int ntpRetries = 0;
        while (time(nullptr) < 8 * 3600 * 24 && ntpRetries < 15) {
            delay(1000);
            Serial.print(".");
            ntpRetries++;
        }

        if (time(nullptr) > 8 * 3600 * 24) {
            Serial.println("\nNTP time synchronized!");
            time_t now = time(nullptr);
            Serial.printf("Current time: %s", ctime(&now));
            lcd.setCursor(0, 1);
            lcd.print("Time Synced");
        } else {
            Serial.println("\nNTP sync failed, using system time");
            lcd.setCursor(0, 1);
            lcd.print("Time Sync Failed");
        }
        delay(2000);
    }

    // Initialize web server and MQTT
    webserver.init(admin_mode);
    mqtt.init(admin_mode);
    
    // Display initial screen
    Serial.println("Displaying initial screen...");
    displayMainScreen();
    displayNeedsUpdate = false; // SESUAI REFERENSI
    
    ts = millis();
    lastDisplayUpdate = millis();
    lastStateUpdate = millis();
    
    Serial.println("System initialized!");
}

void loop() {
    // XY6020 tasks (web/MQTT)
    webserver.task();
    mqtt.task();
    mrd.loop();
    xy.task();
    
    // Electrowinning state management
    if (millis() - lastStateUpdate > 100) {
        // Update smart automation filters first
        updateAllFilters();

        // Update P&O Algorithm for smart optimization
        poAlgorithm.update();

        // Update efficiency calculation with current sensor data
        const auto& sensorData = electrowinningState.getSensorData();
        const auto& powerData = electrowinningState.getPowerData();
        efficiencyCalculator.calculateEfficiency(
            sensorData.gasLevel,
            powerData.actualVoltage,
            powerData.actualCurrent,
            sensorData.temperature,
            sensorData.pH
        );

        electrowinningState.update();
        updateLegacyVariables();

        // Data logging - log current sensor data
        if (dataLogger.isEnabled()) {
            String status = isRunning ? "RUNNING" : "STOPPED";
            dataLogger.logData(pH, temp, gas, xy.actualVoltage(), xy.actualCurrent(), status);
        }

        lastStateUpdate = millis();
    }

    // Data logger auto-save update
    dataLogger.update();

    // Debug time status every 30 seconds
    static unsigned long lastTimeDebug = 0;
    if (millis() - lastTimeDebug > 30000) {
        Serial.printf("System Time: %s (NTP Sync: %s)\n",
                     getCurrentTimeString().c_str(),
                     isTimeSync() ? "OK" : "FAILED");
        lastTimeDebug = millis();
    }

    // Process calibration if active
    if (calibrationState.isCalibrating) {
        processPHCalibration();
    }
    
    // LCD interface tasks
    rotary_loop();

    // Debug: Print system status every 15 seconds (diperlambat)
    static unsigned long lastDebug = 0;
    if (millis() - lastDebug > 15000) {
        int encoderPos = rotaryEncoder.readEncoder();
        bool encoderChanged = rotaryEncoder.encoderChanged();
        bool buttonPressed = rotaryEncoder.isEncoderButtonClicked();

        Serial.printf("SYS: L%d I%d | ENC:%d | XY:%.1fV\n",
                     menuState.currentLevel, menuState.currentItem, encoderPos, xy.actualVoltage());
        lastDebug = millis();
    }
    
    // Update display - SESUAI menu_hierarchy_navigation_guide.md
    // Auto-refresh main screen every 2 seconds for real-time data
    static unsigned long lastMainScreenUpdate = 0;
    bool forceMainScreenUpdate = (menuState.currentLevel == LEVEL_STATUS &&
                                 millis() - lastMainScreenUpdate > 2000);

    if (displayNeedsUpdate || forceMainScreenUpdate) {
        switch (menuState.currentLevel) {
            case LEVEL_STATUS:
                if (forceMainScreenUpdate) lastMainScreenUpdate = millis();
            case LEVEL_START:
            case LEVEL_SETUP:
            case LEVEL_MANUAL_SETTINGS:
            case LEVEL_SENSOR_SETTINGS:
            case LEVEL_OUTPUT_SETTINGS:
            case LEVEL_EDIT_pH:
            case LEVEL_EDIT_Temp:
            case LEVEL_EDIT_Gas:
            case PumpSetting:
            case FanSetting:
            case VoltageControl:
            case CurrentControl:
            case userA:
            case NewUser:
            case LEVEL_ADD_USER:
                displayMainScreen(); // Uses template system
                break;
            case LEVEL_CONFIG_PROFILES:
                displayConfigProfilesMenu(); // Dynamic profile list
                break;
            case LEVEL_chooseProfile:
                displayChooseProfileMenu(); // Dynamic profile selection
                break;
            case LEVEL_CALIBRATION:
            case LEVEL_PH_CALIBRATION:
            case LEVEL_PH_CAL_4:
            case LEVEL_PH_CAL_7:
            case LEVEL_PH_CAL_9:
            case LEVEL_PH_CAL_RESET:
                // Special display for calibration screens
                if (calibrationState.isCalibrating) {
                    displayCalibrationScreen();
                } else {
                    displayMainScreen(); // Uses template system
                }
                break;
            case LEVEL_PH_CAL_VIEW:
                displayCalibrationData();
                break;
            default:
                displayMainScreen(); // Fallback to template
                break;
        }
        displayNeedsUpdate = false;
    }

    delay(100);
}

// ============================================================================
// Smart Automation Filter Functions
// ============================================================================

void updateAllFilters() {
    // Read raw sensor values
    float rawPower = xy.actualVoltage() * xy.actualCurrent();
    float rawPH = readRawPHSensor();
    float rawGas = readRawMQ8HydrogenLevel();
    float rawTemp = readRawTemperatureSensor();

    // Calculate basic efficiency (will be enhanced with P&O algorithm)
    float baseEfficiency = 80.0; // 80% base efficiency
    float gasImpact = rawGas * 0.003; // 0.3% reduction per 1% gas level
    float efficiency = baseEfficiency * (1.0 - gasImpact);
    efficiency = constrain(efficiency, 0.0, 100.0);

    // Update all filters
    filterManager.updateFilters(rawPower, rawPH, rawGas, rawTemp, efficiency);

    // Debug output every 5 seconds
    static unsigned long lastFilterDebug = 0;
    if (millis() - lastFilterDebug > 5000) {
        char statusBuffer[512];
        filterManager.getFilterStatus(statusBuffer, sizeof(statusBuffer));
        Serial.println("=== FILTER STATUS ===");
        Serial.println(statusBuffer);
        lastFilterDebug = millis();
    }
}

float getFilteredPower() {
    return filterManager.getFilteredPower();
}

float getFilteredPH() {
    return filterManager.getFilteredPH();
}

float getFilteredGas() {
    return filterManager.getFilteredGas();
}

float getFilteredTemperature() {
    return filterManager.getFilteredTemperature();
}

float getFilteredEfficiency() {
    // Return comprehensive efficiency from EfficiencyCalculator
    return efficiencyCalculator.getOverallEfficiency();
}

bool isSystemStableForOptimization() {
    return filterManager.isSystemStable();
}

// ============================================================================
// P&O Algorithm Control Functions
// ============================================================================

void enablePOAlgorithm() {
    if (!poAlgorithm.isEnabled()) {
        poAlgorithm.enable();
        Serial.println("P&O Algorithm ENABLED via user command");

        // Display status on LCD
        lcd.clear();
        lcd.setCursor(0, 0);
        lcd.print("P&O: ENABLED");
        lcd.setCursor(0, 1);
        lcd.print("Optimizing...");
        delay(2000);
        displayNeedsUpdate = true;
    } else {
        Serial.println("P&O Algorithm already enabled");
    }
}

void disablePOAlgorithm() {
    if (poAlgorithm.isEnabled()) {
        poAlgorithm.disable();
        Serial.println("P&O Algorithm DISABLED via user command");

        // Display status on LCD
        lcd.clear();
        lcd.setCursor(0, 0);
        lcd.print("P&O: DISABLED");
        lcd.setCursor(0, 1);
        lcd.print("Manual Mode");
        delay(2000);
        displayNeedsUpdate = true;
    } else {
        Serial.println("P&O Algorithm already disabled");
    }
}

void resetPOAlgorithm() {
    poAlgorithm.reset();
    Serial.println("P&O Algorithm RESET via user command");

    // Display status on LCD
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("P&O: RESET");
    lcd.setCursor(0, 1);
    lcd.print("Ready to start");
    delay(2000);
    displayNeedsUpdate = true;
}

void printPOStatus() {
    Serial.println("\n=== P&O ALGORITHM STATUS ===");

    const auto& status = poAlgorithm.getStatus();
    const char* stateNames[] = {"DISABLED", "WAITING_STABILITY", "INITIALIZING",
                               "PERTURBING", "OBSERVING", "CONVERGED", "SAFETY_HOLD"};

    Serial.printf("State: %s\n", stateNames[(int)status.state]);
    Serial.printf("Enabled: %s\n", poAlgorithm.isEnabled() ? "YES" : "NO");
    Serial.printf("Converged: %s\n", poAlgorithm.isConverged() ? "YES" : "NO");
    Serial.printf("Safety Triggered: %s\n", poAlgorithm.isSafetyTriggered() ? "YES" : "NO");

    if (poAlgorithm.isEnabled()) {
        Serial.printf("Current Voltage: %.2fV\n", status.currentVoltage);
        Serial.printf("Current Efficiency: %.2f%%\n", status.currentEfficiency);
        Serial.printf("Optimal Voltage: %.2fV\n", poAlgorithm.getOptimalVoltage());
        Serial.printf("Max Efficiency: %.2f%%\n", poAlgorithm.getMaxEfficiencyFound());
        Serial.printf("Efficiency Gain: %.2f%%\n", poAlgorithm.getEfficiencyGain());
        Serial.printf("Iterations: %d\n", poAlgorithm.getIterationCount());

        if (poAlgorithm.isSafetyTriggered()) {
            Serial.printf("Safety Reason: %s\n", status.lastSafetyReason.c_str());
        }
    }

    Serial.println("============================\n");
}

// ============================================================================
// Efficiency Calculator Control Functions
// ============================================================================

void printEfficiencyStatus() {
    efficiencyCalculator.printDebugInfo();
}

void resetEfficiencyCalculator() {
    efficiencyCalculator.reset();
    Serial.println("Efficiency Calculator RESET via user command");

    // Display status on LCD
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Efficiency: RESET");
    lcd.setCursor(0, 1);
    lcd.print("Recalibrating...");
    delay(2000);
    displayNeedsUpdate = true;
}

String getEfficiencyReport() {
    return efficiencyCalculator.getEfficiencyReport();
}

float getGasBasedEfficiency() {
    const auto& sensorData = electrowinningState.getSensorData();
    return efficiencyCalculator.getGasBasedEfficiency(sensorData.gasLevel);
}

float getPowerEfficiency() {
    const auto& powerData = electrowinningState.getPowerData();
    return efficiencyCalculator.getPowerEfficiency(powerData.actualVoltage, powerData.actualCurrent);
}

float getThermalEfficiency() {
    const auto& sensorData = electrowinningState.getSensorData();
    return efficiencyCalculator.getThermalEfficiency(sensorData.temperature);
}

float getPHEfficiency() {
    const auto& sensorData = electrowinningState.getSensorData();
    return efficiencyCalculator.getPHEfficiency(sensorData.pH);
}

float getEfficiencyTrend() {
    return efficiencyCalculator.getEfficiencyTrend();
}

float getProcessStability() {
    return efficiencyCalculator.getProcessStability();
}

// ============================================================================
// Smart Automation Testing Functions
// ============================================================================

void runSmartAutomationTests() {
    Serial.println("\n=== STARTING SMART AUTOMATION INTEGRATION TESTS ===");

    // Configure test parameters
    SmartAutomationTest::TestConfiguration testConfig;
    testConfig.enableVerboseOutput = true;
    testConfig.enablePerformanceTesting = true;
    testConfig.enableStressTest = false;
    testConfig.testIterations = 50; // Reduced for faster testing
    smartTest.setConfiguration(testConfig);

    // Display system info before testing
    Serial.println(SmartAutomationTest::getSystemInfo());

    // Run comprehensive tests
    bool allTestsPassed = smartTest.runAllTests();

    // Display results on LCD
    lcd.clear();
    lcd.setCursor(0, 0);
    if (allTestsPassed) {
        lcd.print("Tests: PASSED");
        lcd.setCursor(0, 1);
        lcd.printf("Success: %.1f%%", smartTest.getSuccessRate());
        Serial.println("✅ ALL SMART AUTOMATION TESTS PASSED!");
    } else {
        lcd.print("Tests: FAILED");
        lcd.setCursor(0, 1);
        lcd.printf("Success: %.1f%%", smartTest.getSuccessRate());
        Serial.println("❌ SOME SMART AUTOMATION TESTS FAILED!");
    }

    delay(5000); // Show results for 5 seconds
    displayNeedsUpdate = true;

    Serial.println("=== SMART AUTOMATION TESTING COMPLETE ===\n");
}

void runQuickSystemCheck() {
    Serial.println("\n=== QUICK SYSTEM CHECK ===");

    // Quick validation of all components
    bool filtersOK = smartTest.testMovingAverageFilters();
    bool poOK = smartTest.testPOAlgorithm();
    bool efficiencyOK = smartTest.testEfficiencyCalculator();
    bool integrationOK = smartTest.testSystemIntegration();

    Serial.printf("Quick Check Results:\n");
    Serial.printf("  Filters: %s\n", filtersOK ? "OK" : "FAIL");
    Serial.printf("  P&O Algorithm: %s\n", poOK ? "OK" : "FAIL");
    Serial.printf("  Efficiency Calculator: %s\n", efficiencyOK ? "OK" : "FAIL");
    Serial.printf("  System Integration: %s\n", integrationOK ? "OK" : "FAIL");

    bool allOK = filtersOK && poOK && efficiencyOK && integrationOK;
    Serial.printf("Overall System: %s\n", allOK ? "HEALTHY" : "ISSUES DETECTED");

    // Display on LCD
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print(allOK ? "System: HEALTHY" : "System: ISSUES");
    lcd.setCursor(0, 1);
    lcd.printf("Components: %d/4", filtersOK + poOK + efficiencyOK + integrationOK);

    delay(3000);
    displayNeedsUpdate = true;

    Serial.println("=== QUICK SYSTEM CHECK COMPLETE ===\n");
}

void printSmartAutomationStatus() {
    Serial.println("\n=== SMART AUTOMATION STATUS ===");

    // Filter Manager Status
    Serial.println("Filter Manager:");
    Serial.printf("  Gas Filter: %.2f%% (stable: %s)\n",
                 filterManager.getFilteredGas(),
                 filterManager.isSystemStable() ? "YES" : "NO");
    Serial.printf("  Power Filter: %.2fW\n", filterManager.getFilteredPower());
    Serial.printf("  Efficiency Filter: %.1f%%\n", filterManager.getFilteredEfficiency());

    // P&O Algorithm Status
    Serial.println("P&O Algorithm:");
    const auto& poStatus = poAlgorithm.getStatus();
    Serial.printf("  State: %s\n", poAlgorithm.isEnabled() ? "ENABLED" : "DISABLED");
    Serial.printf("  Iterations: %d\n", poStatus.iterationCount);
    Serial.printf("  Current Voltage: %.1fV\n", poStatus.currentVoltage);
    Serial.printf("  Current Efficiency: %.1f%%\n", poStatus.currentEfficiency);

    // Efficiency Calculator Status
    Serial.println("Efficiency Calculator:");
    const auto& effMetrics = efficiencyCalculator.getMetrics();
    Serial.printf("  Overall Efficiency: %.1f%%\n", effMetrics.overallEfficiency);
    Serial.printf("  Efficiency Trend: %.1f%%\n", effMetrics.efficiencyTrend);
    Serial.printf("  Process Stability: %.1f%%\n", effMetrics.processStability);
    Serial.printf("  Gas Impact Factor: %.3f\n", effMetrics.gasImpactFactor);

    // System Performance
    Serial.println("System Performance:");
    Serial.printf("  Free Heap: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("  CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("  Uptime: %lu seconds\n", millis() / 1000);

    Serial.println("===============================\n");
}