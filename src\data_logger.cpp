#include "data_logger.h"
#include <time.h>

DataLogger::DataLogger() {
    // Default configuration - Safe settings
    config.enabled = false;
    config.intervalMs = 1000;  // 1 second default (per detik)
    config.maxEntries = 100;   // Reasonable buffer size
    config.autoSave = false;   // Disabled by default
    config.saveIntervalMs = 300000; // 5 minutes

    lastLogTime = 0;
    lastSaveTime = 0;
    totalLoggedEntries = 0;

    // Generate session ID
    currentSessionId = "SES_" + String(millis());

    // Reserve memory for buffer
    logBuffer.reserve(100);

    Serial.println("DataLogger: Initialized");
}

void DataLogger::setConfig(const LogConfig& newConfig) {
    config = newConfig;
    
    // Adjust buffer size if needed
    if (config.maxEntries != logBuffer.capacity()) {
        setMaxEntries(config.maxEntries);
    }
    
    Serial.printf("DataLogger: Config updated - Enabled:%s, Interval:%lums, MaxEntries:%d\n",
                 config.enabled ? "YES" : "NO", config.intervalMs, config.maxEntries);
}

void DataLogger::setMaxEntries(int maxEntries) {
    config.maxEntries = maxEntries;
    
    // If current buffer is larger, trim it
    if (logBuffer.size() > maxEntries) {
        // Keep the most recent entries
        int toRemove = logBuffer.size() - maxEntries;
        logBuffer.erase(logBuffer.begin(), logBuffer.begin() + toRemove);
    }
    
    // Reserve new capacity
    logBuffer.reserve(maxEntries);
}

void DataLogger::logData(float pH, float temp, float gas, float voltage, float current,
                        const String& status, const String& notes) {
    if (!config.enabled) {
        return;
    }

    // Check if enough time has passed
    unsigned long currentTime = millis();
    if (currentTime - lastLogTime < config.intervalMs) {
        return;
    }

    // Create new log entry
    LogEntry entry;
    // Use real-time timestamp (Unix timestamp in seconds)
    time_t now = time(nullptr);
    if (now > 1000000000) { // Valid Unix timestamp
        entry.timestamp = now;
    } else {
        // Fallback to millis() if NTP not synced
        entry.timestamp = currentTime;
    }

    entry.pH = pH;
    entry.temperature = temp;
    entry.gasLevel = gas;
    entry.voltage = voltage;
    entry.current = current;
    entry.power = voltage * current;
    entry.status = status;
    entry.notes = notes;
    
    // Add to buffer
    logBuffer.push_back(entry);
    totalLoggedEntries++;
    lastLogTime = currentTime;
    
    // Remove old entries if buffer is full
    if (logBuffer.size() > config.maxEntries) {
        logBuffer.erase(logBuffer.begin());
    }
    
    // Debug output
    if (totalLoggedEntries % 10 == 0) { // Every 10th entry
        Serial.printf("DataLogger: Logged entry #%d, Buffer size: %d, Time: %s\n",
                     totalLoggedEntries, logBuffer.size(), getFormattedDateTime(entry.timestamp).c_str());
    }
}

void DataLogger::update() {
    if (!config.enabled || !config.autoSave) {
        return;
    }
    
    // Auto-save check
    unsigned long currentTime = millis();
    if (currentTime - lastSaveTime > config.saveIntervalMs && logBuffer.size() > 0) {
        saveToFile();
        lastSaveTime = currentTime;
    }
}

DataLogger::LogEntry DataLogger::getLogEntry(int index) const {
    if (index < 0 || index >= logBuffer.size()) {
        return LogEntry(); // Return empty entry
    }
    return logBuffer[index];
}

String DataLogger::getLogSummary() const {
    String summary = "=== DATA LOGGER SUMMARY ===\n";
    summary += "Status: " + String(config.enabled ? "ENABLED" : "DISABLED") + "\n";
    summary += "Session ID: " + currentSessionId + "\n";
    summary += "Interval: " + String(config.intervalMs / 1000.0, 1) + " seconds\n";
    summary += "Buffer Size: " + String(logBuffer.size()) + "/" + String(config.maxEntries) + "\n";
    summary += "Total Logged: " + String(totalLoggedEntries) + "\n";
    summary += "Auto Save: " + String(config.autoSave ? "ON" : "OFF") + "\n";
    if (config.autoSave) {
        summary += "Save Interval: " + String(config.saveIntervalMs / 60000.0, 1) + " minutes\n";
    }
    summary += "Last Log: " + String((millis() - lastLogTime) / 1000) + " seconds ago\n";
    summary += "===========================\n";
    
    return summary;
}

std::vector<DataLogger::LogEntry> DataLogger::getRecentLogs(int count) const {
    std::vector<LogEntry> recent;
    
    int startIndex = max(0, (int)logBuffer.size() - count);
    for (int i = startIndex; i < logBuffer.size(); i++) {
        recent.push_back(logBuffer[i]);
    }
    
    return recent;
}

bool DataLogger::saveToFile() {
    if (logBuffer.empty()) {
        return false;
    }
    
    String filename = generateFilename();
    return saveBufferToFile(filename);
}

bool DataLogger::saveToCSV(const String& filename) {
    String csvFile = filename.length() > 0 ? filename : generateFilename();
    if (!csvFile.endsWith(".csv")) {
        csvFile += ".csv";
    }
    return saveBufferToFile(csvFile);
}

String DataLogger::generateFilename() const {
    // Format: logs_YYYYMMDD_HHMMSS.csv
    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);
    
    char buffer[32];
    strftime(buffer, sizeof(buffer), "logs_%Y%m%d_%H%M%S.csv", timeinfo);
    
    return String(buffer);
}

String DataLogger::getFormattedDateTime(unsigned long timestamp) const {
    time_t rawtime;

    // Check if timestamp is a valid Unix timestamp (seconds since 1970)
    if (timestamp > 1000000000UL && timestamp < 4000000000UL) {
        // Valid Unix timestamp in seconds
        rawtime = timestamp;
    } else if (timestamp > 1000000000000UL) {
        // Timestamp is in milliseconds, convert to seconds
        rawtime = timestamp / 1000;
    } else {
        // Timestamp is from millis() (system uptime), convert to real time
        time_t now = time(nullptr);
        if (now > 1000000000) {
            // NTP is synced, calculate real time
            unsigned long currentMillis = millis();
            if (timestamp <= currentMillis) {
                unsigned long elapsedSeconds = (currentMillis - timestamp) / 1000;
                rawtime = now - elapsedSeconds;
            } else {
                rawtime = now;
            }
        } else {
            // NTP not synced, use timestamp as-is (will show as 1970 date)
            rawtime = timestamp / 1000;
        }
    }

    struct tm* timeinfo = localtime(&rawtime);

    char buffer[32];
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);

    return String(buffer);
}

void DataLogger::clearBuffer() {
    logBuffer.clear();
    Serial.println("DataLogger: Buffer cleared");
}

bool DataLogger::saveBufferToFile(const String& filename) {
    if (logBuffer.empty()) {
        Serial.println("DataLogger: No data to save");
        return false;
    }

    // Check SPIFFS is mounted
    if (!SPIFFS.begin(true)) {
        Serial.println("DataLogger: SPIFFS mount failed");
        return false;
    }

    // Save to root directory
    String filepath = "/" + filename;
    File file = SPIFFS.open(filepath, "w");
    if (!file) {
        Serial.printf("DataLogger: Failed to create file: %s\n", filepath.c_str());
        return false;
    }

    // Write CSV header
    file.println("Timestamp,DateTime,pH,Temperature,GasLevel,Voltage,Current,Power,Status,Notes");

    // Write data entries
    for (const auto& entry : logBuffer) {
        file.println(formatLogEntry(entry, true));
    }

    file.close();

    Serial.printf("DataLogger: Saved %d entries to: %s\n", logBuffer.size(), filepath.c_str());
    return true;
}

String DataLogger::formatLogEntry(const LogEntry& entry, bool isCSV) const {
    if (isCSV) {
        return String(entry.timestamp) + "," +
               getFormattedDateTime(entry.timestamp) + "," +
               String(entry.pH, 2) + "," +
               String(entry.temperature, 1) + "," +
               String(entry.gasLevel, 1) + "," +
               String(entry.voltage, 2) + "," +
               String(entry.current, 2) + "," +
               String(entry.power, 2) + "," +
               entry.status + "," +
               entry.notes;
    } else {
        return "[" + getFormattedDateTime(entry.timestamp) + "] " +
               "pH:" + String(entry.pH, 2) + " " +
               "T:" + String(entry.temperature, 1) + "°C " +
               "Gas:" + String(entry.gasLevel, 1) + "% " +
               "V:" + String(entry.voltage, 2) + "V " +
               "I:" + String(entry.current, 2) + "A " +
               "P:" + String(entry.power, 2) + "W " +
               "Status:" + entry.status +
               (entry.notes.length() > 0 ? " Notes:" + entry.notes : "");
    }
}

String DataLogger::getLogsAsJSON(int limit, int offset) const {
    JsonDocument doc;

    int totalEntries = logBuffer.size();

    // For recent logs, we want the most recent entries first
    // So we'll take from the end of the buffer
    int actualLimit = min(limit, totalEntries);
    int startIndex = max(0, totalEntries - actualLimit - offset);
    int endIndex = totalEntries - offset;

    if (startIndex < 0) startIndex = 0;
    if (endIndex > totalEntries) endIndex = totalEntries;
    if (startIndex >= endIndex) {
        startIndex = 0;
        endIndex = 0;
    }

    doc["total"] = totalEntries;
    doc["offset"] = offset;
    doc["limit"] = limit;
    doc["returned"] = endIndex - startIndex;

    JsonArray entries = doc["entries"].to<JsonArray>();

    // Add entries in reverse order (most recent first)
    for (int i = endIndex - 1; i >= startIndex; i--) {
        const LogEntry& entry = logBuffer[i];
        JsonObject entryObj = entries.add<JsonObject>();
        entryObj["timestamp"] = entry.timestamp;
        entryObj["datetime"] = getFormattedDateTime(entry.timestamp);
        entryObj["pH"] = entry.pH;
        entryObj["temperature"] = entry.temperature;
        entryObj["gasLevel"] = entry.gasLevel;
        entryObj["voltage"] = entry.voltage;
        entryObj["current"] = entry.current;
        entryObj["power"] = entry.power;
        entryObj["status"] = entry.status;
        entryObj["notes"] = entry.notes;
    }

    String result;
    serializeJson(doc, result);
    return result;
}

String DataLogger::getLogsAsCSV() const {
    String csv = "Timestamp,DateTime,pH,Temperature,GasLevel,Voltage,Current,Power,Status,Notes\n";

    for (const LogEntry& entry : logBuffer) {
        csv += String(entry.timestamp) + ",";
        csv += getFormattedDateTime(entry.timestamp) + ",";
        csv += String(entry.pH, 2) + ",";
        csv += String(entry.temperature, 1) + ",";
        csv += String(entry.gasLevel, 1) + ",";
        csv += String(entry.voltage, 2) + ",";
        csv += String(entry.current, 2) + ",";
        csv += String(entry.power, 2) + ",";
        csv += entry.status + ",";
        csv += entry.notes + "\n";
    }

    return csv;
}

String DataLogger::getConfigAsJSON() const {
    JsonDocument doc;
    doc["enabled"] = config.enabled;
    doc["interval_ms"] = config.intervalMs;
    doc["interval_seconds"] = config.intervalMs / 1000.0;
    doc["max_entries"] = config.maxEntries;
    doc["auto_save"] = config.autoSave;
    doc["save_interval_ms"] = config.saveIntervalMs;
    doc["save_interval_minutes"] = config.saveIntervalMs / 60000.0;
    doc["current_entries"] = logBuffer.size();
    doc["total_logged"] = totalLoggedEntries;
    doc["session_id"] = currentSessionId;

    String result;
    serializeJson(doc, result);
    return result;
}

bool DataLogger::setConfigFromJSON(const String& jsonStr) {
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, jsonStr);

    if (error) {
        Serial.printf("DataLogger: JSON parse error: %s\n", error.c_str());
        return false;
    }

    LogConfig newConfig = config; // Start with current config

    if (doc["enabled"].is<bool>()) {
        newConfig.enabled = doc["enabled"];
    }
    if (doc["interval_ms"].is<unsigned long>()) {
        newConfig.intervalMs = doc["interval_ms"];
    }
    if (doc["interval_seconds"].is<float>()) {
        newConfig.intervalMs = doc["interval_seconds"].as<float>() * 1000;
    }
    if (doc["max_entries"].is<int>()) {
        newConfig.maxEntries = doc["max_entries"];
    }
    if (doc["auto_save"].is<bool>()) {
        newConfig.autoSave = doc["auto_save"];
    }
    if (doc["save_interval_ms"].is<unsigned long>()) {
        newConfig.saveIntervalMs = doc["save_interval_ms"];
    }
    if (doc["save_interval_minutes"].is<float>()) {
        newConfig.saveIntervalMs = doc["save_interval_minutes"].as<float>() * 60000;
    }

    setConfig(newConfig);
    return true;
}

bool DataLogger::saveToJSON(const String& filename) {
    String jsonFile = filename.length() > 0 ? filename : generateFilename();
    if (!jsonFile.endsWith(".json")) {
        jsonFile += ".json";
    }

    // Create JSON document
    JsonDocument doc;
    doc["session_id"] = currentSessionId;
    doc["timestamp"] = millis();
    doc["total_entries"] = totalLoggedEntries;
    doc["config"]["interval_ms"] = config.intervalMs;
    doc["config"]["max_entries"] = config.maxEntries;

    JsonArray entries = doc["entries"].to<JsonArray>();
    for (const auto& entry : logBuffer) {
        JsonObject entryObj = entries.add<JsonObject>();
        entryObj["timestamp"] = entry.timestamp;
        entryObj["datetime"] = getFormattedDateTime(entry.timestamp);
        entryObj["pH"] = entry.pH;
        entryObj["temperature"] = entry.temperature;
        entryObj["gasLevel"] = entry.gasLevel;
        entryObj["voltage"] = entry.voltage;
        entryObj["current"] = entry.current;
        entryObj["power"] = entry.power;
        entryObj["status"] = entry.status;
        entryObj["notes"] = entry.notes;
    }

    // Save to file
    File file = SPIFFS.open("/" + jsonFile, "w");
    if (!file) {
        Serial.printf("DataLogger: Failed to create JSON file: %s\n", jsonFile.c_str());
        return false;
    }

    serializeJson(doc, file);
    file.close();

    Serial.printf("DataLogger: Saved %d entries to JSON: %s\n", logBuffer.size(), jsonFile.c_str());
    return true;
}
