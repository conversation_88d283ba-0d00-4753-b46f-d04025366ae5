# 📋 Smart Electrowinning Automation Algorithm Documentation

## 🎯 **Overview**

Sistem automasi elektrowinning menggunakan **Modified Perturb and Observe (P&O) Algorithm** yang dikombinasikan dengan **Multi-Layer Safety System** untuk mengoptimalkan efisiensi dan menjaga keamanan operasi secara otomatis.

## 🔧 **1. Moving Average Filter Structure**

### **Konsep:**
Filter untuk menghaluskan data sensor dan mengurangi noise, memberikan stabilitas pada sistem kontrol.

### **Simple Moving Average (SMA):**
```cpp
struct MovingAverageFilter {
    float* buffer;           // Buffer circular untuk menyimpan data
    int bufferSize;         // Ukuran buffer (5-20 samples)
    int currentIndex;       // Index saat ini dalam buffer
    float sum;              // Total nilai dalam buffer
    bool bufferFull;        // Flag apakah buffer sudah penuh
};
```

### **Rumus <PERSON>an:**
```
SMA(n) = (x₁ + x₂ + ... + xₙ) / n

Dimana:
- n = jumlah sample dalam buffer
- x₁, x₂, ..., xₙ = nilai-nilai sample
```

### **Implementasi addSample():**
```cpp
float addSample(float newSample) {
    sum -= buffer[currentIndex];     // Kurangi nilai lama
    buffer[currentIndex] = newSample; // Masukkan nilai baru
    sum += newSample;                // Update total
    
    currentIndex = (currentIndex + 1) % bufferSize; // Circular buffer
    if (currentIndex == 0) bufferFull = true;
    
    int count = bufferFull ? bufferSize : currentIndex;
    return count > 0 ? sum / count : 0.0;
}
```

### **Stability Check:**
```cpp
bool isStable(float threshold = 0.05) {
    float avg = getAverage();
    for (int i = 0; i < bufferSize; i++) {
        if (|buffer[i] - avg| > threshold × avg) {
            return false;  // Tidak stabil jika ada nilai > 5% dari rata-rata
        }
    }
    return true;  // Stabil jika semua nilai dalam threshold
}
```

## 📊 **2. Exponential Moving Average (EMA) Filter**

### **Konsep:**
Filter yang memberikan bobot lebih tinggi pada data terbaru, responsif terhadap perubahan tapi tetap smooth.

### **Rumus:**
```
EMA(t) = α × x(t) + (1-α) × EMA(t-1)

Dimana:
- α = smoothing factor (0.1 = 10% weight untuk data baru)
- x(t) = nilai baru
- EMA(t-1) = EMA sebelumnya
```

### **Implementasi:**
```cpp
struct EMAFilter {
    float alpha = 0.1;      // Smoothing factor
    float value;            // Nilai EMA saat ini
    bool initialized;       // Flag inisialisasi
    
    float addSample(float newSample) {
        if (!initialized) {
            value = newSample;           // Inisialisasi dengan nilai pertama
            initialized = true;
        } else {
            value = alpha * newSample + (1.0 - alpha) * value;
        }
        return value;
    }
};
```

## ⚡ **3. Modified Perturb and Observe (P&O) Algorithm**

### **Konsep Dasar:**
Algoritma optimasi yang mencari titik daya optimal dengan cara:
1. **Perturb** - Mengubah tegangan sedikit (±0.5V)
2. **Observe** - Mengamati perubahan daya yang dihasilkan
3. **Decide** - Menentukan arah perubahan selanjutnya

### **Parameter Utama:**
```cpp
struct ModifiedPOAlgorithm {
    float currentVoltage = 12.0V;    // Tegangan saat ini
    float currentCurrent = 2.0A;     // Arus saat ini
    float voltageStep = 0.5V;        // Langkah perubahan tegangan
    float minVoltage = 8.0V;         // Batas minimum
    float maxVoltage = 24.0V;        // Batas maksimum
    float maxPowerLimit = 200.0W;    // Batas daya maksimum
    
    enum PODirection { INCREASE, DECREASE, HOLD };
    PODirection lastDirection;        // Arah perubahan terakhir
    int holdCounter;                 // Counter untuk hold state
    int maxHoldCycles = 5;           // Maksimum hold cycles
};
```

### **Algoritma P&O:**
```cpp
POResult performPO(float actualVoltage, float actualCurrent, float gasLevel) {
    // 1. Hitung daya saat ini
    currentPower = actualVoltage × actualCurrent;
    
    // 2. Filter daya dengan moving average
    float avgPower = powerFilter->addSample(currentPower);
    
    // 3. Hitung efisiensi berdasarkan gas level
    float efficiency = calculateEfficiency(currentPower, gasLevel);
    
    // 4. Cek stabilitas sistem
    if (!isSystemStable()) {
        return HOLD;  // Tunggu sampai stabil
    }
    
    // 5. Hitung perubahan daya
    float powerDelta = avgPower - previousPower;
    
    // 6. Algoritma keputusan P&O
    if (|powerDelta| < 0.5W) {
        return OPTIMAL;  // Titik optimal tercapai
    } else if (powerDelta > 0) {
        // Daya meningkat, lanjutkan arah yang sama
        if (lastDirection == INCREASE) {
            currentVoltage += voltageStep;
        } else {
            currentVoltage -= voltageStep;
        }
    } else {
        // Daya menurun, balik arah
        if (lastDirection == INCREASE) {
            currentVoltage -= voltageStep;
            lastDirection = DECREASE;
        } else {
            currentVoltage += voltageStep;
            lastDirection = INCREASE;
        }
    }
    
    // 7. Apply voltage limits
    currentVoltage = constrain(currentVoltage, minVoltage, maxVoltage);
    
    // 8. Update previous power
    previousPower = avgPower;
    
    return result;
}
```

### **Efficiency Calculation:**
```cpp
float calculateEfficiency(float power, float gasLevel) {
    // Efisiensi berbanding terbalik dengan produksi gas H₂
    float gasEfficiencyFactor = 1.0 - (gasLevel / 100.0) * 0.3;
    gasEfficiencyFactor = constrain(gasEfficiencyFactor, 0.1, 1.0);
    
    float baseEfficiency = 0.8;  // 80% efisiensi dasar
    return baseEfficiency * gasEfficiencyFactor;
}
```

**Penjelasan:**
- **Gas Level Tinggi** → Efisiensi rendah (energi terbuang untuk elektrolisis air)
- **Gas Level Rendah** → Efisiensi tinggi (energi fokus untuk pengendapan emas)
- **Maximum Penalty** = 30% jika gas level 100%

## 🛡️ **4. Safety System dengan Hysteresis**

### **Konsep Safety Margins:**
Safety system menggunakan setpoint user dengan margin keamanan yang dapat dikonfigurasi.

### **Parameter Safety:**
```cpp
struct SafetySystem {
    // Safety margins (% beyond setpoint limits)
    float pHSafetyMargin = 10.0%;        // pH margin
    float gasSafetyMargin = 20.0%;       // Gas margin
    float tempSafetyMargin = 15.0%;      // Temperature margin
    float powerSafetyMargin = 25.0%;     // Power margin
    
    // Alarm delays (ms)
    unsigned long pHAlarmDelay = 5000;      // 5 detik
    unsigned long gasAlarmDelay = 3000;     // 3 detik (critical)
    unsigned long tempAlarmDelay = 10000;   // 10 detik
    unsigned long powerAlarmDelay = 2000;   // 2 detik
    
    // Hysteresis values
    float pHHysteresis = 0.2;
    float gasHysteresis = 5.0;
};
```

### **Rumus Safety Limits:**
```cpp
// pH Safety Calculation
pHRange = pHMax - pHMin;
pHSafeMin = pHMin - (pHRange × pHSafetyMargin / 100);
pHSafeMax = pHMax + (pHRange × pHSafetyMargin / 100);
pHCriticalMin = pHMin - (pHRange × pHSafetyMargin × 2 / 100);
pHCriticalMax = pHMax + (pHRange × pHSafetyMargin × 2 / 100);

// Gas Safety Calculation
gasRange = gasMax - gasMin;
gasSafeMax = gasMax + (gasRange × gasSafetyMargin / 100);
gasCritical = gasMax + (gasRange × gasSafetyMargin × 2 / 100);

// Power Safety Calculation
powerSafeMax = voltageSetting × currentSetting × (1 + powerSafetyMargin / 100);
currentSafeMax = currentSetting × (1 + powerSafetyMargin / 100);
```

### **Contoh Perhitungan:**
Jika setpoint pH = 6.0-8.0 dan safety margin = 10%:
```
pHRange = 8.0 - 6.0 = 2.0
pHSafeMin = 6.0 - (2.0 × 0.1) = 5.8
pHSafeMax = 8.0 + (2.0 × 0.1) = 8.2
pHCriticalMin = 6.0 - (2.0 × 0.2) = 5.6
pHCriticalMax = 8.0 + (2.0 × 0.2) = 8.4
```

### **Safety Check Algorithm:**
```cpp
SafetyResult checkSafety(float pH, float gasLevel, float temperature, float power, float current) {
    // 1. Filter sensor readings
    float avgPH = pHFilter->addSample(pH);
    float avgGas = gasFilter->addSample(gasLevel);
    float avgPower = powerFilter->addSample(power);
    
    // 2. Calculate safety limits based on current setpoints
    calculateSafetyLimits();
    
    // 3. Check pH safety with hysteresis and timing
    if (avgPH < pHCriticalMin || avgPH > pHCriticalMax) {
        if (!pHAlarmActive) {
            pHAlarmActive = true;
            pHAlarmStartTime = millis();
            return WARNING;
        } else if (millis() - pHAlarmStartTime > pHAlarmDelay) {
            return CRITICAL_STOP;  // Emergency stop after delay
        }
    }
    
    // 4. Similar checks for gas, temperature, and power
    // 5. Return appropriate safety action
}
```

### **Power Reduction Algorithm:**
```cpp
// Reduction factors based on safety condition
pH Warning:    reductionFactor = 0.3   (30% reduction)
Gas Warning:   reductionFactor = 0.5   (50% reduction)
Temp Warning:  reductionFactor = 0.2   (20% reduction)
Power Limit:   reductionFactor = 0.25  (25% reduction)

// Apply reduction
V_safe = V_target × (1 - reductionFactor);
I_safe = I_target × (1 - reductionFactor);
```

## 🔄 **5. Process Controller State Machine**

### **Process States:**
```cpp
enum ProcessState {
    STOPPED,        // Siap untuk start
    STARTING,       // Startup sequence aktif
    RUNNING,        // Operasi normal
    OPTIMIZING,     // P&O algorithm aktif
    WARNING,        // Safety warning, power reduced
    EMERGENCY_STOP  // Critical stop, manual reset required
};
```

### **State Transitions:**
```
STOPPED → STARTING:     Manual start + safety OK
STARTING → RUNNING:     Startup sequence complete + stable
RUNNING → OPTIMIZING:   Optimization interval (30s)
OPTIMIZING → RUNNING:   Optimization complete
ANY → WARNING:          Safety warning detected
ANY → EMERGENCY_STOP:   Critical safety condition
EMERGENCY_STOP → STOPPED: Manual reset only
```

### **Startup Sequence:**
```cpp
Step 0: Pre-ventilation (10s)    - Fan running untuk safety
Step 1: Pump circulation (5s)    - Start pump, low speed
Step 2: Power ramp (15s)         - Gradual voltage increase
Step 3: Stabilization (10s)      - Check stability before running

Total startup time: 40 seconds maximum
```

### **Power Ramp Function:**
```cpp
float rampProgress = min(1.0f, stepDuration / 15000.0f);  // 15 detik
V_ramp = V_start + (V_target - V_start) × rampProgress;
I_ramp = I_start + (I_target - I_start) × rampProgress;

Ramp profile:
t=0s:   V=8V,  I=1A    (Start safe values)
t=7.5s: V=10V, I=1.5A  (50% progress)
t=15s:  V=12V, I=2A    (Target values)
```

## 📊 **6. Performance Metrics Calculation**

### **Energy Consumption:**
```cpp
// Continuous energy integration
E_total = Σ(P(t) × Δt) / 3600  // Watt-hours

Dimana:
- P(t) = Power pada waktu t (Watts)
- Δt = Time interval (seconds)
- 3600 = Konversi detik ke jam
```

### **Average Efficiency:**
```cpp
η_avg = (Σ η(t)) / n

Dimana:
- η(t) = Efisiensi pada waktu t (%)
- n = Jumlah sample efisiensi
```

### **Energy Rate:**
```cpp
Energy_Rate = E_total / (process_time / 3600)  // Wh/hour

Performance targets:
- Efficiency: >80%
- Energy Rate: Optimized berdasarkan kondisi
- Stability: <5% variation dalam 1 menit
```

## ⏱️ **7. Timing dan Update Frequencies**

### **System Update Rates:**
```cpp
Safety Check:        100ms   (10 Hz)  - Critical untuk proteksi
P&O Optimization:    30s     (0.033 Hz) - Cukup untuk stabilisasi
Display Update:      500ms   (2 Hz)   - Smooth untuk user
Status Logging:      10s     (0.1 Hz) - Monitoring
Performance Calc:    1s      (1 Hz)   - Real-time metrics
Sensor Reading:      100ms   (10 Hz)  - Responsive
```

### **Optimization Intervals:**
```cpp
optimizationInterval = 30000ms;  // 30 detik antar optimasi
maxOptimizationTime = 60000ms;   // Maksimum 1 menit per siklus
stabilizationTime = 10000ms;     // 10 detik untuk stabilisasi
```

## 🎯 **8. Skenario Operasi**

### **Skenario Normal Operation:**
```
1. Startup: V=8V, I=1A, P=8W
2. Ramp (15s): V=8V→12V, I=1A→2A, P=8W→24W
3. Stabilization (10s): Monitor stability
4. P&O Cycle 1: V=12V→12.5V, P=24W→25W (naik) ✓
5. P&O Cycle 2: V=12.5V→13V, P=25W→24.5W (turun) ✗
6. P&O Cycle 3: V=13V→12.5V, P=24.5W→25W (optimal)
7. Maintenance: V=12.5V, I=2A, P=25W, η=85%
```

### **Skenario Gas Warning:**
```
1. Normal: V=12V, I=2A, P=24W, Gas=60%
2. Gas increase: Gas=85% (>80% safe limit)
3. Warning triggered: Apply 50% power reduction
4. Reduced: V=6V, I=1A, P=6W
5. Fan speed: Increase to 90% untuk ventilasi
6. Gas decrease: Gas=75% (<80% limit)
7. Recovery: Gradual return ke normal operation
```

### **Skenario pH Critical:**
```
1. Normal: pH=7.0 (dalam range 6.0-8.0)
2. pH drop: pH=5.5 (masuk critical zone <5.6)
3. Alarm start: 5 detik countdown
4. Emergency stop: Output disabled, pump stop
5. Fan continue: 100% speed untuk safety
6. Manual reset: Required untuk restart
7. Check solution: Sebelum restart
```

## 🔧 **9. Parameter Tuning Guidelines**

### **P&O Algorithm Tuning:**
```cpp
voltageStep:
- 0.1V = Very fine, slow convergence
- 0.5V = Balanced (recommended)
- 1.0V = Fast but may overshoot

optimizationInterval:
- 15s = Aggressive, may be unstable
- 30s = Balanced (recommended)
- 60s = Conservative, slow response
```

### **Safety Margin Tuning:**
```cpp
pHSafetyMargin:
- 5% = Tight control, frequent warnings
- 10% = Balanced (recommended)
- 20% = Very safe, less optimization

gasSafetyMargin:
- 10% = Aggressive operation
- 20% = Balanced (recommended)
- 30% = Very conservative
```

### **Filter Size Tuning:**
```cpp
Moving Average Buffer Size:
- 5 samples = Fast response, more noise
- 10 samples = Balanced (recommended)
- 20 samples = Very smooth, slow response

Power Filter: 10 samples (1 second @ 10Hz)
pH Filter: 10 samples (1 second @ 10Hz)
Gas Filter: 5 samples (0.5 second @ 10Hz)
```

## 📈 **10. Algorithm Performance Metrics**

### **Convergence Metrics:**
```cpp
Optimization Speed:
- Time to optimal: <2 minutes typical
- Stability time: <30 seconds
- Overshoot: <10% of target

Safety Response:
- Warning detection: <1 second
- Power reduction: <2 seconds
- Emergency stop: <3 seconds
- Recovery time: <1 minute
```

### **Efficiency Metrics:**
```cpp
Target Performance:
- System efficiency: >80%
- Power stability: ±5%
- pH stability: ±0.2 units
- Gas level control: ±10%
- Temperature stability: ±2°C
```

## 🎉 **Algorithm Summary**

Smart Electrowinning Automation menggunakan pendekatan multi-layer:

1. **Moving Average Filters** - Stabilitas dan noise reduction
2. **Modified P&O Algorithm** - Optimasi efisiensi otomatis
3. **Safety System** - Proteksi multi-level dengan hysteresis
4. **State Machine** - Kontrol proses yang terstruktur
5. **Performance Monitoring** - Real-time metrics dan logging

Semua algoritma bekerja berdasarkan **user setpoints** dengan **configurable safety margins**, memastikan operasi yang aman, efisien, dan dapat diandalkan untuk proses elektrowinning emas.

### **Key Benefits:**
- ✅ **Automatic Optimization** - Mencari titik efisiensi optimal
- ✅ **Safety First** - Multi-layer protection system
- ✅ **User-Friendly** - Smart defaults, minimal configuration
- ✅ **Robust Operation** - Stable under varying conditions
- ✅ **Performance Tracking** - Comprehensive metrics dan logging
