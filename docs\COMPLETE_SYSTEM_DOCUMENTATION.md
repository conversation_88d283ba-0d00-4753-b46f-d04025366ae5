# 📋 Electrowinning System - Complete Documentation

## 🎯 System Overview

Sistem Electrowinning dengan Remote Control adalah sistem monitoring dan kontrol terintegrasi yang menggabungkan:
- **XY6020 Power Supply Control** - Kontrol tegangan dan arus
- **Real-time Sensor Monitoring** - pH, Temperature, Gas Level
- **Data Logging System** - Pencatatan data otomatis
- **Web Interface** - Monitoring dan kontrol via browser
- **LCD Menu System** - Interface lokal dengan rotary encoder

## 🔧 Hardware Components

### **Main Controller**
- **ESP32 Development Board** - Mikrokontroller utama
- **XY6020 Power Supply** - Sumber daya yang dikontrol via Modbus
- **LCD 20x4 I2C** - Display lokal (Address: 0x27)
- **Rotary Encoder** - Input interface (Pins: 32, 33, 25)

### **Sensors**
- **pH Sensor** - GPIO36 (VP) - Analog input
- **DS18B20 Temperature** - GPIO34 - Digital 1-Wire
- **MQ-8 Hydrogen Gas** - GPIO39 (VN) - Analog input

### **Motor Control**
- **Pump Motor** - PWM control via L298N
- **Fan Motor** - PWM control via L298N

## 📊 Data Logging System

### **Features**
- ✅ **Real-time Data Recording** - pH, Temperature, Gas, Voltage, Current, Power
- ✅ **Configurable Intervals** - 1-300 detik (default: 1 detik)
- ✅ **Multiple Export Formats** - CSV dan JSON
- ✅ **Web Integration** - Terintegrasi dengan charts.html
- ✅ **Auto-save Functionality** - Penyimpanan otomatis ke SPIFFS
- ✅ **Buffer Management** - Configurable buffer size (10-1000 entries)

### **Access Methods**

#### **1. LCD Menu Interface**
```
Setup > Data Logging
├── Log Config     - Konfigurasi logging
├── View Logs      - Lihat data tercatat
├── Download Logs  - Export data
└── Clear Buffer   - Hapus buffer
```

#### **2. Web Interface**
- **URL**: `http://[IP_ESP32]/charts.html`
- **Location**: Panel di bawah grafik monitoring
- **Features**: Enable/Disable, Config, Download, Clear

### **Configuration Options**
- **Enable/Disable**: Toggle data logging
- **Interval**: 1-300 detik (per detik untuk akurasi tinggi)
- **Duration**: 0.5-24 jam (auto-calculate max entries)
- **Auto Save**: Enable/disable penyimpanan otomatis
- **Save Interval**: 1-60 menit

### **Data Format**

#### **CSV Export**
```csv
Timestamp,DateTime,pH,Temperature,GasLevel,Voltage,Current,Power,Status,Notes
1722247825,2024-07-29 14:30:25,7.2,25.5,2.1,12.5,2.3,28.75,RUNNING,
```

#### **JSON Export**
```json
{
  "session_id": "SES_1722247825",
  "timestamp": 1722247825,
  "total_entries": 100,
  "config": {
    "interval_ms": 1000,
    "max_entries": 100
  },
  "entries": [
    {
      "timestamp": 1722247825,
      "datetime": "2024-07-29 14:30:25",
      "pH": 7.2,
      "temperature": 25.5,
      "gasLevel": 2.1,
      "voltage": 12.5,
      "current": 2.3,
      "power": 28.75,
      "status": "RUNNING",
      "notes": ""
    }
  ]
}
```

## 📈 Charts & Monitoring

### **Real-time Charts (6 Total)**
1. **Voltage Chart** - Tegangan aktual (V)
2. **Current Chart** - Arus aktual (A) 
3. **Power Chart** - Daya (W)
4. **pH Chart** - Level pH (0-14 scale)
5. **Temperature Chart** - Suhu (°C)
6. **Gas Level Chart** - Level gas hidrogen (0-100%)

### **Chart Features**
- ✅ **Hide/Show Controls** - Toggle visibility setiap chart
- ✅ **Configurable Intervals** - 0.5, 1, 2, 5 detik
- ✅ **Time Range Options** - 30s, 1min, 2min, 5min
- ✅ **Dark Theme** - Konsisten dengan tema XY6020
- ✅ **Real-time Updates** - Data terbaru otomatis
- ✅ **Responsive Design** - Menyesuaikan ukuran layar

### **Data Table**
- **Location**: Di bawah panel data logging
- **Columns**: Time, pH, Temp(°C), Gas(%), Voltage(V), Current(A), Power(W), Status
- **Display Options**: Last 10, 25, 50, 100 entries
- **Auto Refresh**: Setiap 10 detik
- **Status Colors**: RUNNING (hijau), STOPPED (merah)

## ⏰ Date Time System

### **NTP Configuration**
- **Timezone**: Indonesia WIB (UTC+7)
- **NTP Servers**: pool.ntp.org, time.nist.gov
- **Auto Sync**: Saat WiFi terhubung
- **Fallback**: Menggunakan millis() jika NTP gagal

### **Timestamp Features**
- ✅ **Real-time Timestamps** - Unix timestamp yang akurat
- ✅ **Automatic Sync** - Sinkronisasi otomatis saat startup
- ✅ **Smart Detection** - Deteksi format timestamp otomatis
- ✅ **Backward Compatible** - Kompatibel dengan data lama
- ✅ **Debug Monitoring** - Status waktu setiap 30 detik

### **Time Format**
- **Display**: `YYYY-MM-DD HH:MM:SS` (2024-07-29 14:30:25)
- **Storage**: Unix timestamp (detik sejak 1 Januari 1970)
- **Precision**: Per detik (bukan milidetik)

## 🌐 Web Interface

### **Main Pages**
- **/** - Dashboard utama XY6020
- **/charts.html** - Monitoring dan data logging
- **/config** - Konfigurasi sistem

### **API Endpoints**

#### **Control & Monitoring**
```
GET /control          - Data real-time (voltage, current, power, pH, temp, gas)
POST /control         - Kontrol power supply
GET /wifi             - Status WiFi
```

#### **Data Logging**
```
GET /datalogger                           - Status logging
GET /datalogger?action=config             - Konfigurasi
GET /datalogger?action=logs&limit=50      - Data logs
GET /datalogger?action=download&format=csv - Download CSV
GET /datalogger?action=download&format=json - Download JSON
POST /datalogger                          - Update konfigurasi
POST /datalogger?action=clear             - Clear buffer
```

## 🔧 System Configuration

### **WiFi Setup**
- **Admin Mode**: SSID "Electrowinning_Config" (saat reset atau default)
- **Station Mode**: Koneksi ke WiFi yang dikonfigurasi
- **Multi Reset Detection**: 5x reset dalam 5 detik untuk admin mode

### **MQTT Integration**
- **Broker Support**: Hostname atau IP address
- **Topics**: Configurable publish/subscribe
- **Data Publishing**: Real-time sensor dan status data
- **Remote Control**: Command via MQTT

### **Preferences Storage**
- **Configuration**: WiFi, MQTT, system settings
- **User Profiles**: Electrowinning profiles
- **Calibration Data**: pH sensor calibration
- **Data Logging Config**: Persistent logging settings

## 🎮 LCD Menu System

### **Main Menu Structure**
```
Main Screen
├── Status Display    - Real-time monitoring
├── Setup Menu
│   ├── Data Logging  - Logging configuration
│   ├── Calibration   - pH sensor calibration
│   ├── Motor Control - Pump/fan settings
│   └── System Info   - Version, uptime, etc.
└── Process Control
    ├── Start/Stop    - Process control
    ├── Manual Mode   - Manual operation
    └── Profiles      - Saved profiles
```

### **Navigation**
- **Rotary Encoder**: Scroll menu items
- **Button Press**: Select/confirm
- **Long Press**: Back/cancel
- **Auto Return**: Timeout ke main screen

## 🔒 Safety Features

### **Monitoring Limits**
- **Voltage Limits**: Configurable min/max
- **Current Limits**: Overcurrent protection
- **Temperature Monitoring**: Overheat protection
- **Gas Level Alerts**: Hydrogen gas detection

### **Emergency Stop**
- **Hardware Button**: Physical emergency stop
- **Software Stop**: Via web interface atau LCD
- **Auto Stop**: Saat safety limits terlampaui
- **Status Logging**: Semua stop events dicatat

## 📱 User Experience

### **Web Interface Benefits**
- ✅ **Remote Monitoring** - Akses dari mana saja
- ✅ **Real-time Charts** - Visualisasi data langsung
- ✅ **Easy Download** - Download data ke laptop
- ✅ **Mobile Friendly** - Responsive design
- ✅ **Dark Theme** - Nyaman untuk mata

### **LCD Interface Benefits**
- ✅ **Local Control** - Tidak perlu WiFi
- ✅ **Quick Access** - Menu yang cepat
- ✅ **Visual Feedback** - Status real-time
- ✅ **Reliable** - Backup interface

## 🚀 Performance Specifications

### **System Resources**
- **Flash Usage**: ~83% (1,088,805 / 1,310,720 bytes)
- **RAM Usage**: ~15.6% (51,168 / 327,680 bytes)
- **Update Rate**: 100ms untuk state management
- **Chart Update**: Configurable 0.5-5 detik
- **Data Logging**: Configurable 1-300 detik

### **Network Performance**
- **WiFi Connection**: Auto-reconnect
- **Web Response**: < 100ms untuk API calls
- **Chart Data**: Efficient JSON transfer
- **File Download**: Direct browser download

## 🔧 Installation & Setup

### **Hardware Connections**
```
ESP32 Connections:
├── XY6020: GPIO16(RX), GPIO17(TX) - Modbus RTU
├── LCD: SDA, SCL - I2C (Address 0x27)
├── Encoder: GPIO32(A), GPIO33(B), GPIO25(Button)
├── pH Sensor: GPIO36 - Analog input
├── Temperature: GPIO34 - 1-Wire DS18B20
├── Gas Sensor: GPIO39 - Analog input
└── Motors: L298N driver connections
```

### **Software Setup**
1. **Flash Firmware** - Upload compiled firmware
2. **WiFi Configuration** - Connect atau admin mode
3. **Sensor Calibration** - pH sensor calibration
4. **System Testing** - Verify all functions

### **First Time Setup**
1. Power on ESP32
2. Connect to "Electrowinning_Config" WiFi
3. Open browser to ***********
4. Configure WiFi credentials
5. System akan restart dan connect ke WiFi
6. Access via assigned IP address

## 📋 Troubleshooting

### **Common Issues**

#### **WiFi Connection**
- **Problem**: Tidak bisa connect ke WiFi
- **Solution**: Reset 5x untuk admin mode, reconfigure

#### **NTP Sync Failed**
- **Problem**: Waktu tidak akurat
- **Solution**: Check internet connection, restart ESP32

#### **Data Logging Not Working**
- **Problem**: Data tidak tercatat
- **Solution**: Enable logging, check interval settings

#### **Sensor Reading Error**
- **Problem**: Sensor data tidak valid
- **Solution**: Check connections, calibrate sensors

### **Debug Information**
- **Serial Monitor**: 115200 baud untuk debug output
- **Web Interface**: Status indicators untuk monitoring
- **LCD Display**: Error messages dan status
- **LED Indicators**: System status (jika ada)

## 🎯 Best Practices

### **For Long-term Monitoring**
- Set logging interval 10-30 detik
- Enable auto-save setiap 5-10 menit
- Regular download data untuk backup
- Monitor storage space di SPIFFS

### **For Detailed Analysis**
- Set logging interval 1-5 detik
- Increase buffer size ke 500-1000 entries
- Download data setelah setiap run
- Use CSV format untuk Excel analysis

### **For System Reliability**
- Regular restart (weekly)
- Monitor WiFi connection
- Backup configuration settings
- Keep firmware updated

## ✅ System Status

**Current Version**: v2.1
**Build Status**: ✅ SUCCESS
**All Features**: ✅ WORKING
**Documentation**: ✅ COMPLETE

Sistem Electrowinning siap untuk production use dengan monitoring komprehensif dan data logging yang akurat! 🎯✨
