# 🔋 Electrowinning System with Remote Control

Sistem monitoring dan kontrol electrowinning terintegrasi dengan XY6020 power supply, sensor real-time, dan data logging komprehensif.

## ✨ Key Features

### 🌐 **Remote Control (Web/MQTT)**
* **Web Interface**: Monitoring dan kontrol via browser dengan 6 real-time charts
* **Data Logging System**: CSV/JSON export dengan timestamp akurat (NTP sync)
* **MQTT Integration**: Real-time data publishing dan command subscription
* **Home Assistant Compatible**: JSON format untuk integrasi mudah
* **RESTful API**: Comprehensive endpoints untuk kontrol programmatic

### 📱 **Onsite Operation (LCD/Encoder)**
* **LCD 20x4 Display**: Interface visual untuk operator lapangan
* **Rotary Encoder**: Navigasi menu dan pengaturan parameter
* **Menu System**: Navigasi hierarkis untuk semua fungsi termasuk data logging
* **Profile Management**: Sistem profil untuk berbagai jenis logam
* **Manual/Auto Mode**: Fleksibilitas operasi sesuai kebutuhan

### 🔌 **Power Supply Control**
* **XY6020 Modbus Interface**: Kontrol voltage, current, dan power
* **Safety Monitoring**: Batas aman untuk semua parameter
* **Real-time Feedback**: Monitoring actual vs target values

### 📊 **Advanced Monitoring**
* **6 Real-time Charts**: Voltage, Current, Power, pH, Temperature, Gas Level
* **Hide/Show Controls**: Toggle visibility setiap chart
* **Data Table**: Real-time data table dengan auto-refresh
* **Time Synchronization**: NTP sync untuk timestamp akurat (WIB timezone)

### 💾 **Data Logging System**
* **Configurable Intervals**: 1-300 detik (default: 1 detik untuk akurasi tinggi)
* **Duration-based Config**: Input dalam jam, auto-calculate max entries
* **Multiple Formats**: CSV dan JSON export
* **Direct Download**: Download langsung ke browser
* **Auto-save**: Penyimpanan otomatis ke SPIFFS

## 🔧 Hardware Requirements

### **Main Components**
* **ESP32 Development Board** (recommended) - Main controller
* **XY6020 Power Supply** - Modbus RTU interface
* **LCD 20x4 I2C** - Local display (Address: 0x27)
* **Rotary Encoder** - Navigation input dengan push button

### **Sensors**
* **pH Sensor** - Analog input untuk monitoring pH
* **DS18B20 Temperature** - Digital 1-Wire temperature sensor
* **MQ-8 Gas Sensor** - Hydrogen gas detection
* **Motor Drivers** - L298N untuk pump dan fan control

## 🔌 Hardware Connections

### **XY6020 Modbus (Modbus RTU)**
```
ESP32          XY6020
GPIO16 (RX)    -> TX
GPIO17 (TX)    -> RX
GND            -> GND
```

### **LCD I2C Display**
```
ESP32     LCD 20x4
GPIO21    -> SDA
GPIO22    -> SCL
3.3V      -> VCC
GND       -> GND
```

### **Rotary Encoder**
```
ESP32     Encoder
GPIO32    -> A (CLK)
GPIO33    -> B (DT)
GPIO25    -> Button (SW)
3.3V      -> VCC
GND       -> GND
```

### **Sensors**
```
ESP32     Sensor
GPIO36    -> pH Sensor (Analog)
GPIO34    -> DS18B20 Temperature (1-Wire)
GPIO39    -> MQ-8 Gas Sensor (Analog)
```

## 🚀 Installation & Setup

### **Software Installation**
1. **Install PlatformIO** di Visual Studio Code
2. **Clone/Download** project ini
3. **Open** project di PlatformIO
4. **Build and Upload** ke ESP32

### **First Time Setup**
1. **Power on** ESP32 - akan masuk Admin Mode
2. **Connect** ke WiFi AP "Electrowinning_Config"
3. **Navigate** ke http://***********
4. **Configure** WiFi dan MQTT settings
5. **Reboot** - sistem akan connect ke WiFi yang dikonfigurasi

### **Web Interface Access**
* **Main Dashboard**: `http://[ESP32_IP]/`
* **Charts & Data Logging**: `http://[ESP32_IP]/charts.html`
* **System Configuration**: `http://[ESP32_IP]/config`
* **Electrowinning API**: `http://[ESP32_IP]/electrowinning`

## 📊 Data Logging Features

### **Access Methods**
* **LCD Menu**: `Setup > Data Logging`
* **Web Interface**: Panel di charts.html

### **Configuration Options**
* **Interval**: 1-300 detik (default: 1 detik)
* **Duration**: 0.5-24 jam (auto-calculate max entries)
* **Auto-save**: Enable/disable penyimpanan otomatis
* **Export Formats**: CSV dan JSON

### **Data Format Example**
```csv
Timestamp,DateTime,pH,Temperature,GasLevel,Voltage,Current,Power,Status,Notes
1722247825,2024-07-29 14:30:25,7.2,25.5,2.1,12.5,2.3,28.75,RUNNING,
```

## 📈 Real-time Charts

### **Available Charts (6 Total)**
1. **Voltage Chart** - Real-time voltage monitoring
2. **Current Chart** - Current consumption tracking
3. **Power Chart** - Power calculation (V×A)
4. **pH Chart** - pH sensor readings (0-14 scale)
5. **Temperature Chart** - DS18B20 temperature (°C)
6. **Gas Chart** - MQ-8 hydrogen detection (0-100%)

### **Chart Features**
* ✅ Hide/Show individual charts
* ✅ Configurable update intervals (0.5-5s)
* ✅ Time range selection (30s-5min)
* ✅ Dark theme consistent dengan XY6020
* ✅ Responsive design

## 🎮 LCD Interface

### **Navigation**
* **Rotate**: Scroll menu items
* **Press**: Select/Confirm
* **Long Press**: Back/Cancel

### **Menu Structure**
```
Main Screen (Real-time data)
├── Setup
│   ├── Data Logging    ← New feature!
│   ├── Calibration
│   ├── Motor Control
│   └── System Info
└── Process Control
    ├── Start/Stop
    ├── Manual Mode
    └── Profiles
```

## 🌐 API Endpoints

### **Monitoring & Control**
```
GET /control          - Real-time data (voltage, current, power, pH, temp, gas)
POST /control         - XY6020 power supply control
GET /wifi             - WiFi connection status
```

### **Data Logging**
```
GET /datalogger                           - Logging status
GET /datalogger?action=config             - Configuration
GET /datalogger?action=logs&limit=50      - Data logs
GET /datalogger?action=download&format=csv - Download CSV
GET /datalogger?action=download&format=json - Download JSON
POST /datalogger                          - Update configuration
```

### **Legacy Electrowinning API**
```
GET /electrowinning   - Complete system status
POST /electrowinning  - Send commands in JSON format
```

## 🔒 Safety Features

* **Voltage/Current Limits** - Configurable protection
* **Temperature Monitoring** - Overheat protection
* **Gas Detection** - Hydrogen level alerts
* **Emergency Stop** - Hardware and software stops
* **Event Logging** - All safety events recorded

## 🚀 Performance

* **Flash Usage**: ~83% (1.09MB / 1.31MB)
* **RAM Usage**: ~15.6% (51KB / 328KB)
* **Chart Refresh**: 0.5-5s configurable
* **Data Logging**: 1-300s configurable
* **NTP Sync**: Automatic time synchronization

## 📋 Complete Documentation

**📖 Full Documentation**: [`docs/COMPLETE_SYSTEM_DOCUMENTATION.md`](docs/COMPLETE_SYSTEM_DOCUMENTATION.md)

Dokumentasi lengkap mencakup:
- Detailed hardware setup dan wiring
- Complete software configuration
- Comprehensive API reference
- Troubleshooting guide dan best practices
- Data logging system details
- Chart configuration dan customization

## 🎯 System Status

- **Version**: v2.1
- **Build Status**: ✅ SUCCESS
- **All Features**: ✅ WORKING
- **Production Ready**: ✅ YES

---

**Electrowinning System v2.1** - Complete monitoring and control solution with advanced data logging! 🎯✨
