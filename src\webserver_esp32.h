#ifndef WEBSERVER_ESP32_H
#define WEBSERVER_ESP32_H

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>

#include "settings.h"
#include "xy6020.h"
#include "content/result.h"
#include "electrowinning_state.h"

class XyWebServer {
private:
  WebServer* server;
  Xy6020 *mXy;
  Settings &mConfig;

protected:
  void handleNotFound();
  void handleRoot();
  void handleSettings();
  void handleCharts();
  void handleStyleCss();
  void handleLogicJs();
  void handleSegmentDisplayJs();
  void handleChartsJs();
  void handleControlGet();
  void handleControlPost();
  void handleSettingsGet();
  void handleSettingsPost();
  void handleCORS();
  void handleWifiStatus();
  void handleElectrowinningGet();
  void handleElectrowinningPost();

  // Data logging endpoints
  void handleDataLoggerGet();
  void handleDataLoggerPost();

  // Smart automation endpoints
  void handleSmartAutomationGet();

  // P&O Algorithm endpoints
  void handlePOAlgorithmGet();
  void handlePOAlgorithmPost();

  // Efficiency Calculator endpoints
  void handleEfficiencyGet();
  void handleEfficiencyPost();

  // Smart Automation Testing endpoints
  void handleTestGet();
  void handleTestPost();

public:
  XyWebServer(Xy6020 *xy_obj, Settings &config);
  void init(bool admin_mode = false);
  void task();
};

#endif // WEBSERVER_ESP32_H
