#ifndef EFFICIENCY_CALCULATOR_H
#define EFFICIENCY_CALCULATOR_H

#include <Arduino.h>

/**
 * @brief Comprehensive efficiency calculation for electrowinning process
 * 
 * This class provides multiple efficiency calculation methods based on:
 * - Gas level impact (primary indicator)
 * - Power consumption efficiency
 * - Temperature effects
 * - pH optimization
 * - Time-based efficiency trends
 */
class EfficiencyCalculator {
public:
    struct EfficiencyMetrics {
        float gasBasedEfficiency = 0.0;      // Primary efficiency based on gas levels
        float powerEfficiency = 0.0;         // Power consumption efficiency
        float thermalEfficiency = 0.0;       // Temperature-based efficiency
        float pHEfficiency = 0.0;            // pH optimization efficiency
        float overallEfficiency = 0.0;       // Combined weighted efficiency
        float efficiencyTrend = 0.0;         // Trend indicator (-100 to +100)
        
        // Performance indicators
        float gasImpactFactor = 0.0;         // How much gas affects efficiency
        float powerUtilization = 0.0;        // Power usage effectiveness
        float processStability = 0.0;        // Process stability indicator
        
        // Timestamps
        unsigned long lastUpdate = 0;
        bool isValid = false;
    };

    struct EfficiencyParameters {
        // Gas level parameters
        float optimalGasLevel = 30.0;        // Optimal gas level for efficiency
        float maxGasLevel = 80.0;            // Maximum safe gas level
        float gasImpactFactor = 0.015;       // How much gas affects efficiency (1.5% per 1% gas)
        
        // Power efficiency parameters
        float optimalPowerRange[2] = {200.0, 400.0}; // Optimal power range (W)
        float maxPowerLimit = 500.0;         // Maximum power limit
        float powerEfficiencyFactor = 0.8;   // Power efficiency scaling factor
        
        // Temperature parameters
        float optimalTempRange[2] = {25.0, 35.0}; // Optimal temperature range (°C)
        float tempImpactFactor = 0.02;       // Temperature impact factor
        
        // pH parameters
        float optimalPHRange[2] = {6.5, 7.5}; // Optimal pH range
        float pHImpactFactor = 0.05;         // pH impact factor
        
        // Weighting factors (must sum to 1.0)
        float gasWeight = 0.5;               // Gas level weight (50%)
        float powerWeight = 0.25;            // Power efficiency weight (25%)
        float thermalWeight = 0.15;          // Temperature weight (15%)
        float pHWeight = 0.1;                // pH weight (10%)
        
        // Trend calculation
        int trendSampleCount = 10;           // Number of samples for trend calculation
        float trendSmoothingFactor = 0.1;    // Exponential smoothing factor
    };

private:
    EfficiencyParameters mParams;
    EfficiencyMetrics mMetrics;
    
    // Historical data for trend calculation
    float mEfficiencyHistory[20];            // Circular buffer for efficiency history
    int mHistoryIndex = 0;
    int mHistoryCount = 0;
    
    // Smoothing filters
    float mSmoothedGasEfficiency = 0.0;
    float mSmoothedPowerEfficiency = 0.0;
    float mSmoothedThermalEfficiency = 0.0;
    float mSmoothedPHEfficiency = 0.0;
    
    // Internal calculation methods
    float calculateGasBasedEfficiency(float gasLevel);
    float calculatePowerEfficiency(float voltage, float current, float power);
    float calculateThermalEfficiency(float temperature);
    float calculatePHEfficiency(float pH);
    float calculateEfficiencyTrend();
    float calculateProcessStability();
    
    // Utility methods
    float applyOptimalRange(float value, float optimalMin, float optimalMax, float impactFactor);
    void updateEfficiencyHistory(float efficiency);
    void applySmoothing();

public:
    EfficiencyCalculator();
    
    // Configuration
    void setParameters(const EfficiencyParameters& params);
    const EfficiencyParameters& getParameters() const { return mParams; }
    
    // Main calculation method
    void calculateEfficiency(float gasLevel, float voltage, float current, 
                           float temperature, float pH);
    
    // Individual efficiency calculations
    float getGasBasedEfficiency(float gasLevel);
    float getPowerEfficiency(float voltage, float current);
    float getThermalEfficiency(float temperature);
    float getPHEfficiency(float pH);
    
    // Results access
    const EfficiencyMetrics& getMetrics() const { return mMetrics; }
    float getOverallEfficiency() const { return mMetrics.overallEfficiency; }
    float getEfficiencyTrend() const { return mMetrics.efficiencyTrend; }
    float getProcessStability() const { return mMetrics.processStability; }
    
    // Utility methods
    void reset();
    void printDebugInfo();
    String getEfficiencyReport();
    
    // Static utility functions
    static float constrainEfficiency(float efficiency) { return constrain(efficiency, 0.0, 100.0); }
    static String getEfficiencyGrade(float efficiency);
    static String getEfficiencyDescription(float efficiency);
};

#endif // EFFICIENCY_CALCULATOR_H
