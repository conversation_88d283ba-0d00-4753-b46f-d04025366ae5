#ifndef MOVING_AVERAGE_FILTER_H
#define MOVING_AVERAGE_FILTER_H

#include <Arduino.h>

/**
 * Simple Moving Average Filter
 * Maintains a circular buffer of samples and calculates the average
 * Provides stability detection based on variance from mean
 */
class MovingAverageFilter {
private:
    float* buffer;          // Circular buffer for samples
    int bufferSize;         // Number of samples to average
    int currentIndex;       // Current position in buffer
    float sum;              // Running sum for efficiency
    bool bufferFull;        // Flag indicating buffer is full
    
public:
    /**
     * Constructor
     * @param size Number of samples to average (buffer size)
     */
    MovingAverageFilter(int size);
    
    /**
     * Destructor - cleans up allocated memory
     */
    ~MovingAverageFilter();
    
    /**
     * Add a new sample to the filter
     * @param value New sample value
     * @return Current filtered average
     */
    float addSample(float value);
    
    /**
     * Get current average without adding new sample
     * @return Current average value
     */
    float getAverage();
    
    /**
     * Check if the filter output is stable
     * @param threshold Maximum allowed deviation as percentage (0.05 = 5%)
     * @return true if all samples are within threshold of average
     */
    bool isStable(float threshold = 0.05);
    
    /**
     * Reset filter to initial state
     */
    void reset();
    
    /**
     * Get number of samples currently in buffer
     * @return Sample count (0 to bufferSize)
     */
    int getSampleCount();
    
    /**
     * Get variance of current samples
     * @return Variance value
     */
    float getVariance();
    
    /**
     * Get standard deviation of current samples
     * @return Standard deviation value
     */
    float getStandardDeviation();
    
    /**
     * Check if buffer is full
     * @return true if buffer contains bufferSize samples
     */
    bool isBufferFull();
};

/**
 * Exponential Moving Average Filter
 * Uses exponential weighting where recent samples have more influence
 * More responsive than SMA but uses constant memory
 */
class EMAFilter {
private:
    float alpha;            // Smoothing factor (0-1)
    float value;            // Current EMA value
    bool initialized;       // Initialization flag
    float previousValues[10]; // For stability checking
    int valueIndex;         // Index for previous values array
    int valueCount;         // Count of values stored
    
public:
    /**
     * Constructor
     * @param smoothingFactor Alpha value (0-1), higher = more responsive
     */
    EMAFilter(float smoothingFactor = 0.1);
    
    /**
     * Add a new sample to the filter
     * @param newValue New sample value
     * @return Current filtered value
     */
    float addSample(float newValue);
    
    /**
     * Get current filtered value without adding new sample
     * @return Current EMA value
     */
    float getValue();
    
    /**
     * Check if the filter output is stable
     * @param threshold Maximum allowed deviation as percentage (0.05 = 5%)
     * @return true if recent values are within threshold
     */
    bool isStable(float threshold = 0.05);
    
    /**
     * Reset filter to initial state
     */
    void reset();
    
    /**
     * Set new smoothing factor
     * @param newAlpha New alpha value (0-1)
     */
    void setAlpha(float newAlpha);
    
    /**
     * Get current smoothing factor
     * @return Current alpha value
     */
    float getAlpha();
    
    /**
     * Check if filter has been initialized
     * @return true if at least one sample has been added
     */
    bool isInitialized();
};

/**
 * Filter Manager Class
 * Manages multiple filters for different sensor types
 * Provides centralized filtering and stability checking
 */
class FilterManager {
private:
    MovingAverageFilter* powerFilter;
    MovingAverageFilter* pHFilter;
    MovingAverageFilter* gasFilter;
    EMAFilter* tempFilter;
    EMAFilter* efficiencyFilter;
    
    // Stability thresholds for each sensor type
    float powerStabilityThreshold;
    float pHStabilityThreshold;
    float gasStabilityThreshold;
    float tempStabilityThreshold;
    float efficiencyStabilityThreshold;
    
public:
    /**
     * Constructor - initializes all filters with optimal parameters
     */
    FilterManager();
    
    /**
     * Destructor - cleans up allocated filters
     */
    ~FilterManager();
    
    /**
     * Update all filters with new sensor readings
     * @param power Power value (V * I)
     * @param pH pH sensor reading
     * @param gas Gas sensor reading (0-100%)
     * @param temperature Temperature reading
     * @param efficiency Current efficiency value
     */
    void updateFilters(float power, float pH, float gas, float temperature, float efficiency);
    
    /**
     * Get filtered power value
     * @return Filtered power reading
     */
    float getFilteredPower();
    
    /**
     * Get filtered pH value
     * @return Filtered pH reading
     */
    float getFilteredPH();
    
    /**
     * Get filtered gas value
     * @return Filtered gas reading
     */
    float getFilteredGas();
    
    /**
     * Get filtered temperature value
     * @return Filtered temperature reading
     */
    float getFilteredTemperature();
    
    /**
     * Get filtered efficiency value
     * @return Filtered efficiency reading
     */
    float getFilteredEfficiency();
    
    /**
     * Check if system is stable for optimization
     * @return true if all critical sensors are stable
     */
    bool isSystemStable();
    
    /**
     * Check if power readings are stable
     * @return true if power filter is stable
     */
    bool isPowerStable();
    
    /**
     * Check if pH readings are stable
     * @return true if pH filter is stable
     */
    bool isPHStable();
    
    /**
     * Check if gas readings are stable
     * @return true if gas filter is stable
     */
    bool isGasStable();
    
    /**
     * Check if temperature readings are stable
     * @return true if temperature filter is stable
     */
    bool isTemperatureStable();
    
    /**
     * Reset all filters
     */
    void resetAllFilters();
    
    /**
     * Get filter status for debugging
     * @param buffer String buffer to write status
     * @param bufferSize Size of the buffer
     */
    void getFilterStatus(char* buffer, int bufferSize);
};

#endif // MOVING_AVERAGE_FILTER_H
