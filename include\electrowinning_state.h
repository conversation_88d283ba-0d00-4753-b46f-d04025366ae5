#ifndef ELECTROWINNING_STATE_H
#define ELECTROWINNING_STATE_H

#include <Arduino.h>
#include <Preferences.h>
#include "menu.h"
#include "settings.h"

// Forward declarations
class Xy6020;

// Struktur untuk data sensor real-time
struct SensorData {
    float pH = 0.0;
    float temperature = 0.0;
    float gasLevel = 0.0;
    unsigned long lastUpdate = 0;
    bool isValid = false;

    // Smart automation data
    float filteredPower = 0.0;
    float filteredEfficiency = 0.0;
    bool systemStable = false;
    float noiseReduction = 0.0;  // Percentage of noise reduced by filters
};

// Struktur untuk data output control
struct OutputControl {
    int pumpSpeed = 0;        // 0-100%
    int fanSpeed = 0;         // 0-100%
    int pumpTime = 0;         // minutes
    int fanTime = 0;          // minutes
    bool pumpEnabled = false;
    bool fanEnabled = false;
    unsigned long pumpStartTime = 0;
    unsigned long fanStartTime = 0;
};

// Struktur untuk data power supply (dari XY6020)
struct PowerData {
    float targetVoltage = 0.0;
    float targetCurrent = 0.0;
    float actualVoltage = 0.0;
    float actualCurrent = 0.0;
    float actualPower = 0.0;
    float inputVoltage = 0.0;
    bool outputEnabled = false;
    bool isConnected = false;
};

// Struktur untuk status proses elektrowinning
struct ProcessStatus {
    bool isRunning = false;
    bool isManualMode = true;
    int currentProfileIndex = -1;
    unsigned long startTime = 0;
    unsigned long elapsedTime = 0;
    String lastError = "";
    int errorCount = 0;
};

// Struktur untuk limits dan thresholds
struct ProcessLimits {
    float pHMin = 0.0;
    float pHMax = 14.0;
    float tempMin = 0.0;
    float tempMax = 100.0;
    float gasMin = 20.0;   // 20% - Auto-enable fan threshold
    float gasMax = 80.0;   // 80% - Emergency override threshold
    float maxVoltage = 30.0;
    float maxCurrent = 20.0;
    float maxPower = 600.0;
};

// Class untuk mengelola state terpusat
class ElectrowinningState {
private:
    // Data structures
    SensorData mSensorData;
    OutputControl mOutputControl;
    PowerData mPowerData;
    ProcessStatus mProcessStatus;
    ProcessLimits mProcessLimits;
    
    // User profiles
    UserProfile mProfiles[10];
    int mProfileCount = 1;
    UserProfile mTempProfile;
    
    // Menu state untuk LCD
    MenuState mMenuState;
    
    // References to hardware controllers
    Xy6020* mXy6020 = nullptr;
    Preferences* mPreferences = nullptr;
    
    // Timing
    unsigned long mLastSensorUpdate = 0;
    unsigned long mLastStatusUpdate = 0;
    unsigned long mLastSave = 0;

    // Gas-based fan control
    bool mGasControlActive = false;
    
    // Internal methods
    void updateSensorData();
    void updatePowerData();
    void updateOutputControl();
    void updateGasBasedFanControl();
    void checkSafetyLimits();
    void saveToPreferences();
    
public:
    ElectrowinningState();
    
    // Initialization
    void init(Xy6020* xy6020, Preferences* prefs);
    void loadFromPreferences();
    
    // Main update loop
    void update();
    
    // Sensor data access
    const SensorData& getSensorData() const { return mSensorData; }
    void setSensorData(float pH, float temp, float gas);
    
    // Output control access
    const OutputControl& getOutputControl() const { return mOutputControl; }
    void setPumpSpeed(int speed);
    void setFanSpeed(int speed);
    void setPumpTime(int minutes);
    void setFanTime(int minutes);
    void enablePump(bool enable);
    void enableFan(bool enable);
    
    // Power data access
    const PowerData& getPowerData() const { return mPowerData; }
    void setTargetVoltage(float voltage);
    void setTargetCurrent(float current);
    void enableOutput(bool enable);
    
    // Process control
    const ProcessStatus& getProcessStatus() const { return mProcessStatus; }
    void startProcess(bool manualMode = true, int profileIndex = -1);
    void stopProcess();
    void pauseProcess();
    void resumeProcess();
    
    // Profile management
    const UserProfile& getProfile(int index) const;
    void setProfile(int index, const UserProfile& profile);
    void loadProfile(int index);
    void saveProfile(int index);
    int getProfileCount() const { return mProfileCount; }
    void setProfileCount(int count) { mProfileCount = count; }
    
    // Menu state for LCD
    MenuState& getMenuState() { return mMenuState; }
    const MenuState& getMenuState() const { return mMenuState; }
    
    // Limits and safety
    const ProcessLimits& getLimits() const { return mProcessLimits; }
    void setLimits(const ProcessLimits& limits);
    bool isSafeToOperate() const;
    bool canStartProcess() const;
    
    // Status and error handling
    void setError(const String& error);
    void clearError();
    bool hasError() const { return !mProcessStatus.lastError.isEmpty(); }
    
    // JSON serialization for web/MQTT
    String toJson() const;
    bool fromJson(const String& json);
    
    // Display helpers
    String getStatusString() const;
    String getElapsedTimeString() const;
};

// Global instance declaration
extern ElectrowinningState electrowinningState;

#endif // ELECTROWINNING_STATE_H
