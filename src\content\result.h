#ifndef RESULT_H
#define RESULT_H

// Length 2850 / 4740
const char PROGMEM html__index[] = "<!DOCTYPE html><html><head><script type=\"text/javascript\" src=\"segment-display.js\"></script><script type=\"text/javascript\" src=\"logic.js\"></script><link rel=\"stylesheet\" type=\"text/css\" href=\"style.css\"><title>XY6020 Control</title></head><body onload=\"init()\"><center><div id=\"main-page\"><h1 style=\"font-size: 50px; color: gray\">XY6020 Control <span id=\"connection-state\"\nstyle=\"display: inline; color: lightgreen;\">&#10003;</span></h1><button class=\"my-button\" id=\"on-button\" onclick=\"setOutput(1)\">ON</button><br><button class=\"my-button active-button\" id=\"off-button\" onclick=\"setOutput(0)\">OFF</button><br><br><button class=\"my-button\" id=\"settings-button\" onclick=\"goToSettings()\">Settings</button><br><button class=\"my-button\" id=\"charts-button\" onclick=\"goToCharts()\">Monitoring</button><br><div class=\"my-container\"><span>WiFi Status</span><table><tr><td class=\"segment-label description\">Status:</td><td id=\"wifi-status\">Not Connected</td></tr><tr><td class=\"segment-label description\">IP Address:</td><td id=\"ip-address\">-</td></tr><tr><td class=\"segment-label description\">SSID:</td><td id=\"wifi-ssid\">-</td></tr></table></div><br><div class=\"my-container\"><span>Actual values</span><table><tr><td class=\"segment-label description\">Voltage:</td><td><canvas id=\"actVoltage\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit colored-voltage\">V</td></tr><tr><td class=\"segment-label description\">Current:</td><td><canvas id=\"actCurrent\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit colored-current\">A</td></tr><tr><td class=\"segment-label description\">Power:</td><td><canvas id=\"actPower\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit colored-power\">W</td></tr><tr><td class=\"segment-label description\">Input Voltage:</td><td><canvas id=\"inputVoltage\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\" style=\"color: #ffffa0\">V</td></tr></table></div><br><div class=\"my-container\"><span>Limits</span><table><tr><td class=\"segment-label description\">Voltage:</td><td><canvas id=\"targetVoltage\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\">V</td><td><button class=\"small-button\" id=\"set-voltage-button\"\nonclick=\"setTargetValue(this.id)\">SET</button></td></tr><tr><td class=\"segment-label description\">Current:</td><td><canvas id=\"targetCurrent\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\">A</td><td><button class=\"small-button\" id=\"set-current-button\"\nonclick=\"setTargetValue(this.id)\">SET</button></td></tr><tr><td class=\"segment-label description\">Power:</td><td><canvas id=\"targetPower\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\">W</td><td><button class=\"small-button\" id=\"set-power-button\"\nonclick=\"setTargetValue(this.id)\">SET</button></td></tr></table></div></div></center></body></html>";

// Length 11607 / 11607
const char PROGMEM js__logic[] = "//server_ip = \"http://*************\"\nserver_ip = \"\"\n\nfunction init() {\n    createSegments();\n    getWifiStatus();\n    setInterval(function () {\n        getData();\n    }, 500);\n    setInterval(function () {\n        getWifiStatus();\n    }, 5000);\n}\n\nfunction getWifiStatus() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            const data = JSON.parse(this.responseText);\n            document.getElementById(\"wifi-status\").textContent = data.status;\n            document.getElementById(\"ip-address\").textContent = data.ip;\n            document.getElementById(\"wifi-ssid\").textContent = data.ssid;\n\n            if (data.status === \"Connected\") {\n                document.getElementById(\"wifi-status\").style.color = \"lightgreen\";\n            } else {\n                document.getElementById(\"wifi-status\").style.color = \"red\";\n            }\n        }\n    };\n    xhttp.open(\"GET\", server_ip + \"/wifi-status\", true);\n    xhttp.send();\n}\n\nfunction getData() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            const data = JSON.parse(this.responseText);\n            setDisplayValue(displayActVoltage, data.voltage);\n            setDisplayValue(displayActCurrent, data.current);\n            setDisplayValue(displayActPower, data.power);\n            document.getElementById(\"on-button\").classList.remove(\"my-active-button\");\n            document.getElementById(\"off-button\").classList.remove(\"my-active-button\");\n            if (data.output) {\n                document.getElementById(\"on-button\").classList.add(\"my-active-button\");\n            } else {\n                document.getElementById(\"off-button\").classList.add(\"my-active-button\");\n            }\n            setDisplayValue(displayTargetVoltage, data.tvoltage);\n            setDisplayValue(displayTargetCurrent, data.tcurrent);\n            setDisplayValue(displayTargetPower, data.tpower);\n            setDisplayValue(displayInputVoltage, data.ivoltage);\n            item = document.querySelector(\"[id='connection-state']\");\n            if (data.connected) {\n                item.style.display = \"inline\";\n            } else {\n                item.style.display = \"none\";\n            }\n\n        }\n    };\n    xhttp.open(\"GET\", server_ip + \"/control\", true);\n    xhttp.send();\n}\n\n\nfunction getConfig() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            console.log(this.responseText);\n            const cfg = JSON.parse(this.responseText);\n            console.log(cfg);\n            inp = document.querySelector(\"[name='ssid']\");\n            inp.value = cfg.ssid;\n\n            inp = document.querySelector(\"[name='use-static-ip']\");\n            inp.checked = cfg[\"use-static-ip\"];\n            inp = document.querySelector(\"[name='static-ip']\");\n            inp.value = cfg[\"static-ip\"];\n            inp = document.querySelector(\"[name='subnet']\");\n            inp.value = cfg[\"subnet\"];\n            inp = document.querySelector(\"[name='gateway']\");\n            inp.value = cfg[\"gateway\"];\n\n            inp = document.querySelector(\"[name='mqtt-server']\");\n            inp.value = cfg[\"mqtt-server\"];\n            inp = document.querySelector(\"[name='mqtt-port']\");\n            inp.value = cfg[\"mqtt-port\"];\n            inp = document.querySelector(\"[name='mqtt-user']\");\n            inp.value = cfg[\"mqtt-user\"];\n            inp = document.querySelector(\"[name='mqtt-id']\");\n            inp.value = cfg[\"mqtt-id\"];\n\n            inp = document.querySelector(\"[name='zero-feed-in']\");\n            inp.checked = cfg[\"zero-feed-in\"];\n            inp = document.querySelector(\"[name='smi-topic']\");\n            inp.value = cfg[\"smi-topic\"];\n            inp = document.querySelector(\"[name='sm-name']\");\n            inp.value = cfg[\"sm-name\"];\n\n            inp = document.querySelector(\"[name='enable-input-limits']\");\n            inp.checked = cfg[\"enable-input-limits\"];\n            inp = document.querySelector(\"[name='switch-off-voltage']\");\n            inp.value = cfg[\"switch-off-voltage\"];\n            inp = document.querySelector(\"[name='switch-on-voltage']\");\n            inp.value = cfg[\"switch-on-voltage\"];\n        }\n    };\n    xhttp.open(\"GET\", server_ip + \"/config\", true);\n    xhttp.send();\n}\n\nfunction applySettings(reset) {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            try {\n                var response = JSON.parse(this.responseText);\n                if (response.status === \"OK\") {\n                    // Check if WiFi settings were changed\n                    var wifiChanged = false;\n                    var ssid = document.querySelector(\"[name='ssid']\").value;\n                    var wifiPass = document.querySelector(\"[name='wifi-pass']\").value;\n\n                    if (ssid !== \"\" || wifiPass !== \"\") {\n                        wifiChanged = true;\n                    }\n\n                    if (wifiChanged) {\n                        alert(\"Settings saved! ESP32 will restart to apply WiFi changes. Please reconnect to the new WiFi network.\");\n                    } else {\n                        alert(\"Settings saved successfully!\");\n                    }\n                } else {\n                    alert(\"Applying settings failed!\");\n                }\n            } catch (e) {\n                console.error(\"Error parsing JSON response:\", e);\n                alert(\"Applying settings failed! Error parsing response.\");\n            }\n        }\n    };\n    xhttp.open(\"POST\", server_ip + \"/config\", true);\n    cfg = {};\n    inp = document.querySelector(\"[name='ssid']\");\n    cfg[\"ssid\"] = inp.value;\n    inp = document.querySelector(\"[name='wifi-pass']\");\n    cfg[\"wifi-password\"] = inp.value;\n    inp = document.querySelector(\"[name='use-static-ip']\");\n    cfg[\"use-static-ip\"] = inp.checked;\n    inp = document.querySelector(\"[name='static-ip']\");\n    cfg[\"static-ip\"] = inp.value;\n    inp = document.querySelector(\"[name='subnet']\");\n    cfg[\"subnet\"] = inp.value;\n    inp = document.querySelector(\"[name='gateway']\");\n    cfg[\"gateway\"] = inp.value;\n\n    inp = document.querySelector(\"[name='mqtt-server']\");\n    cfg[\"mqtt-server\"] = inp.value;\n    inp = document.querySelector(\"[name='mqtt-port']\");\n    cfg[\"mqtt-port\"] = parseInt(inp.value);\n    inp = document.querySelector(\"[name='mqtt-user']\");\n    cfg[\"mqtt-user\"] = inp.value;\n    inp = document.querySelector(\"[name='mqtt-pass']\");\n    cfg[\"mqtt-pass\"] = inp.value;\n    inp = document.querySelector(\"[name='mqtt-id']\");\n    cfg[\"mqtt-id\"] = inp.value;\n\n    inp = document.querySelector(\"[name='zero-feed-in']\");\n    cfg[\"zero-feed-in\"] = inp.checked;\n    inp = document.querySelector(\"[name='smi-topic']\");\n    cfg[\"smi-topic\"] = inp.value;\n    inp = document.querySelector(\"[name='sm-name']\");\n    cfg[\"sm-name\"] = inp.value;\n\n    inp = document.querySelector(\"[name='enable-input-limits']\");\n    cfg[\"enable-input-limits\"] = inp.checked;\n    inp = document.querySelector(\"[name='switch-off-voltage']\");\n    cfg[\"switch-off-voltage\"] = inp.value;\n    inp = document.querySelector(\"[name='switch-on-voltage']\");\n    cfg[\"switch-on-voltage\"] = inp.value;\n\n    data = JSON.stringify(cfg);\n    console.log(data);\n    xhttp.send(data);\n}\n\nfunction goBack() {\n    window.location.href = 'index.html';\n}\n\nfunction goToSettings() {\n    window.location.href = 'settings.html';\n}\n\nfunction goToCharts() {\n    window.location.href = 'charts.html';\n}\n\nfunction resetEsp() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.open(\"POST\", server_ip + \"/control?reset\", true);\n    xhttp.send();\n    goBack();\n}\n\nfunction setOutput(state) {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            try {\n                var response = JSON.parse(this.responseText);\n                if (response.status !== \"OK\") {\n                    alert(\"Set output failed!\");\n                }\n            } catch (e) {\n                console.error(\"Error parsing JSON response:\", e);\n                alert(\"Set output failed! Error parsing response.\");\n            }\n        }\n    };\n    xhttp.open(\"POST\", server_ip + \"/control?output=\" + state, true);\n    xhttp.send();\n}\n\nfunction setTargetValue(id) {\n    value = prompt(\"Please enter target value...\");\n    if (value != null && parseFloat(value) != NaN) {\n        var xhttp = new XMLHttpRequest();\n        xhttp.onreadystatechange = function () {\n            if (this.readyState == 4 && this.status == 200) {\n                try {\n                    var response = JSON.parse(this.responseText);\n                    if (response.status !== \"OK\") {\n                        alert(\"Set parameter failed!\");\n                    }\n                } catch (e) {\n                    console.error(\"Error parsing JSON response:\", e);\n                    alert(\"Set parameter failed! Error parsing response.\");\n                }\n            }\n        };\n        var param = \"\";\n        if (id == \"set-voltage-button\") {\n            param = \"voltage\";\n        } else if (id == \"set-current-button\") {\n            param = \"current\";\n        } if (id == \"set-power-button\") {\n            param = \"max-power\";\n        }\n        value = parseFloat(value);\n        xhttp.open(\"POST\", server_ip + \"/control?\" + param + \"=\" + value, true);\n        xhttp.send();\n    }\n}\n\nfunction createSegment(display) {\n    display.pattern = \"###.##\";\n    display.displayAngle = 1.5;\n    display.digitHeight = 21;\n    display.digitWidth = 14;\n    display.digitDistance = 3.1;\n    display.segmentWidth = 2.9;\n    display.segmentDistance = 0.9;\n    display.segmentCount = 7;\n    display.cornerType = 3;\n    display.colorOn = \"#f0f0f0\";\n    display.colorOff = \"#3b3b3b\";\n    display.draw();\n    display.setValue('  0.00');\n}\n\nfunction createSegments() {\n    //actual\n    displayActVoltage = new SegmentDisplay(\"actVoltage\");\n    createSegment(displayActVoltage);\n    displayActVoltage.colorOn = \"#a0a0ff\";\n\n    displayActCurrent = new SegmentDisplay(\"actCurrent\");\n    createSegment(displayActCurrent);\n    displayActCurrent.colorOn = \"#ffa0a0\";\n\n    displayActPower = new SegmentDisplay(\"actPower\");\n    createSegment(displayActPower);\n    displayActPower.colorOn = \"#a0ffa0\";\n    displayActPower.pattern = \"####.#\";\n    displayActPower.setValue('   0.0');\n\n    displayInputVoltage = new SegmentDisplay(\"inputVoltage\");\n    createSegment(displayInputVoltage);\n    displayInputVoltage.colorOn = \"#ffffa0\";\n    setDisplayValue(displayInputVoltage, 0);\n\n    //target\n    displayTargetVoltage = new SegmentDisplay(\"targetVoltage\");\n    createSegment(displayTargetVoltage);\n    displayTargetCurrent = new SegmentDisplay(\"targetCurrent\");\n    createSegment(displayTargetCurrent);\n    displayTargetPower = new SegmentDisplay(\"targetPower\");\n    createSegment(displayTargetPower);\n    displayTargetPower.pattern = \"####.#\";\n    displayTargetPower.setValue('   0.0');\n}\n\nfunction setDisplayValue(display, value) {\n    var pattern_words = display.pattern.split('.');\n    var total_len = display.pattern.length;\n    var post_len = pattern_words[1].length;\n    var value_words = String(value).split('.');\n    var post_word = '';\n    if (value_words.length == 2) {\n        post_word = value_words[1];\n    }\n    post_word = post_word.padEnd(post_len, '0');\n    display.setValue(value_words[0].padStart(total_len - post_len - 1) + '.' + post_word);\n}\n\n";

// Length 13600 / 13600
const char PROGMEM js__segmentdisplay[] = "/*!\n * segment-display.js\n *\n * Copyright 2012, R�diger Appel\n * http://www.3quarks.com\n * Published under Creative Commons 3.0 License.\n *\n * Date: 2012-02-14\n * Version: 1.0.0\n * \n * Dokumentation: http://www.3quarks.com/de/Segmentanzeige\n * Documentation: http://www.3quarks.com/en/SegmentDisplay\n */\n\n// Segment display types\nSegmentDisplay.SevenSegment    = 7;\nSegmentDisplay.FourteenSegment = 14;\nSegmentDisplay.SixteenSegment  = 16;\n\n// Segment corner types\nSegmentDisplay.SymmetricCorner = 0;\nSegmentDisplay.SquaredCorner   = 1;\nSegmentDisplay.RoundedCorner   = 2;\n\n\nfunction SegmentDisplay(displayId) {\n  this.displayId       = displayId;\n  this.pattern         = '##:##:##';\n  this.value           = '12:34:56';\n  this.digitHeight     = 20;\n  this.digitWidth      = 10;\n  this.digitDistance   = 2.5;\n  this.displayAngle    = 12;\n  this.segmentWidth    = 2.5;\n  this.segmentDistance = 0.2;\n  this.segmentCount    = SegmentDisplay.SevenSegment;\n  this.cornerType      = SegmentDisplay.RoundedCorner;\n  this.colorOn         = 'rgb(233, 93, 15)';\n  this.colorOff        = 'rgb(75, 30, 5)';\n};\n\nSegmentDisplay.prototype.setValue = function(value) {\n  this.value = value;\n  this.draw();\n};\n\nSegmentDisplay.prototype.draw = function() {\n  var display = document.getElementById(this.displayId);\n  if (display) {\n    var context = display.getContext('2d');\n    if (context) {\n      // clear canvas\n      context.clearRect(0, 0, display.width, display.height);\n      \n      // compute and check display width\n      var width = 0;\n      var first = true;\n      if (this.pattern) {\n        for (var i = 0; i < this.pattern.length; i++) {\n          var c = this.pattern.charAt(i).toLowerCase();\n          if (c == '#') {\n            width += this.digitWidth;\n          } else if (c == '.' || c == ':') {\n            width += this.segmentWidth;\n          } else if (c != ' ') {\n            return;\n          }\n          width += first ? 0 : this.digitDistance;\n          first = false;\n        }\n      }\n      if (width <= 0) {\n        return;\n      }\n      \n      // compute skew factor\n      var angle = -1.0 * Math.max(-45.0, Math.min(45.0, this.displayAngle));\n      var skew  = Math.tan((angle * Math.PI) / 180.0);\n      \n      // compute scale factor\n      var scale = Math.min(display.width / (width + Math.abs(skew * this.digitHeight)), display.height / this.digitHeight);\n      \n      // compute display offset\n      var offsetX = (display.width - (width + skew * this.digitHeight) * scale) / 2.0;\n      var offsetY = (display.height - this.digitHeight * scale) / 2.0;\n      \n      // context transformation\n      context.save();\n      context.translate(offsetX, offsetY);\n      context.scale(scale, scale);\n      context.transform(1, 0, skew, 1, 0, 0);\n\n      // draw segments\n      var xPos = 0;\n      var size = (this.value) ? this.value.length : 0;\n      for (var i = 0; i < this.pattern.length; i++) {\n        var mask  = this.pattern.charAt(i);\n        var value = (i < size) ? this.value.charAt(i).toLowerCase() : ' ';\n        xPos += this.drawDigit(context, xPos, mask, value);\n      }\n\n      // finish drawing\n      context.restore();\n    }\n  }\n};\n\nSegmentDisplay.prototype.drawDigit = function(context, xPos, mask, c) {\n  switch (mask) {\n    case '#':\n      var r = Math.sqrt(this.segmentWidth * this.segmentWidth / 2.0);\n      var d = Math.sqrt(this.segmentDistance * this.segmentDistance / 2.0);\n      var e = d / 2.0; \n      var f = (this.segmentWidth - d) * Math.sin((45.0 * Math.PI) / 180.0);\n      var g = f / 2.0;\n      var h = (this.digitHeight - 3.0 * this.segmentWidth) / 2.0;\n      var w = (this.digitWidth - 3.0 * this.segmentWidth) / 2.0;\n      var s = this.segmentWidth / 2.0;\n      var t = this.digitWidth / 2.0;\n\n       {\n        var x = xPos;\n        var y = 0;\n        context.fillStyle = this.getSegmentColor(c, '02356789acefp', '02356789abcdefgiopqrstz@');\n        context.beginPath();\n        switch (this.cornerType) {\n          case SegmentDisplay.SymmetricCorner:\n            context.moveTo(x + s + d, y + s);\n            context.lineTo(x + this.segmentWidth + d, y);\n            context.lineTo(x + this.digitWidth - this.segmentWidth - d, y);\n            context.lineTo(x + this.digitWidth - s - d, y + s);\n            break;\n          case SegmentDisplay.SquaredCorner:\n            context.moveTo(x + s + e, y + s - e);\n            context.lineTo(x + this.segmentWidth, y);\n            context.lineTo(x + this.digitWidth - this.segmentWidth, y);\n            context.lineTo(x + this.digitWidth - s - e, y + s - e);\n            break;\n          default:\n            context.moveTo(x + this.segmentWidth - f, y + this.segmentWidth - f - d);\n            context.quadraticCurveTo(x + this.segmentWidth - g, y, x + this.segmentWidth, y);\n            context.lineTo(x + this.digitWidth - this.segmentWidth, y);\n            context.quadraticCurveTo(x + this.digitWidth - this.segmentWidth + g, y, x + this.digitWidth - this.segmentWidth + f, y + this.segmentWidth - f - d);\n        }\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y + this.segmentWidth);\n        context.lineTo(x + this.segmentWidth + d, y + this.segmentWidth);\n        context.fill();\n      }\n      \n      // draw segment b\n      x = xPos + this.digitWidth - this.segmentWidth;\n      y = 0;\n      context.fillStyle = this.getSegmentColor(c, '01234789adhpy', '01234789abdhjmnopqruwy');\n      context.beginPath();\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.moveTo(x + s, y + s + d);\n          context.lineTo(x + this.segmentWidth, y + this.segmentWidth + d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.moveTo(x + s + e, y + s + e);\n          context.lineTo(x + this.segmentWidth, y + this.segmentWidth);\n          break;\n        default:\n          context.moveTo(x + f + d, y + this.segmentWidth - f);\n          context.quadraticCurveTo(x + this.segmentWidth, y + this.segmentWidth - g, x + this.segmentWidth, y + this.segmentWidth);\n      }\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n      context.lineTo(x, y + h + this.segmentWidth - d);\n      context.lineTo(x, y + this.segmentWidth + d);\n      context.fill();\n      \n      // draw segment c\n      x = xPos + this.digitWidth - this.segmentWidth;\n      y = h + this.segmentWidth;\n      context.fillStyle = this.getSegmentColor(c, '013456789abdhnouy', '01346789abdghjmnoqsuw@', '%');\n      context.beginPath();\n      context.moveTo(x, y + this.segmentWidth + d);\n      context.lineTo(x + s, y + s + d);\n      context.lineTo(x + this.segmentWidth, y + this.segmentWidth + d);\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n          context.lineTo(x, y + h + this.segmentWidth - d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.lineTo(x + s + e, y + h + this.segmentWidth + s - e);\n          context.lineTo(x, y + h + this.segmentWidth - d);\n          break;\n        default:\n          context.quadraticCurveTo(x + this.segmentWidth, y + h + this.segmentWidth + g, x + f + d, y + h + this.segmentWidth + f); //\n          context.lineTo(x, y + h + this.segmentWidth - d);\n      }\n      context.fill();\n      \n {\n        x = xPos;\n        y = this.digitHeight - this.segmentWidth;\n        context.fillStyle = this.getSegmentColor(c, '0235689bcdelotuy_', '0235689bcdegijloqsuz_=@');\n        context.beginPath();\n        context.moveTo(x + this.segmentWidth + d, y);\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y);\n        switch (this.cornerType) {\n          case SegmentDisplay.SymmetricCorner:\n            context.lineTo(x + this.digitWidth - s - d, y + s);\n            context.lineTo(x + this.digitWidth - this.segmentWidth - d, y + this.segmentWidth);\n            context.lineTo(x + this.segmentWidth + d, y + this.segmentWidth);\n            context.lineTo(x + s + d, y + s);\n            break;\n          case SegmentDisplay.SquaredCorner:\n            context.lineTo(x + this.digitWidth - s - e, y + s + e);\n            context.lineTo(x + this.digitWidth - this.segmentWidth, y + this.segmentWidth);\n            context.lineTo(x + this.segmentWidth, y + this.segmentWidth);\n            context.lineTo(x + s + e, y + s + e);\n            break;\n          default:\n            context.lineTo(x + this.digitWidth - this.segmentWidth + f, y + f + d);\n            context.quadraticCurveTo(x + this.digitWidth - this.segmentWidth + g, y + this.segmentWidth, x + this.digitWidth - this.segmentWidth, y + this.segmentWidth);\n            context.lineTo(x + this.segmentWidth, y + this.segmentWidth);\n            context.quadraticCurveTo(x + this.segmentWidth - g, y + this.segmentWidth, x + this.segmentWidth - f, y + f + d);\n            context.lineTo(x + this.segmentWidth - f, y + f + d);\n        }\n        context.fill();\n      }\n      \n      // draw segment e\n      x = xPos;\n      y = h + this.segmentWidth;\n      context.fillStyle = this.getSegmentColor(c, '0268abcdefhlnoprtu', '0268acefghjklmnopqruvw@');\n      context.beginPath();\n      context.moveTo(x, y + this.segmentWidth + d);\n      context.lineTo(x + s, y + s + d);\n      context.lineTo(x + this.segmentWidth, y + this.segmentWidth + d);\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n          context.lineTo(x, y + h + this.segmentWidth - d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.lineTo(x + s - e, y + h + this.segmentWidth + s - d + e);\n          context.lineTo(x, y + h + this.segmentWidth);\n          break;\n        default:\n          context.lineTo(x + this.segmentWidth - f - d, y + h + this.segmentWidth + f); \n          context.quadraticCurveTo(x, y + h + this.segmentWidth + g, x, y + h + this.segmentWidth);\n      }\n      context.fill();\n      \n      // draw segment f\n      x = xPos;\n      y = 0;\n      context.fillStyle = this.getSegmentColor(c, '045689abcefhlpty', '045689acefghklmnopqrsuvwy@', '%');\n      context.beginPath();\n      context.moveTo(x + this.segmentWidth, y + this.segmentWidth + d);\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n      context.lineTo(x, y + h + this.segmentWidth - d);\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.lineTo(x, y + this.segmentWidth + d);\n          context.lineTo(x + s, y + s + d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.lineTo(x, y + this.segmentWidth);\n          context.lineTo(x + s - e, y + s + e);\n          break;\n        default:\n          context.lineTo(x, y + this.segmentWidth);\n          context.quadraticCurveTo(x, y + this.segmentWidth - g, x + this.segmentWidth - f - d, y + this.segmentWidth - f); \n          context.lineTo(x + this.segmentWidth - f - d, y + this.segmentWidth - f); \n      }\n      context.fill();\n\n      // draw segment g for 7 segments\n      if (this.segmentCount == 7) {\n        x = xPos;\n        y = (this.digitHeight - this.segmentWidth) / 2.0;\n        context.fillStyle = this.getSegmentColor(c, '2345689abdefhnoprty-=');\n        context.beginPath();\n        context.moveTo(x + s + d, y + s);\n        context.lineTo(x + this.segmentWidth + d, y);\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y);\n        context.lineTo(x + this.digitWidth - s - d, y + s);\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y + this.segmentWidth);\n        context.lineTo(x + this.segmentWidth + d, y + this.segmentWidth);\n        context.fill();\n      }\n            \n      \n      return this.digitDistance + this.digitWidth;\n      \n    case '.':\n      context.fillStyle = (c == '#') || (c == '.') ? this.colorOn : this.colorOff;\n      this.drawPoint(context, xPos, this.digitHeight - this.segmentWidth, this.segmentWidth);\n      return this.digitDistance + this.segmentWidth;\n      \n    case ':':\n      context.fillStyle = (c == '#') || (c == ':') ? this.colorOn : this.colorOff;\n      var y = (this.digitHeight - this.segmentWidth) / 2.0 - this.segmentWidth;\n      this.drawPoint(context, xPos, y, this.segmentWidth);\n      this.drawPoint(context, xPos, y + 2.0 * this.segmentWidth, this.segmentWidth);\n      return this.digitDistance + this.segmentWidth;\n      \n    default:\n      return this.digitDistance;    \n  }\n};\n\nSegmentDisplay.prototype.drawPoint = function(context, x1, y1, size) {\n  var x2 = x1 + size;\n  var y2 = y1 + size;\n  var d  = size / 4.0;\n  \n  context.beginPath();\n  context.moveTo(x2 - d, y1);\n  context.quadraticCurveTo(x2, y1, x2, y1 + d);\n  context.lineTo(x2, y2 - d);\n  context.quadraticCurveTo(x2, y2, x2 - d, y2);\n  context.lineTo(x1 + d, y2);\n  context.quadraticCurveTo(x1, y2, x1, y2 - d);\n  context.lineTo(x1, y1 + d);\n  context.quadraticCurveTo(x1, y1, x1 + d, y1);\n  context.fill();\n}; \n\nSegmentDisplay.prototype.getSegmentColor = function(c, charSet7, charSet14, charSet16) {\n  if (c == '#') {\n    return this.colorOn;\n  } else {\n    switch (this.segmentCount) {\n      case 7:  return (charSet7.indexOf(c) == -1) ? this.colorOff : this.colorOn;\n      default: return this.colorOff;\n    }\n  }\n};\n\n\n\n\n";

// Length 3479 / 5377
const char PROGMEM html__settings[] = "<!DOCTYPE html><html><head><script type=\"text/javascript\" src=\"logic.js\"></script><link rel=\"stylesheet\" type=\"text/css\" href=\"style.css\"><title>XY6020 Settings</title></head><body onload=\"getConfig()\"><center><h1 style=\"font-size: 50px; color: gray\">XY6020 Settings</h1><div id=\"settings-page\"><div class=\"my-container\"><span>WiFi</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">SSID:</span></td><td style=\"width: 70%\"><input type=\"text\" class=\"my-input\" name=\"ssid\" placeholder=\"SSID\"></td></tr><tr><td><span class=\"my-label\">Password:</span></td><td><input type=\"password\" class=\"my-input\" name=\"wifi-pass\"></td></tr></table></div></div><div class=\"my-container\"><span>DHCP</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Use static ip address</span></td><td style=\"width: 70%\"><input type=\"checkbox\" id=\"use-static-ip\" name=\"use-static-ip\"\nclass=\"switch\" /></td></tr><tr><td><span class=\"my-label\">IP address:</span></td><td><input type=\"text\" class=\"my-input\" name=\"static-ip\"></td></tr><tr><td><span class=\"my-label\">Subnet mask:</span></td><td><input type=\"text\" class=\"my-input\" name=\"subnet\"></td></tr><tr><td><span class=\"my-label\">Gateway:</span></td><td><input type=\"text\" class=\"my-input\" name=\"gateway\"></td></tr></table></div><div class=\"my-container\"><span>MQTT</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Server ip:</span></td><td style=\"width: 70%\"><input type=\"text\" class=\"my-input\" name=\"mqtt-server\"></td></tr><tr><td><span class=\"my-label\">Server port:</span></td><td><input type=\"text\" class=\"my-input\" name=\"mqtt-port\"></td></tr><tr><td><span class=\"my-label\">User:</span></td><td><input type=\"text\" class=\"my-input\" name=\"mqtt-user\" placeholder=\"user\"></td></tr><tr><td><span class=\"my-label\">Password:</span></td><td><input type=\"password\" class=\"my-input\" name=\"mqtt-pass\"></td></tr><tr><td><span class=\"my-label\">Client ID:</span></td><td><input type=\"text\" class=\"my-input\" name=\"mqtt-id\" placeholder=\"xy6020_MMMMMMMMMMMM\"></td></tr></table></div><div class=\"my-container\"><span>Electricity feed-in</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Zero feed-in mode</span></td><td style=\"width: 70%\"><input type=\"checkbox\" id=\"checkbox0\" name=\"zero-feed-in\" class=\"switch\" /></td></tr><tr><td><span class=\"my-label\">Tasmota SMI MQTT topic:</span></td><td><input type=\"text\" class=\"my-input\" name=\"smi-topic\"></td></tr><tr><td><span class=\"my-label\">Smart meter name:</span></td><td><input type=\"text\" class=\"my-input\" name=\"sm-name\"></td></tr></table></div><div class=\"my-container\"><span>Input voltage limits</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Enable limits</span></td><td style=\"width: 70%\"><input type=\"checkbox\" id=\"checkbox0\" name=\"enable-input-limits\"\nclass=\"switch\" /></td></tr><tr><td><span class=\"my-label\">Switch off lower voltage:</span></td><td><input type=\"number\" class=\"my-input\" name=\"switch-off-voltage\"></td></tr><tr><td><span class=\"my-label\">Switch on over voltage:</span></td><td><input type=\"text\" class=\"my-input\" name=\"switch-on-voltage\"></td></tr></table></div><br><br><br><div style=\"width: 60%; margin:0px; padding: 0px;\"><button class=\" my-button small\" id=\"back-button\" onclick=\"goBack()\">Back</button><button class=\" my-button small\" id=\"apply-button\" onclick=\"applySettings()\">Apply</button><button class=\" my-button small\" id=\"reset-button\" onclick=\"resetEsp()\">Reboot</button></div></center></body></html>";

// Length 2622 / 3481
const char PROGMEM css__style[] = "body{background-color:#272624;color:white;}.my-label{align-items:left;color:inherit;display:flex;font-size:1rem;font-weight:bold;min-width:0px;padding-bottom:0.25rem;padding-top:0.25rem;font-size:20px;vertical-align:middle;margin:10px;}.my-input{background-color:#363f4f;border-color:transparent;border-style:solid;border-width:0.125rem;border-radius:0.5rem;box-sizing:border-box;color:inherit;display:block;font-family:inherit;font-size:inherit;font-weight:normal;line-height:inherit;margin:3px;margin-left:10px;min-width:0px;outline:0px;padding:0.05rem 0.5rem;width:100%;}.my-input:focus{border-color:#1c76fd;}.switch{appearance:none;box-sizing:border-box;color:#1c76fd;cursor:pointer;display:inline-block;height:1.25rem;position:relative;width:2rem;}.switch:after{background-color:#fff;border-radius:100%;content:'';display:block;height:0.875rem;left:0.1875rem;position:absolute;top:0.1875rem;transition:left 0.3s ease;width:0.875rem;z-index:2;}.switch:before{background-color:#363f4f;border-radius:2rem;content:'';display:block;height:100%;left:0px;position:absolute;top:0px;transition:background-color 0.3s ease;width:100%;z-index:1;}.switch:checked:after{left:1rem;}.switch:checked:before{background-color:currentColor;}.my-button a{color:white;text-decoration:none;text-decoration-color:red;}.my-container{width:50%;display:inline-block;border:2px solid grey;border-radius:10px;margin:3px;margin-left:auto;margin-right:auto;padding:15px;padding-top:0px;}.my-param-table{width:100%;}.my-container>span:first-child{color:#ccc;display:block;border-top-left-radius:10px;border-top-right-radius:10px;margin-bottom:30px;margin-left:-15px;margin-right:-15px;padding-left:10px;background-color:#5b5e55;text-align:left;font-weight:bold;font-size:32px;}.small-button:hover{background-color:#2d2f2d;font-weight:bold;}.small-button{background-color:#1d1f1d;border:none;border-radius:7px;color:white;padding:10px 10px;text-align:center;text-decoration:none;display:inline-block;font-size:15px;}.my-button.small{width:320px;}.my-button{width:413px;background-color:#383b38;border:none;border-radius:7px;color:white;padding:20px 120px;text-align:center;text-decoration:none;display:inline-block;font-size:20px;font-weight:bold;}.my-active-button:hover{background-color:#5c665c !important;}.my-button:hover{background-color:#2d2f2d;}.my-active-button{background-color:#869486;}.segment-label{font-size:28px;font-weight:bold;position:relative;bottom:3px;}.unit{font-size:40px;}.description{color:gray;text-align:right;padding-right:15px;}.colored-voltage{color:#a0a0ff;}.colored-current{color:#ffa0a0;}.colored-power{color:#a0ffa0;}";

// Length 8500 / 10000
const char PROGMEM html__charts[] = "<!DOCTYPE html><html><head><script type=\"text/javascript\" src=\"https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js\"></script><script type=\"text/javascript\" src=\"charts.js\"></script><script type=\"text/javascript\" src=\"logic.js\"></script><link rel=\"stylesheet\" type=\"text/css\" href=\"style.css\"><title>XY6020 Monitoring</title><style>.chart-container{position:relative;height:250px;width:100%;margin-bottom:20px;}.chart-controls{margin-bottom:20px;display:flex;justify-content:space-between;align-items:center;}.chart-controls select,.chart-controls input{background-color:#363f4f;border:none;color:white;padding:5px 10px;border-radius:5px;margin-left:10px;}.chart-controls label{color:#ccc;margin-right:5px;}.data-logging-panel{background-color:#2a3441;border-radius:10px;padding:15px;margin:20px 0;border:1px solid #4a5568;}.logging-controls{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;}.logging-status{display:flex;align-items:center;gap:10px;}.status-indicator{width:12px;height:12px;border-radius:50%;background-color:#e53e3e;}.status-indicator.active{background-color:#38a169;}.logging-buttons{display:flex;gap:10px;}.log-table-container{margin-top:15px;max-height:300px;overflow-y:auto;border:1px solid #4a5568;border-radius:5px;}.log-table{width:100%;border-collapse:collapse;background-color:#363f4f;}.log-table th{background-color:#4a5568;color:#fff;padding:8px;text-align:left;font-size:12px;border-bottom:1px solid #5a6578;}.log-table td{padding:6px 8px;color:#ccc;font-size:11px;border-bottom:1px solid #4a5568;}.log-table tr:nth-child(even){background-color:#3a4451;}.log-table tr:hover{background-color:#4a5568;}.table-controls{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;}.table-controls select{background-color:#363f4f;border:none;color:white;padding:3px 8px;border-radius:3px;font-size:12px;}.chart-visibility{display:flex;gap:10px;margin-bottom:15px;flex-wrap:wrap;}.chart-toggle{display:flex;align-items:center;gap:5px;}.chart-toggle input[type=\"checkbox\"]{margin-right:5px;}.chart-toggle label{color:#ccc;font-size:12px;cursor:pointer;}</style></head><body onload=\"initCharts()\"><center><h1 style=\"font-size:50px;color:gray\">XY6020 Monitoring <span id=\"connection-state\" style=\"display:inline;color:lightgreen;\">&#10003;</span></h1><div class=\"my-container\" style=\"width:80%;\"><span>Real-time Monitoring</span><div class=\"chart-controls\"><div><label for=\"update-interval\">Update Interval:</label><select id=\"update-interval\" onchange=\"updateChartSettings()\"><option value=\"500\">0.5 seconds</option><option value=\"1000\" selected>1 second</option><option value=\"2000\">2 seconds</option><option value=\"5000\">5 seconds</option></select></div><div><label for=\"time-range\">Time Range:</label><select id=\"time-range\" onchange=\"updateChartSettings()\"><option value=\"30\">30 seconds</option><option value=\"60\" selected>1 minute</option><option value=\"300\">5 minutes</option><option value=\"600\">10 minutes</option></select></div><div><button class=\"small-button\" onclick=\"clearChartData()\">Clear Data</button></div></div><div class=\"chart-visibility\"><div class=\"chart-toggle\"><input type=\"checkbox\" id=\"show-voltage\" checked onchange=\"toggleChart('voltage')\"><label for=\"show-voltage\">Voltage</label></div><div class=\"chart-toggle\"><input type=\"checkbox\" id=\"show-current\" checked onchange=\"toggleChart('current')\"><label for=\"show-current\">Current</label></div><div class=\"chart-toggle\"><input type=\"checkbox\" id=\"show-power\" checked onchange=\"toggleChart('power')\"><label for=\"show-power\">Power</label></div><div class=\"chart-toggle\"><input type=\"checkbox\" id=\"show-ph\" checked onchange=\"toggleChart('ph')\"><label for=\"show-ph\">pH</label></div><div class=\"chart-toggle\"><input type=\"checkbox\" id=\"show-temperature\" checked onchange=\"toggleChart('temperature')\"><label for=\"show-temperature\">Temperature</label></div><div class=\"chart-toggle\"><input type=\"checkbox\" id=\"show-gas\" checked onchange=\"toggleChart('gas')\"><label for=\"show-gas\">Gas Level</label></div></div><div class=\"chart-container\" id=\"voltage-container\"><canvas id=\"voltageChart\"></canvas></div><div class=\"chart-container\" id=\"current-container\"><canvas id=\"currentChart\"></canvas></div><div class=\"chart-container\" id=\"power-container\"><canvas id=\"powerChart\"></canvas></div><div class=\"chart-container\" id=\"ph-container\"><canvas id=\"phChart\"></canvas></div><div class=\"chart-container\" id=\"temperature-container\"><canvas id=\"temperatureChart\"></canvas></div><div class=\"chart-container\" id=\"gas-container\"><canvas id=\"gasChart\"></canvas></div></div><div class=\"my-container data-logging-panel\" style=\"width:80%;\"><span>Data Logging</span><div class=\"logging-controls\"><div class=\"logging-status\"><div id=\"logging-indicator\" class=\"status-indicator\"></div><span id=\"logging-status-text\">Disabled</span><span id=\"logging-info\" style=\"color:#888;font-size:12px;\">0 entries</span></div><div class=\"logging-buttons\"><button class=\"small-button\" onclick=\"toggleDataLogging()\" id=\"toggle-logging-btn\">Enable</button><button class=\"small-button\" onclick=\"showLoggingConfig()\">Config</button><button class=\"small-button\" onclick=\"downloadLogs('csv')\">Download CSV</button><button class=\"small-button\" onclick=\"downloadLogs('json')\">Download JSON</button><button class=\"small-button\" onclick=\"clearLogs()\">Clear</button></div></div><div id=\"logging-config-panel\" style=\"display:none;margin-top:15px;padding-top:15px;border-top:1px solid #4a5568;\"><div style=\"display:grid;grid-template-columns:1fr 1fr;gap:15px;\"><div><label for=\"log-interval\">Interval (seconds):</label><input type=\"number\" id=\"log-interval\" min=\"1\" max=\"300\" step=\"1\" value=\"5\" style=\"width:100%;background-color:#363f4f;border:none;color:white;padding:5px;border-radius:3px;\"></div><div><label for=\"duration-hours\">Duration (hours):</label><input type=\"number\" id=\"duration-hours\" min=\"0.1\" max=\"24\" step=\"0.1\" value=\"2\" style=\"width:100%;background-color:#363f4f;border:none;color:white;padding:5px;border-radius:3px;\"></div><div><label><input type=\"checkbox\" id=\"auto-save\" style=\"margin-right:5px;\">Auto Save</label></div><div><label for=\"save-interval\">Save Interval (minutes):</label><input type=\"number\" id=\"save-interval\" min=\"1\" max=\"60\" step=\"1\" value=\"5\" style=\"width:100%;background-color:#363f4f;border:none;color:white;padding:5px;border-radius:3px;\"></div></div><div style=\"margin-top:10px;text-align:right;\"><button class=\"small-button\" onclick=\"saveLoggingConfig()\">Save Config</button><button class=\"small-button\" onclick=\"hideLoggingConfig()\">Cancel</button></div></div><div class=\"table-controls\"><div><label for=\"log-display-count\" style=\"color:#ccc;font-size:12px;\">Show:</label><select id=\"log-display-count\" onchange=\"refreshLogTable()\"><option value=\"10\">Last 10</option><option value=\"25\" selected>Last 25</option><option value=\"50\">Last 50</option><option value=\"100\">Last 100</option></select><span style=\"color:#888;font-size:11px;margin-left:10px;\">entries</span></div><div><button class=\"small-button\" onclick=\"refreshLogTable()\" style=\"font-size:11px;padding:4px 8px;\">Refresh Table</button></div></div><div class=\"log-table-container\"><table class=\"log-table\" id=\"log-data-table\"><thead><tr><th>Time</th><th>pH</th><th>Temp(°C)</th><th>Gas(%)</th><th>Voltage(V)</th><th>Current(A)</th><th>Power(W)</th><th>Status</th></tr></thead><tbody id=\"log-table-body\"><tr><td colspan=\"8\" style=\"text-align:center;padding:20px;color:#888;\">No data available. Enable logging to start collecting data.</td></tr></tbody></table></div></div><br><div style=\"width:60%;margin:0px;padding:0px;\"><button class=\"my-button small\" id=\"back-button\" onclick=\"goBack()\">Back</button><button class=\"my-button small\" id=\"refresh-button\" onclick=\"togglePause()\">Pause</button></div></center></body></html>";

// Length 20000 / 25000
const char PROGMEM js__charts[] = "let voltageChart;let currentChart;let powerChart;let phChart;let temperatureChart;let gasChart;let chartData={voltage:{labels:[],values:[]},current:{labels:[],values:[]},power:{labels:[],values:[]},ph:{labels:[],values:[]},temperature:{labels:[],values:[]},gas:{labels:[],values:[]}};let updateInterval=1000;let timeRange=60;let maxDataPoints=timeRange;let isPaused=false;let updateTimer;let loggingEnabled=false;let loggingConfig={enabled:false,interval_ms:5000,max_entries:100,auto_save:false,save_interval_ms:300000};function initCharts(){getWifiStatus();setInterval(function(){getWifiStatus();},5000);createVoltageChart();createCurrentChart();createPowerChart();createPhChart();createTemperatureChart();createGasChart();initDataLogging();startDataCollection();}function initDataLogging(){loadLoggingConfig();setInterval(updateLoggingStatus,5000);setInterval(refreshLogTable,10000);}function loadLoggingConfig(){var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4&&this.status==200){try{loggingConfig=JSON.parse(this.responseText);updateLoggingUI();}catch(e){console.error('Error loading logging config:',e);}}};xhttp.open('GET',server_ip+'/datalogger?action=config',true);xhttp.send();}function updateLoggingStatus(){var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4&&this.status==200){try{const status=JSON.parse(this.responseText);loggingEnabled=status.enabled;updateLoggingStatusDisplay(status);}catch(e){console.error('Error updating logging status:',e);}}};xhttp.open('GET',server_ip+'/datalogger',true);xhttp.send();}function updateLoggingUI(){document.getElementById('log-interval').value=loggingConfig.interval_ms/1000;const durationHours=loggingConfig.max_entries*(loggingConfig.interval_ms/1000)/3600;document.getElementById('duration-hours').value=durationHours.toFixed(1);document.getElementById('auto-save').checked=loggingConfig.auto_save;document.getElementById('save-interval').value=loggingConfig.save_interval_ms/60000;loggingEnabled=loggingConfig.enabled;updateLoggingStatusDisplay({enabled:loggingConfig.enabled,entries:0,total_logged:0});}function updateLoggingStatusDisplay(status){const indicator=document.getElementById('logging-indicator');const statusText=document.getElementById('logging-status-text');const infoText=document.getElementById('logging-info');const toggleBtn=document.getElementById('toggle-logging-btn');if(status.enabled){indicator.classList.add('active');statusText.textContent='Active';toggleBtn.textContent='Disable';refreshLogTable();}else{indicator.classList.remove('active');statusText.textContent='Disabled';toggleBtn.textContent='Enable';}infoText.textContent=(status.entries||0)+' entries ('+(status.total_logged||0)+' total)';}function toggleDataLogging(){const newConfig={...loggingConfig};newConfig.enabled=!loggingEnabled;var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4){if(this.status==200){loggingEnabled=newConfig.enabled;loggingConfig.enabled=newConfig.enabled;updateLoggingStatusDisplay({enabled:newConfig.enabled,entries:0,total_logged:0});console.log('Logging toggled:',newConfig.enabled?'enabled':'disabled');}else{alert('Failed to toggle logging');}}};xhttp.open('POST',server_ip+'/datalogger',true);xhttp.setRequestHeader('Content-Type','application/json');xhttp.send(JSON.stringify(newConfig));}function showLoggingConfig(){document.getElementById('logging-config-panel').style.display='block';}function hideLoggingConfig(){document.getElementById('logging-config-panel').style.display='none';}function saveLoggingConfig(){const intervalSec=parseFloat(document.getElementById('log-interval').value);const durationHours=parseFloat(document.getElementById('duration-hours').value);if(intervalSec<1){alert('Interval minimum adalah 1 detik untuk data logging');return;}if(durationHours<0.1){alert('Duration minimum adalah 0.1 jam');return;}const maxEntries=Math.ceil((durationHours*3600)/intervalSec);if(maxEntries>1000){alert('Kombinasi interval dan durasi menghasilkan terlalu banyak entries (max 1000)');return;}const newConfig={enabled:loggingConfig.enabled,interval_ms:intervalSec*1000,max_entries:maxEntries,auto_save:document.getElementById('auto-save').checked,save_interval_ms:parseFloat(document.getElementById('save-interval').value)*60000};var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4){if(this.status==200){try{const response=JSON.parse(this.responseText);loggingConfig=newConfig;hideLoggingConfig();alert('Configuration saved successfully');}catch(e){alert('Configuration saved successfully');loggingConfig=newConfig;hideLoggingConfig();}}else{alert('Failed to save configuration: '+this.status);}}};xhttp.open('POST',server_ip+'/datalogger',true);xhttp.setRequestHeader('Content-Type','application/json');xhttp.send(JSON.stringify(newConfig));}function downloadLogs(format){window.open(server_ip+'/datalogger?action=download&format='+format,'_blank');}function clearLogs(){if(confirm('Are you sure you want to clear all logged data?')){var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4){if(this.status==200){alert('Logs cleared successfully');updateLoggingStatus();refreshLogTable();}else{alert('Failed to clear logs');}}};xhttp.open('POST',server_ip+'/datalogger?action=clear',true);xhttp.send();}}function refreshLogTable(){if(!loggingEnabled){return;}var limit=parseInt(document.getElementById('log-display-count').value);var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4&&this.status==200){try{const response=JSON.parse(this.responseText);updateLogTable(response.entries||[]);}catch(e){console.error('Error loading log data:',e);}}};xhttp.open('GET',server_ip+'/datalogger?action=logs&limit='+limit+'&offset=0',true);xhttp.send();}function updateLogTable(entries){const tbody=document.getElementById('log-table-body');if(!entries||entries.length===0){tbody.innerHTML='<tr><td colspan=\"8\" style=\"text-align:center;padding:20px;color:#888;\">No data available. Enable logging to start collecting data.</td></tr>';return;}let html='';entries.reverse();for(let i=0;i<entries.length;i++){const entry=entries[i];const time=formatTimestamp(entry.timestamp);html+='<tr>';html+='<td>'+time+'</td>';html+='<td>'+entry.pH.toFixed(2)+'</td>';html+='<td>'+entry.temperature.toFixed(1)+'</td>';html+='<td>'+entry.gasLevel.toFixed(1)+'</td>';html+='<td>'+entry.voltage.toFixed(2)+'</td>';html+='<td>'+entry.current.toFixed(2)+'</td>';html+='<td>'+entry.power.toFixed(2)+'</td>';html+='<td><span style=\"color:'+(entry.status==='RUNNING'?'#38a169':'#e53e3e')+';\">'+entry.status+'</span></td>';html+='</tr>';}tbody.innerHTML=html;}function formatTimestamp(timestamp){const date=new Date(timestamp);const hours=date.getHours().toString().padStart(2,'0');const minutes=date.getMinutes().toString().padStart(2,'0');const seconds=date.getSeconds().toString().padStart(2,'0');return hours+':'+minutes+':'+seconds;}function createVoltageChart(){const ctx=document.getElementById('voltageChart').getContext('2d');voltageChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Voltage (V)',data:[],borderColor:'#a0a0ff',backgroundColor:'rgba(160, 160, 255, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:false,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function createCurrentChart(){const ctx=document.getElementById('currentChart').getContext('2d');currentChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Current (A)',data:[],borderColor:'#ffa0a0',backgroundColor:'rgba(255, 160, 160, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:true,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function createPowerChart(){const ctx=document.getElementById('powerChart').getContext('2d');powerChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Power (W)',data:[],borderColor:'#a0ffa0',backgroundColor:'rgba(160, 255, 160, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:true,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function createPhChart(){const ctx=document.getElementById('phChart').getContext('2d');phChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'pH Level',data:[],borderColor:'#ffff80',backgroundColor:'rgba(255, 255, 128, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:false,min:0,max:14,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function createTemperatureChart(){const ctx=document.getElementById('temperatureChart').getContext('2d');temperatureChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Temperature (°C)',data:[],borderColor:'#ff8080',backgroundColor:'rgba(255, 128, 128, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:false,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function createGasChart(){const ctx=document.getElementById('gasChart').getContext('2d');gasChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Gas Level (%)',data:[],borderColor:'#80ff80',backgroundColor:'rgba(128, 255, 128, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:true,min:0,max:100,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function startDataCollection(){if(updateTimer){clearInterval(updateTimer);}updateTimer=setInterval(function(){if(!isPaused){fetchAndUpdateChartData();}},updateInterval);fetchAndUpdateChartData();}function fetchAndUpdateChartData(){var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4&&this.status==200){try{const data=JSON.parse(this.responseText);const connectionState=document.getElementById('connection-state');if(data.connected){connectionState.style.display='inline';connectionState.style.color='lightgreen';}else{connectionState.style.display='inline';connectionState.style.color='red';}const now=new Date();const timeString=now.getHours().toString().padStart(2,'0')+':'+now.getMinutes().toString().padStart(2,'0')+':'+now.getSeconds().toString().padStart(2,'0');updateChartData('voltage',timeString,data.voltage);updateChartData('current',timeString,data.current);updateChartData('power',timeString,data.power);updateChartData('ph',timeString,data.pH||7.0);updateChartData('temperature',timeString,data.temperature||25.0);updateChartData('gas',timeString,data.gasLevel||0.0);updateCharts();}catch(e){console.error('Error parsing data:',e);}}};xhttp.open('GET',server_ip+'/control',true);xhttp.send();}function updateChartData(type,label,value){chartData[type].labels.push(label);chartData[type].values.push(value);if(chartData[type].labels.length>maxDataPoints){chartData[type].labels.shift();chartData[type].values.shift();}}function updateCharts(){voltageChart.data.labels=chartData.voltage.labels;voltageChart.data.datasets[0].data=chartData.voltage.values;voltageChart.update();currentChart.data.labels=chartData.current.labels;currentChart.data.datasets[0].data=chartData.current.values;currentChart.update();powerChart.data.labels=chartData.power.labels;powerChart.data.datasets[0].data=chartData.power.values;powerChart.update();phChart.data.labels=chartData.ph.labels;phChart.data.datasets[0].data=chartData.ph.values;phChart.update();temperatureChart.data.labels=chartData.temperature.labels;temperatureChart.data.datasets[0].data=chartData.temperature.values;temperatureChart.update();gasChart.data.labels=chartData.gas.labels;gasChart.data.datasets[0].data=chartData.gas.values;gasChart.update();}function updateChartSettings(){updateInterval=parseInt(document.getElementById('update-interval').value);timeRange=parseInt(document.getElementById('time-range').value);maxDataPoints=Math.ceil(timeRange*1000/updateInterval);startDataCollection();}function clearChartData(){chartData={voltage:{labels:[],values:[]},current:{labels:[],values:[]},power:{labels:[],values:[]},ph:{labels:[],values:[]},temperature:{labels:[],values:[]},gas:{labels:[],values:[]}};updateCharts();}function toggleChart(chartType){const checkbox=document.getElementById('show-'+chartType);const container=document.getElementById(chartType+'-container');if(checkbox.checked){container.style.display='block';}else{container.style.display='none';}}function togglePause(){isPaused=!isPaused;const button=document.getElementById('refresh-button');button.textContent=isPaused?'Resume':'Pause';}";


// Length 11190 / 17539
const char PROGMEM html__datalogger[] = "<!DOCTYPE html> <html> <head> <script type=\"text/javascript\" src=\"logic.js\"></script> <link rel=\"stylesheet\" type=\"text/css\" href=\"style.css\"> <title>Data Logger - XY6020 Control</title> <style> .datalogger-container { max-width: 1000px; margin: 20px auto; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); } .config-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; } .logs-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; } .config-row { display: flex; align-items: center; margin-bottom: 15px; gap: 15px; } .config-label { min-width: 150px; font-weight: bold; } .config-input { padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; } .toggle-switch { position: relative; display: inline-block; width: 60px; height: 34px; } .toggle-switch input { opacity: 0; width: 0; height: 0; } .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 34px; } .slider:before { position: absolute; content: \"\"; height: 26px; width: 26px; left: 4px; bottom: 4px; background-color: white; transition: .4s; border-radius: 50%; } input:checked + .slider { background-color: #2196F3; } input:checked + .slider:before { transform: translateX(26px); } .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; } .status-enabled { background-color: #4CAF50; } .status-disabled { background-color: #f44336; } .log-table { width: 100%; border-collapse: collapse; margin-top: 15px; } .log-table th, .log-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; } .log-table th { background-color: #f2f2f2; font-weight: bold; } .log-table tr:nth-child(even) { background-color: #f9f9f9; } .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; } .stat-card { background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #2196F3; text-align: center; } .stat-value { font-size: 24px; font-weight: bold; color: #333; } .stat-label { color: #666; font-size: 14px; margin-top: 5px; } .button-group { display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap; } .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; transition: all 0.3s ease; } .btn-primary { background: #2196F3; color: white; } .btn-success { background: #4CAF50; color: white; } .btn-warning { background: #FF9800; color: white; } .btn-danger { background: #f44336; color: white; } .btn:hover { opacity: 0.8; transform: translateY(-1px); } .alert { padding: 15px; margin-bottom: 15px; border-radius: 5px; display: none; } .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; } .alert-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; } </style> </head> <body onload=\"initDataLogger()\"> <center> <div class=\"datalogger-container\"> <h1 style=\"color: #333; margin-bottom: 30px;\">📊 Data Logger</h1> <!-- Alert Messages --> <div id=\"alert-success\" class=\"alert alert-success\"></div> <div id=\"alert-error\" class=\"alert alert-error\"></div> <!-- Statistics --> <div class=\"stats-grid\"> <div class=\"stat-card\"> <div class=\"stat-value\" id=\"stat-status\"> <span class=\"status-indicator status-disabled\"></span> DISABLED </div> <div class=\"stat-label\">Logger Status</div> </div> <div class=\"stat-card\"> <div class=\"stat-value\" id=\"stat-entries\">0</div> <div class=\"stat-label\">Current Entries</div> </div> <div class=\"stat-card\"> <div class=\"stat-value\" id=\"stat-interval\">5.0s</div> <div class=\"stat-label\">Log Interval</div> </div> <div class=\"stat-card\"> <div class=\"stat-value\" id=\"stat-capacity\">1440</div> <div class=\"stat-label\">Max Capacity</div> </div> </div> <!-- Configuration Section --> <div class=\"config-section\"> <h2 style=\"color: #333; margin-bottom: 20px;\">⚙️ Configuration</h2> <div class=\"config-row\"> <div class=\"config-label\">Enable Logging:</div> <label class=\"toggle-switch\"> <input type=\"checkbox\" id=\"config-enabled\"> <span class=\"slider\"></span> </label> </div> <div class=\"config-row\"> <div class=\"config-label\">Log Interval (seconds):</div> <input type=\"number\" id=\"config-interval\" class=\"config-input\" min=\"1\" max=\"3600\" value=\"5\" step=\"0.1\"> </div> <div class=\"config-row\"> <div class=\"config-label\">Max Entries:</div> <input type=\"number\" id=\"config-max-entries\" class=\"config-input\" min=\"100\" max=\"10000\" value=\"1440\"> <small style=\"color: #666; margin-left: 10px;\"> (2 hours = 1440 entries at 5s interval) </small> </div> <div class=\"config-row\"> <div class=\"config-label\">Auto Save:</div> <label class=\"toggle-switch\"> <input type=\"checkbox\" id=\"config-auto-save\" checked> <span class=\"slider\"></span> </label> </div> <div class=\"config-row\"> <div class=\"config-label\">Save Interval (minutes):</div> <input type=\"number\" id=\"config-save-interval\" class=\"config-input\" min=\"1\" max=\"60\" value=\"5\"> </div> <div class=\"button-group\"> <button class=\"btn btn-primary\" onclick=\"saveConfig()\">💾 Save Configuration</button> <button class=\"btn btn-success\" onclick=\"loadConfig()\">🔄 Reload Config</button> </div> </div> <!-- Data Management Section --> <div class=\"logs-section\"> <h2 style=\"color: #333; margin-bottom: 20px;\">📁 Data Management</h2> <div class=\"button-group\"> <button class=\"btn btn-primary\" onclick=\"downloadCSV()\">📥 Download CSV</button> <button class=\"btn btn-primary\" onclick=\"downloadJSON()\">📥 Download JSON</button> <button class=\"btn btn-success\" onclick=\"refreshLogs()\">🔄 Refresh Data</button> <button class=\"btn btn-warning\" onclick=\"clearLogs()\">🗑️ Clear Buffer</button> </div> <!-- Log Display --> <div style=\"margin-top: 20px;\"> <h3 style=\"color: #333;\">Recent Log Entries</h3> <div style=\"max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px;\"> <table class=\"log-table\" id=\"log-table\"> <thead> <tr> <th>Time</th> <th>pH</th> <th>Temp (°C)</th> <th>Gas (%)</th> <th>Voltage (V)</th> <th>Current (A)</th> <th>Power (W)</th> <th>Status</th> </tr> </thead> <tbody id=\"log-table-body\"> <tr> <td colspan=\"8\" style=\"text-align: center; padding: 20px; color: #666;\"> No data available. Enable logging to start collecting data. </td> </tr> </tbody> </table> </div> </div> </div> <!-- Navigation --> <div style=\"margin-top: 30px;\"> <button class=\"my-button\" onclick=\"goToHome()\">🏠 Back to Home</button> <button class=\"my-button\" onclick=\"goToCharts()\">📈 Monitoring</button> </div> </div> </center> <script> let currentConfig = {}; function initDataLogger() { console.log('Initializing Data Logger page...'); loadConfig(); refreshLogs(); // Auto-refresh every 10 seconds setInterval(function() { if (currentConfig.enabled) { refreshLogs(); } }, 10000); } function goToHome() { window.location.href = 'index.html'; } function showAlert(message, isError = false) { const alertId = isError ? 'alert-error' : 'alert-success'; const alertElement = document.getElementById(alertId); alertElement.textContent = message; alertElement.style.display = 'block'; // Hide other alert const otherAlertId = isError ? 'alert-success' : 'alert-error'; document.getElementById(otherAlertId).style.display = 'none'; // Auto-hide after 5 seconds setTimeout(() => { alertElement.style.display = 'none'; }, 5000); } function loadConfig() { fetch('/api/datalogger/config') .then(response => response.json()) .then(data => { currentConfig = data; updateConfigUI(data); updateStats(data); }) .catch(error => { console.error('Error loading config:', error); showAlert('Failed to load configuration', true); }); } function updateConfigUI(config) { document.getElementById('config-enabled').checked = config.enabled; document.getElementById('config-interval').value = config.interval_seconds; document.getElementById('config-max-entries').value = config.max_entries; document.getElementById('config-auto-save').checked = config.auto_save; document.getElementById('config-save-interval').value = config.save_interval_minutes; } function updateStats(config) { // Status const statusElement = document.getElementById('stat-status'); const statusIndicator = statusElement.querySelector('.status-indicator'); if (config.enabled) { statusElement.innerHTML = '<span class=\"status-indicator status-enabled\"></span>ENABLED'; } else { statusElement.innerHTML = '<span class=\"status-indicator status-disabled\"></span>DISABLED'; } // Other stats document.getElementById('stat-entries').textContent = config.current_entries; document.getElementById('stat-interval').textContent = config.interval_seconds + 's'; document.getElementById('stat-capacity').textContent = config.max_entries; } function saveConfig() { const config = { enabled: document.getElementById('config-enabled').checked, interval_seconds: parseFloat(document.getElementById('config-interval').value), max_entries: parseInt(document.getElementById('config-max-entries').value), auto_save: document.getElementById('config-auto-save').checked, save_interval_minutes: parseFloat(document.getElementById('config-save-interval').value) }; fetch('/api/datalogger/config', { method: 'POST', headers: { 'Content-Type': 'application/json', }, body: JSON.stringify(config) }) .then(response => response.json()) .then(data => { currentConfig = data; updateStats(data); showAlert('Configuration saved successfully!'); }) .catch(error => { console.error('Error saving config:', error); showAlert('Failed to save configuration', true); }); } function refreshLogs() { fetch('/api/datalogger/logs?limit=50') .then(response => response.json()) .then(data => { updateLogTable(data.entries); }) .catch(error => { console.error('Error loading logs:', error); showAlert('Failed to load log data', true); }); } function updateLogTable(entries) { const tbody = document.getElementById('log-table-body'); if (!entries || entries.length === 0) { tbody.innerHTML = '<tr><td colspan=\"8\" style=\"text-align: center; padding: 20px; color: #666;\">No data available</td></tr>'; return; } tbody.innerHTML = entries.map(entry => ` <tr> <td>${new Date(entry.timestamp).toLocaleTimeString()}</td> <td>${entry.pH.toFixed(2)}</td> <td>${entry.temperature.toFixed(1)}</td> <td>${entry.gasLevel.toFixed(1)}</td> <td>${entry.voltage.toFixed(2)}</td> <td>${entry.current.toFixed(2)}</td> <td>${entry.power.toFixed(2)}</td> <td>${entry.status}</td> </tr> `).join(''); } function downloadCSV() { window.location.href = '/api/datalogger/download?format=csv'; showAlert('CSV download started...'); } function downloadJSON() { window.location.href = '/api/datalogger/download?format=json'; showAlert('JSON download started...'); } function clearLogs() { if (confirm('Are you sure you want to clear all log data from memory? This cannot be undone.')) { // Note: This would require a new API endpoint to clear logs showAlert('Clear logs functionality not yet implemented', true); } } </script> </body> </html>";

#endif // RESULT_H
