#ifndef SMART_AUTOMATION_TEST_H
#define SMART_AUTOMATION_TEST_H

#include <Arduino.h>

/**
 * @brief Comprehensive testing suite for Smart Automation components
 * 
 * This class provides integration testing for all smart automation components:
 * - Moving Average Filters
 * - P&O Algorithm
 * - Efficiency Calculator
 * - System Integration
 */
class SmartAutomationTest {
public:
    struct TestResults {
        // Filter tests
        bool filterInitialization = false;
        bool filterAccuracy = false;
        bool filterStability = false;
        bool filterPerformance = false;
        
        // P&O Algorithm tests
        bool poInitialization = false;
        bool poStateMachine = false;
        bool poOptimization = false;
        bool poSafety = false;
        
        // Efficiency Calculator tests
        bool efficiencyInitialization = false;
        bool efficiencyAccuracy = false;
        bool efficiencyTrend = false;
        bool efficiencyStability = false;
        
        // Integration tests
        bool systemIntegration = false;
        bool webApiIntegration = false;
        bool dataConsistency = false;
        bool performanceMetrics = false;
        
        // Overall results
        int totalTests = 0;
        int passedTests = 0;
        float successRate = 0.0;
        unsigned long testDuration = 0;
        
        // Timestamps
        unsigned long startTime = 0;
        unsigned long endTime = 0;
        bool isComplete = false;
    };

    struct TestConfiguration {
        // Test parameters
        bool enableVerboseOutput = true;
        bool enablePerformanceTesting = true;
        bool enableStressTest = false;
        int testIterations = 100;
        int stressTestDuration = 30000; // 30 seconds
        
        // Test data ranges
        float testGasRange[2] = {0.0, 100.0};
        float testVoltageRange[2] = {5.0, 25.0};
        float testCurrentRange[2] = {0.0, 20.0};
        float testTempRange[2] = {15.0, 50.0};
        float testPHRange[2] = {4.0, 10.0};
        
        // Tolerance levels
        float filterTolerance = 5.0;      // 5% tolerance for filter accuracy
        float efficiencyTolerance = 3.0;  // 3% tolerance for efficiency calculation
        float optimizationTolerance = 2.0; // 2% tolerance for P&O optimization
    };

private:
    TestConfiguration mConfig;
    TestResults mResults;

    // Test helper methods
    bool testFilterInitialization();
    bool testFilterAccuracy();
    bool testFilterStability();
    bool testFilterPerformance();

    bool testPOInitialization();
    bool testPOStateMachine();
    bool testPOOptimization();
    bool testPOSafety();

    bool testEfficiencyInitialization();
    bool testEfficiencyAccuracy();
    bool testEfficiencyTrend();
    bool testEfficiencyStability();

    bool testWebApiIntegration();
    bool testDataConsistency();
    bool testPerformanceMetrics();
    
    // Utility methods
    float generateRandomFloat(float min, float max);
    void simulateSensorData(float& gas, float& voltage, float& current, float& temp, float& pH);
    bool compareFloats(float a, float b, float tolerance);
    void printTestResult(const String& testName, bool result);
    void printProgressBar(int current, int total);

public:
    SmartAutomationTest();
    
    // Configuration
    void setConfiguration(const TestConfiguration& config);
    const TestConfiguration& getConfiguration() const { return mConfig; }
    
    // Main testing methods
    bool runAllTests();
    bool runFilterTests();
    bool runPOAlgorithmTests();
    bool runEfficiencyTests();
    bool runIntegrationTests();
    bool runStressTest();
    
    // Individual test categories
    bool testMovingAverageFilters();
    bool testPOAlgorithm();
    bool testEfficiencyCalculator();
    bool testSystemIntegration();

    // Results and status
    bool allTestsPassed();
    void reset();
    const TestResults& getResults() const;
    float getSuccessRate() const;

    // Reporting
    void printTestSummary();

    // Utility methods
    void enableVerboseOutput(bool enable) { mConfig.enableVerboseOutput = enable; }
    void setTestIterations(int iterations) { mConfig.testIterations = iterations; }

    // Static test utilities
    static bool validateSystemMemory();
    static bool validateSystemPerformance();
    static String getSystemInfo();
};

#endif // SMART_AUTOMATION_TEST_H
