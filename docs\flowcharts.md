# 📊 Smart Electrowinning Automation System Flowcharts

## 🎯 **Overview**

Dokumen ini berisi flowchart lengkap untuk semua komponen utama dalam Smart Electrowinning Automation System. Setiap flowchart menggambarkan alur kerja algoritma secara detail dengan decision points, calculations, dan state transitions.

## 📋 **Daftar Flowcharts**

1. **[Main System Flowchart](#1-main-system-flowchart)** - Alur kerja keseluruhan sistem
2. **[Modified P&O Algorithm](#2-modified-po-algorithm-flowchart)** - Detail algoritma optimasi P&O
3. **[Multi-Layer Safety System](#3-multi-layer-safety-system-flowchart)** - Sistem keamanan berlapis
4. **[Startup Sequence](#4-startup-sequence-flowchart)** - Proses startup bertahap
5. **[Moving Average Filter](#5-moving-average-filter-flowchart)** - Algoritma filter data

---

## 1. Main System Flowchart

### **Deskripsi:**
Flowchart utama yang menggambarkan alur kerja keseluruhan sistem dari startup hingga operasi normal, termasuk state transitions dan decision logic.

### **Key Components:**
- **System Initialization** - Hardware setup dan loading setpoints
- **Safety Checks** - Continuous monitoring untuk keamanan
- **State Machine** - STOPPED → STARTING → RUNNING → OPTIMIZING
- **Emergency Handling** - Critical stop dan recovery procedures
- **User Interface** - Status display dan user interactions

### **State Descriptions:**
```
STOPPED:     System ready, waiting for start command
STARTING:    Startup sequence active (40s maximum)
RUNNING:     Normal operation with safety monitoring
OPTIMIZING:  P&O algorithm active for efficiency
WARNING:     Safety warning, reduced power operation
EMERGENCY:   Critical stop, manual reset required
```

### **Decision Points:**
- **Safety System Check** - Continuous monitoring setiap 100ms
- **Ready to Start** - Multi-parameter validation
- **Optimization Interval** - 30 detik interval untuk P&O
- **System Stability** - Filter-based stability detection

---

## 2. Modified P&O Algorithm Flowchart

### **Deskripsi:**
Detail implementasi Modified Perturb and Observe algorithm untuk optimasi efisiensi daya secara otomatis.

### **Algorithm Steps:**
1. **Power Calculation** - P = V × I
2. **Filter Application** - Moving average untuk stabilitas
3. **Efficiency Calculation** - Berdasarkan gas level
4. **Stability Check** - Memastikan sistem stabil sebelum optimasi
5. **Power Delta Analysis** - Menentukan arah perubahan
6. **Voltage Adjustment** - Increment/decrement berdasarkan hasil
7. **Limit Application** - Voltage clamping untuk keamanan

### **Key Parameters:**
```cpp
voltageStep = 0.5V          // Langkah perubahan tegangan
powerThreshold = 0.5W       // Threshold untuk optimal point
stabilityThreshold = 5%     // Threshold untuk stability check
maxOptimizationTime = 60s   // Maksimum waktu optimasi
```

### **Efficiency Formula:**
```
η = baseEfficiency × (1 - gasLevel × 0.3)
baseEfficiency = 0.8 (80%)
gasLevel = 0-100% (normalized)
```

---

## 3. Multi-Layer Safety System Flowchart

### **Deskripsi:**
Sistem keamanan berlapis dengan hysteresis dan timing untuk mencegah false alarms sambil memberikan proteksi yang cepat dan akurat.

### **Safety Layers:**
1. **pH Safety** - Critical: 5s delay, Warning: immediate
2. **Gas Safety** - Critical: 3s delay, Warning: immediate  
3. **Temperature Safety** - Warning: 10s delay
4. **Power Safety** - Warning: 2s delay

### **Safety Calculations:**
```cpp
// pH Safety Limits
pHRange = pHMax - pHMin
pHSafeMin = pHMin - (pHRange × 0.1)     // 10% margin
pHSafeMax = pHMax + (pHRange × 0.1)     // 10% margin
pHCriticalMin = pHMin - (pHRange × 0.2) // 20% margin
pHCriticalMax = pHMax + (pHRange × 0.2) // 20% margin

// Gas Safety Limits
gasRange = gasMax - gasMin
gasSafeMax = gasMax + (gasRange × 0.2)  // 20% margin
gasCritical = gasMax + (gasRange × 0.4) // 40% margin

// Power Reduction Factors
pH Warning:    30% reduction
Gas Warning:   50% reduction
Temp Warning:  20% reduction
Power Warning: 25% reduction
```

### **Hysteresis Implementation:**
- **pH Hysteresis:** ±0.2 units
- **Gas Hysteresis:** ±5%
- **Moving Average:** 5-10 samples untuk noise reduction
- **Timer-based Delays:** Mencegah nuisance trips

---

## 4. Startup Sequence Flowchart

### **Deskripsi:**
Proses startup bertahap yang memastikan sistem start dengan aman dan stabil sebelum memasuki operasi normal.

### **Startup Steps:**
```
Step 0: Pre-ventilation (10s)
- Fan: 50% speed
- Pump: OFF
- Output: OFF
- Purpose: Ventilasi awal untuk keamanan

Step 1: Pump Circulation (5s)  
- Fan: 50% speed
- Pump: 30% speed
- Output: OFF
- Purpose: Sirkulasi larutan

Step 2: Power Ramp (15s)
- Fan: 50% speed
- Pump: 50% speed
- Output: Gradual ramp 8V→target
- Purpose: Soft start untuk stabilitas

Step 3: Stabilization (10s)
- Fan: 50% speed
- Pump: 50% speed  
- Output: Target values
- Purpose: Verifikasi stabilitas
```

### **Power Ramp Formula:**
```cpp
progress = stepDuration / 15000.0  // 15 seconds
V_ramp = V_start + (V_target - V_start) × progress
I_ramp = I_start + (I_target - I_start) × progress

Where:
V_start = 8V, I_start = 1A (safe starting values)
V_target, I_target = user setpoints
```

### **Safety Checks:**
- **Pre-startup:** All sensors dalam range aman
- **During startup:** Continuous monitoring
- **Post-startup:** Final stability verification
- **Restart Logic:** Automatic restart jika gagal

---

## 5. Moving Average Filter Flowchart

### **Deskripsi:**
Implementasi Simple Moving Average (SMA) dan Exponential Moving Average (EMA) untuk noise reduction dan stability detection.

### **SMA Algorithm:**
```cpp
struct MovingAverageFilter {
    float* buffer;      // Circular buffer
    int bufferSize;     // 5-20 samples
    int currentIndex;   // Current position
    float sum;          // Running sum
    bool bufferFull;    // Full flag
};

SMA = sum / count
count = bufferFull ? bufferSize : currentIndex
```

### **EMA Algorithm:**
```cpp
struct EMAFilter {
    float alpha = 0.1;  // Smoothing factor
    float value;        // Current EMA value
    bool initialized;   // Initialization flag
};

EMA(t) = α × x(t) + (1-α) × EMA(t-1)
```

### **Stability Detection:**
```cpp
bool isStable(float threshold = 0.05) {
    float avg = getAverage();
    for (int i = 0; i < bufferSize; i++) {
        if (|buffer[i] - avg| > threshold × avg) {
            return false;  // Unstable
        }
    }
    return true;  // Stable
}
```

### **Filter Applications:**
- **Power Filter:** 10 samples, 5% stability threshold
- **pH Filter:** 10 samples, 10% stability threshold  
- **Gas Filter:** 5 samples, 5% stability threshold
- **Efficiency Filter:** 20 samples, 10% stability threshold

---

## 🎯 **Flowchart Integration**

### **System Timing:**
```
Main Loop:           100ms cycle
Safety Check:        Every cycle (10 Hz)
P&O Optimization:    30s interval
Display Update:      500ms (2 Hz)
Performance Calc:    1s (1 Hz)
```

### **State Synchronization:**
- **Safety System:** Always active, highest priority
- **P&O Algorithm:** Active only during OPTIMIZING state
- **Startup Sequence:** Sequential steps dengan timing
- **Moving Average:** Continuous filtering untuk semua sensors

### **Error Handling:**
- **Hardware Errors:** Return to STOPPED state
- **Sensor Errors:** Use last known good values
- **Communication Errors:** Local operation mode
- **Critical Errors:** Emergency stop dengan manual reset

### **Performance Metrics:**
- **Convergence Time:** <2 minutes untuk optimal point
- **Safety Response:** <3 seconds untuk critical stop
- **Startup Time:** 40 seconds maximum
- **Stability Time:** <30 seconds setelah perubahan

---

## 📋 **Usage Guidelines**

### **For Developers:**
- Gunakan flowcharts untuk understanding algorithm flow
- Reference untuk debugging dan troubleshooting
- Basis untuk code review dan optimization

### **For Engineers:**
- Parameter tuning berdasarkan flowchart logic
- Safety system configuration
- Performance optimization guidelines

### **For Operators:**
- Understanding sistem behavior
- Troubleshooting procedures
- Expected timing dan responses

### **For Maintenance:**
- Diagnostic procedures
- Component testing sequences
- System validation steps

---

## 🎉 **Summary**

Flowcharts ini memberikan visualisasi lengkap dari Smart Electrowinning Automation System, menunjukkan bagaimana setiap komponen bekerja secara individual dan terintegrasi untuk memberikan operasi yang aman, efisien, dan dapat diandalkan.

**Key Benefits:**
- ✅ **Clear Logic Flow** - Easy to understand dan follow
- ✅ **Decision Points** - Explicit conditions dan actions
- ✅ **Error Handling** - Comprehensive error recovery
- ✅ **Safety First** - Multi-layer protection visible
- ✅ **Performance Focus** - Optimization logic clear
