# 🎯 **Smart Electrowinning System - Versi Balanced**

## ✅ **File Flowchart Balanced yang Tersedia**

### **📁 File XML Balanced (Tidak Terlalu Sederhana, Tidak Terlalu Detail)**
1. **`docs/sistem_utama_balanced.xml`** - ✅ **Sistem Utama Balanced**
2. **`docs/deteksi_emas_subsistem.xml`** - ✅ **Subsistem Deteksi Emas** (tetap sama)

**Format:** XML Draw.io dengan level detail yang pas - cukup informasi tapi tidak overwhelming

---

## 🎨 **Flowchart Sistem Utama Balanced**

### **📊 Alur Sistem yang Balanced:**
```
MULAI SISTEM (Boot ESP32)
    ↓
Inisialisasi Hardware
• Setup pin & peripheral
• Load pengaturan EEPROM
• Cek koneksi sensor
• Inisialisasi LCD & WiFi
    ↓
Cek Sistem Awal? → [GAGAL] → Error Inisialisasi → (ulangi inisialisasi)
    ↓ [BERHASIL]
SISTEM SIAP (Status: BERHENTI)
    ↓
Loop Sistem Siap
• Monitor sensor dasar
• Tampilkan status
• Tunggu perintah user
    ↓
Perintah Start? → [TIDAK] → (kembali ke Loop Siap)
    ↓ [YA]
Siap untuk Start?
• Sensor OK
• Parameter valid
• Keamanan OK
├─ [TIDAK] → Tampilkan Peringatan → (kembali ke Loop Siap)
└─ [YA] → MEMULAI - Startup Sequence
    • Pra-ventilasi (10s)
    • Sirkulasi pompa (5s)
    • Ramp daya (15s)
    • Stabilisasi (10s)
    ↓
Startup Berhasil? → [TIDAK] → (ulangi startup)
    ↓ [YA]
BERJALAN - Operasi Normal (Status: RUNNING)
    ↓
Loop Operasi Utama
• Baca semua sensor (pH, Gas, Suhu)
• Baca parameter listrik (V, I, R, P)
• Terapkan filter data
• Update display & logging
    ↓
Cek Keamanan Multi-Layer?
├─ [KRITIS] → STOP DARURAT → (kembali ke Sistem Siap)
├─ [PERINGATAN] → PERINGATAN KEAMANAN → (kembali ke Loop Operasi)
└─ [AMAN] → Deteksi Emas (Interval 5 detik)?
    ├─ [TIDAK] → Optimisasi P&O (Interval 30 detik)?
    │   ├─ [YA] → MENGOPTIMASI → (kembali ke Loop Operasi)
    │   └─ [TIDAK] → Perintah Stop Manual?
    │       ├─ [YA] → STOP MANUAL → PROSES SELESAI
    │       └─ [TIDAK] → (kembali ke Loop Operasi)
    └─ [YA] → Status Kandungan Emas?
        ├─ [NORMAL] → EMAS NORMAL → (kembali ke Loop Operasi)
        ├─ [MENIPIS] → EMAS MENIPIS → (kembali ke Loop Operasi)
        └─ [HABIS] → EMAS HABIS → PROSES SELESAI
```

### **🔧 Fitur Balanced:**
- ✅ **Detail yang cukup** - tidak terlalu sederhana
- ✅ **Tidak overwhelming** - tidak terlalu banyak detail
- ✅ **Error handling** yang jelas
- ✅ **Startup sequence** dengan langkah-langkah
- ✅ **Multi-layer safety** dengan respons yang tepat
- ✅ **Gold detection** terintegrasi dengan baik
- ✅ **P&O optimization** dengan kondisi yang jelas
- ✅ **Manual control** untuk operator
- ✅ **End process** yang natural (emas habis)

---

## 🎯 **Level Detail yang Pas**

### **✅ Yang Ditambahkan dari Versi Sederhana:**
- **Error handling** pada inisialisasi
- **Loop sistem siap** dengan monitoring dasar
- **Cek siap untuk start** dengan validasi
- **Startup sequence** dengan timing detail
- **Startup success check** dengan retry
- **Safety warning** dengan recovery
- **Gold status** dengan 3 level (Normal, Menipis, Habis)
- **Manual stop** dengan konfirmasi
- **Process complete** dengan detail

### **✅ Yang Tidak Berlebihan (Dibanding Versi Detail):**
- Tidak ada detail algoritma P&O di main flow
- Tidak ada detail kalkulasi safety thresholds
- Tidak ada detail buffer management
- Tidak ada detail timer mechanisms
- Tidak ada detail manual override procedures
- Tidak ada detail recovery algorithms

### **✅ Balance yang Tepat:**
```
Sederhana:     7 langkah utama
Balanced:      15 langkah dengan decision points ← OPTIMAL
Detail:        30+ langkah dengan semua detail
```

---

## 🔧 **Komponen Utama Sistem Balanced**

### **🚀 Startup & Initialization:**
```
1. Hardware Setup
   • Pin configuration
   • EEPROM loading
   • Sensor check
   • Display & WiFi init

2. System Ready Check
   • Initial safety validation
   • Sensor connectivity
   • Parameter validation

3. Startup Sequence (4 steps)
   • Pre-ventilation: 10s
   • Pump circulation: 5s
   • Power ramp: 15s
   • Stabilization: 10s
```

### **⚡ Operation Loop:**
```
1. Data Acquisition
   • Read all sensors (pH, Gas, Temp)
   • Read electrical params (V, I, R, P)
   • Apply data filtering

2. Safety System
   • Multi-layer safety check
   • Critical → Emergency stop
   • Warning → Reduce power, continue

3. Gold Detection (5s interval)
   • Calculate depletion trends
   • Evaluate gold status
   • Normal/Depleting/Depleted

4. Optimization (30s interval)
   • P&O algorithm execution
   • Only if gold status allows
   • Skip if gold depleting
```

### **🛡️ Safety & Control:**
```
Safety Levels:
• CRITICAL → Emergency stop, return to ready
• WARNING → Power reduction, continue operation
• SAFE → Normal operation allowed

Gold Status:
• NORMAL → Continue all operations
• MENIPIS → Skip optimization, intensive monitoring
• HABIS → End process (main endpoint)

Manual Control:
• Stop command available anytime
• Graceful shutdown procedure
• Data saving before stop
```

---

## 🎯 **End Process Strategy**

### **📊 3 End Conditions:**
```
1. EMAS HABIS (Primary End)
   • Natural process completion
   • Maximum recovery achieved
   • Auto or manual decision

2. STOP DARURAT (Safety End)
   • Critical safety condition
   • Immediate shutdown
   • Return to system ready

3. STOP MANUAL (Operator End)
   • Operator decision
   • Graceful shutdown
   • Process complete with data
```

### **🥇 Gold Depletion as Main End:**
- ✅ **Natural endpoint** - proses selesai saat emas habis
- ✅ **Efficient operation** - maksimum recovery
- ✅ **Predictable timing** - estimasi berdasarkan trend
- ✅ **Quality control** - stop pada waktu yang tepat

---

## 🚀 **Implementasi & Penggunaan**

### **Step 1: Import Flowchart**
```
1. Download: docs/sistem_utama_balanced.xml
2. Buka https://app.diagrams.net/
3. File → Import from → Device
4. Select file XML
5. Lihat flowchart dengan level detail yang pas
```

### **Step 2: Understand the Flow**
```
• Ikuti alur dari start sampai end
• Perhatikan decision points yang jelas
• Pahami kondisi-kondisi end process
• Lihat integration dengan subsistem
```

### **Step 3: Implementation Guide**
```cpp
// Main system states
enum SystemState {
    STOPPED,
    STARTING,
    RUNNING,
    OPTIMIZING,
    STOPPING
};

// Main system loop
void mainSystemLoop() {
    switch(currentState) {
        case STOPPED:
            handleStoppedState();
            break;
        case STARTING:
            handleStartupSequence();
            break;
        case RUNNING:
            handleRunningState();
            break;
        case OPTIMIZING:
            handleOptimization();
            break;
        case STOPPING:
            handleStoppingState();
            break;
    }
}

// Running state handler
void handleRunningState() {
    // Read all sensors
    readAllSensors();
    
    // Safety check
    SafetyStatus safety = checkSafety();
    if (safety == CRITICAL) {
        emergencyStop();
        return;
    }
    
    // Gold detection (5s interval)
    if (goldDetectionInterval()) {
        GoldStatus gold = detectGold();
        if (gold == DEPLETED) {
            endProcess("Gold Depleted");
            return;
        }
    }
    
    // Optimization (30s interval, conditional)
    if (optimizationInterval() && goldStatus != DEPLETING) {
        currentState = OPTIMIZING;
    }
    
    // Manual stop check
    if (manualStopRequested()) {
        currentState = STOPPING;
    }
}
```

---

## 🎉 **Benefits Versi Balanced**

### **✅ Untuk Developer:**
- **Cukup detail** untuk implementasi yang benar
- **Tidak overwhelming** saat coding
- **Clear decision points** untuk logic branching
- **Proper error handling** guidance

### **✅ Untuk Operator:**
- **Understandable flow** tanpa detail teknis berlebihan
- **Clear status indicators** untuk monitoring
- **Predictable behavior** untuk operation planning
- **Manual control** tetap tersedia

### **✅ Untuk Management:**
- **Professional documentation** dengan level detail yang tepat
- **Clear process overview** untuk business understanding
- **Defined endpoints** untuk planning dan scheduling
- **Quality assurance** dengan systematic approach

### **✅ Untuk Maintenance:**
- **Balanced troubleshooting** - tidak terlalu simple, tidak terlalu complex
- **Component identification** yang jelas
- **Update points** yang well-defined
- **Documentation standard** yang maintainable

---

## 🎯 **Kesimpulan**

**Flowchart Balanced ini memberikan:**

1. **✅ Level detail yang pas** - tidak terlalu sederhana, tidak terlalu rumit
2. **✅ Implementasi yang jelas** - cukup informasi untuk coding
3. **✅ Understanding yang mudah** - tidak overwhelming untuk operator
4. **✅ Professional quality** - cocok untuk dokumentasi dan presentation
5. **✅ End process yang natural** - emas habis sebagai endpoint utama

**Sistem ini mencapai sweet spot antara simplicity dan completeness - memberikan informasi yang cukup untuk implementasi yang benar tanpa membuat flowchart menjadi terlalu kompleks!** 🎯

**Silakan import file `sistem_utama_balanced.xml` untuk melihat flowchart dengan level detail yang pas!** 🚀
