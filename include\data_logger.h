#ifndef DATA_LOGGER_H
#define DATA_LOGGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include <vector>
#include <time.h>

class DataLogger {
public:
    struct LogEntry {
        unsigned long timestamp;
        float pH;
        float temperature;
        float gasLevel;
        float voltage;
        float current;
        float power;
        String status;
        String notes;
        
        LogEntry() : timestamp(0), pH(0), temperature(0), gasLevel(0), 
                    voltage(0), current(0), power(0), status(""), notes("") {}
    };

    struct LogConfig {
        bool enabled;
        unsigned long intervalMs;
        int maxEntries;
        bool autoSave;
        unsigned long saveIntervalMs;
        
        LogConfig() : enabled(false), intervalMs(1000), maxEntries(100),
                     autoSave(false), saveIntervalMs(300000) {}
    };

    DataLogger();
    
    // Configuration
    void setConfig(const LogConfig& config);
    LogConfig getConfig() const { return config; }
    String getConfigAsJSON() const;
    bool setConfigFromJSON(const String& jsonStr);
    
    // Logging operations
    void logData(float pH, float temp, float gas, float voltage, float current, 
                const String& status, const String& notes = "");
    void update(); // Call in main loop for auto-save
    
    // Data access
    int getEntryCount() const { return logBuffer.size(); }
    LogEntry getLogEntry(int index) const;
    std::vector<LogEntry> getRecentLogs(int count = 50) const;
    String getLogsAsJSON(int limit = 50, int offset = 0) const;
    String getLogsAsCSV() const;
    String getLogSummary() const;
    
    // File operations
    bool saveToFile();
    bool saveToCSV(const String& filename = "");
    bool saveToJSON(const String& filename = "");
    void clearBuffer();
    
    // Status
    bool isEnabled() const { return config.enabled; }
    unsigned long getLastLogTime() const { return lastLogTime; }
    int getTotalLoggedEntries() const { return totalLoggedEntries; }

private:
    LogConfig config;
    std::vector<LogEntry> logBuffer;
    unsigned long lastLogTime;
    unsigned long lastSaveTime;
    int totalLoggedEntries;
    String currentSessionId;
    
    // Helper methods
    void setMaxEntries(int maxEntries);
    String generateFilename() const;
    bool saveBufferToFile(const String& filename);
    String formatLogEntry(const LogEntry& entry, bool isCSV = false) const;
    String getFormattedDateTime(unsigned long timestamp) const;
};

#endif // DATA_LOGGER_H
