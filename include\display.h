#ifndef DISPLAY_H
#define DISPLAY_H

#include <LiquidCrystal_I2C.h>
#include <Preferences.h>
#include "AiEsp32RotaryEncoder.h"
#include "menu.h"
#include "electrowinning_state.h"

// pH Calibration Data Structure - Three Point Calibration
struct PHCalibrationData {
    bool isCalibrated = false;
    // Three calibration points
    float pH4_voltage = 1.8;       // Voltage at pH 4.00
    float pH7_voltage = 2.5;       // Voltage at pH 6.86 (near neutral)
    float pH9_voltage = 3.2;       // Voltage at pH 9.18
    // Calculated parameters
    float slope = 0.18;            // mV per pH unit (average)
    float offset = 0.0;            // Offset correction
    bool point1_done = false;      // pH 4.00 calibration done
    bool point2_done = false;      // pH 6.86 calibration done
    bool point3_done = false;      // pH 9.18 calibration done
    unsigned long calibrationTime = 0; // Timestamp of last calibration
};

// Calibration state for UI
struct CalibrationState {
    bool isCalibrating = false;
    int currentStep = 0;           // 0=idle, 1=pH7, 2=pH4
    float currentReading = 0.0;
    int sampleCount = 0;
    float sampleSum = 0.0;
    unsigned long startTime = 0;
};

// Eksternal variabel yang dibutuhkan
extern LiquidCrystal_I2C lcd;
extern Preferences preferences;
extern AiEsp32RotaryEncoder rotaryEncoder;
extern MenuState menuState;
extern float pH, temp, current, gas;
extern int pumpSpeed, fanSpeed, voltage;
extern float pHMin, pHMax, tempMin, tempMax, gasMin, gasMax;
extern int pumpSpeedSetting, fanTimeSetting;
extern float voltageSetting, currentSetting;

// pH Calibration variables
extern PHCalibrationData pHCalibration;
extern CalibrationState calibrationState;
extern int currentMenuSize;
extern int previousEncoderPosition;
extern bool displayNeedsUpdate;
extern int userNameIndex, profileTypeIndex;
extern UserProfile profiles[];
extern int profileCount, currentProfileIndex;
extern UserProfile tempProfile;
extern bool isRunning;
extern const char* profileTypes[];
extern ElectrowinningState electrowinningState;

// Deklarasi fungsi display
void displayMainScreen();
void displaySetupMenu();
void displayStartMenu();


void displayManualSettingsMenu();
void displaySensorSettingsMenu();
void displayOutputSettingsMenu();
void displayConfigProfilesMenu();
void displayUserAMenu();
void displayNewUserMenu();
void displayPumpSettingMenu();
void displayFanSettingMenu();
void displayVoltageControlMenu();
void displayCurrentControlMenu();
void displayEditSensorMenu();
void displayAddUserMenu();
void displayStartMenu();
void displayChooseProfileMenu();
String formatFloat(float value, int decimalPlaces);

// Helper functions for menu navigation - SESUAI menu_hierarchy_navigation_guide.md
int getCurrentMenuSize();
const MenuItem* getCurrentMenu();
void navigateToLevel(MenuLevel newLevel);
MenuLevel determineBackTarget();
void navigateBack();
void toggleEditMode();
void updateScrollPosition();
void displayMenuTemplate();
String getCurrentValue(int itemIndex);

// Deklarasi fungsi rotary encoder dan pengelolaan profil
void IRAM_ATTR readEncoderISR();
void rotary_loop();
void rotary_onButtonClick();
void updateEncoderBoundaries();
void initializeProfiles();
void saveProfileToPreferences(int index);
void loadProfilesFromPreferences();
void saveProfileCountToPreferences();
void loadVoltageCurrentSettings();
void saveVoltageCurrentSettings();
void loadDataLoggerConfig();

// Motor control functions (defined in main.cpp)
extern void testMotorControl();
extern void updateLegacyVariables();

// pH Calibration functions
void startPHCalibration(int step); // 1=pH7, 2=pH4
void processPHCalibration();
void finishPHCalibration();
void resetPHCalibration();
void savePHCalibration();
void loadPHCalibration();
float calculateCalibratedPH(float rawVoltage);
void displayCalibrationScreen();
void displayCalibrationData();

#endif