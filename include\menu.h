#ifndef MENU_H
#define MENU_H
#include <Arduino.h>

enum MenuLevel {
    LEVEL_STATUS = 0,
    LEVEL_START,
    LEVEL_SETUP,
    LEVEL_MANUAL_MODE,
    LEVEL_AUTO_MODE,
    LEVEL_MANUAL_SETTINGS,
    LEVEL_CONFIG_PROFILES,
    LEVEL_SENSOR_SETTINGS,
    LEVEL_OUTPUT_SETTINGS,
    LEVEL_EDIT_VALUES,
    LEVEL_EDIT_pH,
    LEVEL_EDIT_Temp,
    LEVEL_EDIT_Gas,
    SetMinpH,
    SetMaxpH,
    SetMinTemp,
    SetMaxTemp,
    SetMinGas,
    SetMaxGas,
    PumpSetting,
    FanSetting,
    VoltageControl,
    CurrentControl,
    PumpSpeed,
    PumpTime,
    FanSpeed,
    FanTime,
    voltSet,
    curSet,
    NewUser,
    userA,
    resetProfile,
    SaveProfile,
    startProfile,
    nameUser,
    typeUser,
    LEVEL_ADD_USER,
    deleteProfile,
    updateProfil,
    risetDefault,
    autoMode,
    LEVEL_chooseProfile,
    testMotors,
    // Calibration menu levels
    LEVEL_CALIBRATION,          // Calibration main menu
    LEVEL_PH_CALIBRATION,       // pH Calibration submenu
    LEVEL_PH_CAL_4,             // pH 4.00 calibration
    LEVEL_PH_CAL_7,             // pH 6.86 calibration (near neutral)
    LEVEL_PH_CAL_9,             // pH 9.18 calibration
    LEVEL_PH_CAL_VIEW,          // View calibration data
    LEVEL_PH_CAL_RESET,         // Reset calibration
    // Data logging menu levels
    LEVEL_DATA_LOGGING,         // Data logging main menu
    LEVEL_LOG_CONFIG,           // Data logging configuration
    LEVEL_LOG_VIEW,             // View logged data
    LEVEL_LOG_DOWNLOAD,         // Download logs
    LEVEL_LOG_CLEAR             // Clear log buffer
};

enum MenuAction {
    ACTION_NONE,
    ACTION_ENTER,
    ACTION_BACK,
    ACTION_START,
    ACTION_STOP,
    ACTION_SAVE,
    ACTION_UPDATE,
    ACTION_DELETE,
    ACTION_LOAD,
    ACTION_RESET,
    ACTION_DISCARD,
    ACTION_TEST_MOTORS,
    // Data logging actions
    ACTION_LOG_TOGGLE,
    ACTION_LOG_VIEW,
    ACTION_LOG_SAVE_CSV,
    ACTION_LOG_SAVE_JSON,
    ACTION_LOG_CLEAR,
    ACTION_LOG_CONFIG
};

struct MenuItem {
    const char* text;
    MenuLevel nextLevel;
    MenuAction action;
    bool isEditable;
};

struct MenuState {
    MenuLevel currentLevel;
    int currentItem;
    int previousItem;
    int scrollPosition;
    bool isEditing;
    unsigned long lastActivity;
};

struct UserProfile {
    float pHMin, pHMax;
    int pumpSpeed, pumpTime;
    int FanSpeed, FanTime;
    float Volt, Current;
    int nameIndex;
    int typeIndex;
};

//==========================================================================
// Menu items definition - SESUAI menu_hierarchy_navigation_guide.md

// Status Menu - SESUAI menu_hierarchy_navigation_guide.md
const MenuItem statusMenu[] = {
    {"Start", LEVEL_START, ACTION_NONE, false},
    {"Setup", LEVEL_SETUP, ACTION_NONE, false}
};

// LEVEL_SETUP - Setup Menu
const MenuItem setupMenu[] = {
    {"Config Profiles", LEVEL_CONFIG_PROFILES, ACTION_NONE, false},
    {"Calibration", LEVEL_CALIBRATION, ACTION_NONE, false},
    {"Data Logging", LEVEL_DATA_LOGGING, ACTION_NONE, false},
    {"Test Motors", testMotors, ACTION_TEST_MOTORS, false},
    {"Reset to Default", risetDefault, ACTION_RESET, false},
    {"Back", LEVEL_STATUS, ACTION_BACK, false}
};
//LEVEL_SENSOR_SETTINGS - Sensor Settings Menu
const MenuItem sensorSettingsMenu[] = {
    {"pH Settings", LEVEL_EDIT_pH, ACTION_NONE, false},
    {"Temp Settings", LEVEL_EDIT_Temp, ACTION_NONE, false},
    {"Gas Settings", LEVEL_EDIT_Gas, ACTION_NONE, false},
    {"Back", LEVEL_MANUAL_SETTINGS, ACTION_BACK, false}
};
// LEVEL_EDIT_pH - pH Settings Menu
const MenuItem pHsettings[] = {
    {"Set Min: ", LEVEL_EDIT_pH, ACTION_NONE, true},
    {"Set Max: ", LEVEL_EDIT_pH, ACTION_NONE, true},
    {"Back", LEVEL_SENSOR_SETTINGS, ACTION_BACK, false}
};

// LEVEL_EDIT_Temp - Temperature Settings Menu
const MenuItem Tempsettings[] = {
    {"Set Min: ", LEVEL_EDIT_Temp, ACTION_NONE, true},
    {"Set Max: ", LEVEL_EDIT_Temp, ACTION_NONE, true},
    {"Back", LEVEL_SENSOR_SETTINGS, ACTION_BACK, false}
};

// LEVEL_EDIT_Gas - Gas Settings Menu
const MenuItem Gassettings[] = {
    {"Set Min (%): ", LEVEL_EDIT_Gas, ACTION_NONE, true},
    {"Set Max (%): ", LEVEL_EDIT_Gas, ACTION_NONE, true},
    {"Back", LEVEL_SENSOR_SETTINGS, ACTION_BACK, false}
};
//LEVEL_OUTPUT_SETTINGS - Output Settings Menu
const MenuItem outputSettings[] = {
    {"Pump Setting", PumpSetting, ACTION_NONE, false},
    {"Fan Setting", FanSetting, ACTION_NONE, false},
    {"Voltage Control", VoltageControl, ACTION_NONE, false},
    {"Current Control", CurrentControl, ACTION_NONE, false},
    {"Back", LEVEL_MANUAL_SETTINGS, ACTION_BACK, false}
};
// PumpSetting - Pump Settings Menu
const MenuItem pumpSetting[] = {
    {"Speed (%): ", PumpSetting, ACTION_NONE, true},
    {"Back", LEVEL_OUTPUT_SETTINGS, ACTION_BACK, false}
};

// FanSetting - Fan Settings Menu
const MenuItem fanSetting[] = {
    {"Speed (%): ", FanSetting, ACTION_NONE, true},
    {"Back", LEVEL_OUTPUT_SETTINGS, ACTION_BACK, false}
};

// VoltageControl - Voltage Control Menu
const MenuItem voltControl[] = {
    {"Volt (V): ", VoltageControl, ACTION_NONE, true},
    {"Back", LEVEL_OUTPUT_SETTINGS, ACTION_BACK, false}
};

// CurrentControl - Current Control Menu
const MenuItem currentControl[] = {
    {"Current (mA): ", CurrentControl, ACTION_NONE, true},
    {"Back", LEVEL_OUTPUT_SETTINGS, ACTION_BACK, false}
};
//LEVEL_CONFIG_PROFILES - Config Profiles Menu (Dynamic menu, handled in display function)
const MenuItem configProfiles[] = {
    {"User 1: Gold A", userA, ACTION_NONE, false},
    {"Create New User", NewUser, ACTION_NONE, false},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem newUser[] = {
    {"pH Min ", NewUser, ACTION_NONE, true},
    {"pH Max ", NewUser, ACTION_NONE, true},
    {"Pump % ", NewUser, ACTION_NONE, true},
    {"Pump (min) ", NewUser, ACTION_NONE, true},
    {"Fan % ", NewUser, ACTION_NONE, true},
    {"Fan (min) ", NewUser, ACTION_NONE, true},
    {"Volt ", NewUser, ACTION_NONE, true},
    {"Current ", NewUser, ACTION_NONE, true},
    {"Reset Profile ", resetProfile, ACTION_RESET, false},
    {"Save as Profile ", LEVEL_ADD_USER, ACTION_NONE, false},
    {"Back", LEVEL_CONFIG_PROFILES, ACTION_BACK, false}
};
const MenuItem UserA[] = {
    {"pH Min ", userA, ACTION_NONE, true},
    {"pH Max ", userA, ACTION_NONE, true},
    {"Pump % ", userA, ACTION_NONE, true},
    {"Pump (min) ", userA, ACTION_NONE, true},
    {"Fan % ", userA, ACTION_NONE, true},
    {"Fan (min) ", userA, ACTION_NONE, true},
    {"Volt ", userA, ACTION_NONE, true},
    {"Current ", userA, ACTION_NONE, true},
    {"Start Profile ", startProfile, ACTION_START, false},
    {"Reset Profile ", resetProfile, ACTION_RESET, false},
    {"Update Settings ", updateProfil, ACTION_UPDATE, false},
    {"Delete Profile ", deleteProfile, ACTION_DELETE, false},
    {"Back", LEVEL_CONFIG_PROFILES, ACTION_BACK, false}
};
const MenuItem addUser[] = {
    {"User Name ", LEVEL_ADD_USER, ACTION_NONE, true},
    {"Profile Type ", LEVEL_ADD_USER, ACTION_NONE, true},
    {"Save User", SaveProfile, ACTION_SAVE, false},
    {"Back", NewUser, ACTION_BACK, false}
};

// Start Menu - SESUAI menu_hierarchy_navigation_guide.md
const MenuItem startMenu[] = {
    {"Manual Mode", LEVEL_MANUAL_SETTINGS, ACTION_NONE, false},
    {"Auto Mode", LEVEL_chooseProfile, ACTION_NONE, false},
    {"Back", LEVEL_STATUS, ACTION_BACK, false}
};
//LEVEL_MANUAL_SETTINGS - Manual Settings Menu
const MenuItem manualSettingsMenu[] = {
    {"Sensor Settings", LEVEL_SENSOR_SETTINGS, ACTION_NONE, false},
    {"Output Settings", LEVEL_OUTPUT_SETTINGS, ACTION_NONE, false},
    {"Start Process", startProfile, ACTION_START, false},
    {"Back", LEVEL_START, ACTION_BACK, false}
};

//LEVEL_chooseProfile - Choose Profile Menu (Dynamic menu, handled in display function)
const MenuItem chooseProfile[] = {
    {"Back", LEVEL_START, ACTION_BACK, false}
};

// LEVEL_CALIBRATION - Calibration Main Menu
const MenuItem calibrationMenu[] = {
    {"pH Calibration", LEVEL_PH_CALIBRATION, ACTION_NONE, false},
    {"Temp Calibration", LEVEL_CALIBRATION, ACTION_NONE, false}, // Future feature
    {"Gas Calibration", LEVEL_CALIBRATION, ACTION_NONE, false},  // Future feature
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};

// LEVEL_PH_CALIBRATION - pH Calibration Menu (Three-Point)
const MenuItem pHCalibrationMenu[] = {
    {"pH 4.00 Cal", LEVEL_PH_CAL_4, ACTION_NONE, false},
    {"pH 6.86 Cal", LEVEL_PH_CAL_7, ACTION_NONE, false},
    {"pH 9.18 Cal", LEVEL_PH_CAL_9, ACTION_NONE, false},
    {"View Cal Data", LEVEL_PH_CAL_VIEW, ACTION_NONE, false},
    {"Reset Cal", LEVEL_PH_CAL_RESET, ACTION_NONE, false},
    {"Back", LEVEL_CALIBRATION, ACTION_BACK, false}
};

// LEVEL_PH_CAL_4 - pH 4.00 Calibration
const MenuItem pHCal4Menu[] = {
    {"Start Cal", LEVEL_PH_CAL_4, ACTION_NONE, false},
    {"Back", LEVEL_PH_CALIBRATION, ACTION_BACK, false}
};

// LEVEL_PH_CAL_7 - pH 6.86 Calibration
const MenuItem pHCal7Menu[] = {
    {"Start Cal", LEVEL_PH_CAL_7, ACTION_NONE, false},
    {"Back", LEVEL_PH_CALIBRATION, ACTION_BACK, false}
};

// LEVEL_PH_CAL_9 - pH 9.18 Calibration
const MenuItem pHCal9Menu[] = {
    {"Start Cal", LEVEL_PH_CAL_9, ACTION_NONE, false},
    {"Back", LEVEL_PH_CALIBRATION, ACTION_BACK, false}
};

// LEVEL_PH_CAL_VIEW - View Calibration Data
const MenuItem pHCalViewMenu[] = {
    {"Back", LEVEL_PH_CALIBRATION, ACTION_BACK, false}
};

// LEVEL_PH_CAL_RESET - Reset Calibration
const MenuItem pHCalResetMenu[] = {
    {"Confirm Reset", LEVEL_PH_CAL_RESET, ACTION_NONE, false},
    {"Cancel", LEVEL_PH_CALIBRATION, ACTION_BACK, false}
};

// LEVEL_DATA_LOGGING - Data Logging Main Menu
const MenuItem dataLoggingMenu[] = {
    {"Log Config", LEVEL_LOG_CONFIG, ACTION_NONE, false},
    {"View Logs", LEVEL_LOG_VIEW, ACTION_NONE, false},
    {"Download Logs", LEVEL_LOG_DOWNLOAD, ACTION_NONE, false},
    {"Clear Buffer", LEVEL_LOG_CLEAR, ACTION_NONE, false},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};

// LEVEL_LOG_CONFIG - Data Logging Configuration
const MenuItem logConfigMenu[] = {
    {"Enable/Disable", LEVEL_LOG_CONFIG, ACTION_LOG_TOGGLE, false},
    {"Set Interval", LEVEL_LOG_CONFIG, ACTION_NONE, true},
    {"Max Entries", LEVEL_LOG_CONFIG, ACTION_NONE, true},
    {"Auto Save", LEVEL_LOG_CONFIG, ACTION_LOG_TOGGLE, false},
    {"Save Interval", LEVEL_LOG_CONFIG, ACTION_NONE, true},
    {"Back", LEVEL_DATA_LOGGING, ACTION_BACK, false}
};

// LEVEL_LOG_VIEW - View Logged Data
const MenuItem logViewMenu[] = {
    {"Recent 10", LEVEL_LOG_VIEW, ACTION_LOG_VIEW, false},
    {"Recent 50", LEVEL_LOG_VIEW, ACTION_LOG_VIEW, false},
    {"All Entries", LEVEL_LOG_VIEW, ACTION_LOG_VIEW, false},
    {"Log Summary", LEVEL_LOG_VIEW, ACTION_LOG_VIEW, false},
    {"Back", LEVEL_DATA_LOGGING, ACTION_BACK, false}
};

// LEVEL_LOG_DOWNLOAD - Download Logs
const MenuItem logDownloadMenu[] = {
    {"Save to CSV", LEVEL_LOG_DOWNLOAD, ACTION_LOG_SAVE_CSV, false},
    {"Save to JSON", LEVEL_LOG_DOWNLOAD, ACTION_LOG_SAVE_JSON, false},
    {"Auto Save Now", LEVEL_LOG_DOWNLOAD, ACTION_LOG_CONFIG, false},
    {"Back", LEVEL_DATA_LOGGING, ACTION_BACK, false}
};

// LEVEL_LOG_CLEAR - Clear Log Buffer
const MenuItem logClearMenu[] = {
    {"Confirm Clear", LEVEL_LOG_CLEAR, ACTION_LOG_CLEAR, false},
    {"Cancel", LEVEL_DATA_LOGGING, ACTION_BACK, false}
};

#endif