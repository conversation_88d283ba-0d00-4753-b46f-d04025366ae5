Stack trace:
Frame         Function      Args
0007FFFF9F90  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E90) msys-2.0.dll+0x1FE8E
0007FFFF9F90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA268) msys-2.0.dll+0x67F9
0007FFFF9F90  000210046832 (000210286019, 0007FFFF9E48, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F90  000210068E24 (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA270  00021006A225 (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF893D00000 ntdll.dll
7FF8926A0000 KERNEL32.DLL
7FF891510000 KERNELBASE.dll
7FF893070000 USER32.dll
7FF890E30000 win32u.dll
7FF8925C0000 GDI32.dll
7FF891130000 gdi32full.dll
7FF890FE0000 msvcp_win.dll
7FF8913C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF892FB0000 advapi32.dll
7FF893C10000 msvcrt.dll
7FF893250000 sechost.dll
7FF892C80000 RPCRT4.dll
7FF890420000 CRYPTBASE.DLL
7FF891090000 bcryptPrimitives.dll
7FF8920D0000 IMM32.DLL
