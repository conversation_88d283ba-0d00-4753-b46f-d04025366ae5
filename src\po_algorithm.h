#ifndef PO_ALGORITHM_H
#define PO_ALGORITHM_H

#include <Arduino.h>

/**
 * @brief Modified Perturb and Observe (P&O) Algorithm for Electrowinning Optimization
 * 
 * This algorithm automatically adjusts voltage to find the optimal operating point
 * that maximizes efficiency while maintaining safe operating conditions.
 * 
 * Key Features:
 * - Adaptive step size based on system response
 * - Convergence detection to prevent oscillations
 * - Safety limits integration
 * - Gas-level based efficiency calculation
 * - Stability requirement before optimization
 */

enum class POState {
    PO_DISABLED,        // Algorithm disabled
    WAITING_STABILITY,  // Waiting for system to stabilize
    INITIALIZING,       // Setting initial conditions
    PERTURBING,         // Applying voltage perturbation
    OBSERVING,          // Measuring system response
    CONVERGED,          // Optimal point found
    SAFETY_HOLD         // Safety limits triggered
};

enum class PODirection {
    NONE,               // No direction set
    INCREASE,           // Increasing voltage
    DECREASE            // Decreasing voltage
};

struct POParameters {
    // Voltage control parameters
    float minVoltage = 5.0;         // Minimum safe voltage (V)
    float maxVoltage = 25.0;        // Maximum safe voltage (V)
    float initialStepSize = 0.5;    // Initial voltage step (V)
    float minStepSize = 0.1;        // Minimum voltage step (V)
    float maxStepSize = 2.0;        // Maximum voltage step (V)
    
    // Efficiency parameters
    float baseEfficiency = 80.0;    // Base efficiency percentage
    float gasImpactFactor = 0.003;  // Efficiency reduction per 1% gas
    float minEfficiency = 50.0;     // Minimum acceptable efficiency
    
    // Convergence parameters
    float convergenceThreshold = 0.5;   // Efficiency change threshold (%)
    int convergenceCount = 5;           // Consecutive stable readings needed
    int maxIterations = 50;             // Maximum optimization iterations
    
    // Timing parameters
    unsigned long perturbInterval = 5000;   // Time between perturbations (ms)
    unsigned long observeInterval = 3000;   // Time to observe after perturbation (ms)
    unsigned long stabilityTimeout = 30000; // Max time to wait for stability (ms)
    
    // Safety parameters
    float maxCurrentLimit = 18.0;       // Maximum current limit (A)
    float maxPowerLimit = 450.0;        // Maximum power limit (W)
    float maxGasLevel = 75.0;           // Maximum gas level (%)
    float minPHLevel = 2.0;             // Minimum pH level
    float maxPHLevel = 12.0;            // Maximum pH level
};

struct POStatus {
    POState state = POState::PO_DISABLED;
    PODirection direction = PODirection::NONE;
    
    // Current values
    float currentVoltage = 0.0;
    float currentEfficiency = 0.0;
    float currentPower = 0.0;
    
    // Previous values for comparison
    float previousVoltage = 0.0;
    float previousEfficiency = 0.0;
    float previousPower = 0.0;
    
    // Algorithm progress
    int iterationCount = 0;
    int convergenceCounter = 0;
    float currentStepSize = 0.5;
    
    // Timing
    unsigned long lastPerturbTime = 0;
    unsigned long lastObserveTime = 0;
    unsigned long algorithmStartTime = 0;
    
    // Performance metrics
    float maxEfficiencyFound = 0.0;
    float optimalVoltage = 0.0;
    float totalEnergyGain = 0.0;
    
    // Safety status
    bool safetyLimitTriggered = false;
    String lastSafetyReason = "";
};

class POAlgorithm {
private:
    POParameters mParams;
    POStatus mStatus;
    
    // External dependencies
    class FilterManager* mFilterManager;
    class ElectrowinningState* mElectrowinningState;
    class Xy6020* mXy6020;
    
    // Internal methods
    bool checkSystemStability();
    bool checkSafetyLimits();
    float calculateEfficiency();
    float calculatePower();
    void updateStepSize();
    void applyVoltageChange(float newVoltage);
    void logProgress();
    
public:
    POAlgorithm();
    ~POAlgorithm();
    
    // Initialization
    void init(FilterManager* filterManager, ElectrowinningState* electrowinningState, Xy6020* xy6020);
    void setParameters(const POParameters& params);
    
    // Control methods
    void enable();
    void disable();
    void reset();
    void pause();
    void resume();
    
    // Main algorithm execution
    void update();
    
    // Status and monitoring
    const POStatus& getStatus() const { return mStatus; }
    const POParameters& getParameters() const { return mParams; }
    bool isEnabled() const { return mStatus.state != POState::PO_DISABLED; }
    bool isConverged() const { return mStatus.state == POState::CONVERGED; }
    bool isSafetyTriggered() const { return mStatus.safetyLimitTriggered; }
    
    // Performance metrics
    float getEfficiencyGain() const;
    float getOptimalVoltage() const { return mStatus.optimalVoltage; }
    float getMaxEfficiencyFound() const { return mStatus.maxEfficiencyFound; }
    int getIterationCount() const { return mStatus.iterationCount; }
    
    // Configuration
    void setVoltageRange(float minV, float maxV);
    void setStepSize(float initialStep, float minStep, float maxStep);
    void setConvergenceThreshold(float threshold, int count);
    void setSafetyLimits(float maxCurrent, float maxPower, float maxGas);
    
    // Debug and monitoring
    void getStatusString(char* buffer, int bufferSize);
    void getPerformanceReport(char* buffer, int bufferSize);
    void printDebugInfo();
};

#endif // PO_ALGORITHM_H
