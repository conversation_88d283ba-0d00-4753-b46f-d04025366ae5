#include "electrowinning_state.h"
#include "xy6020.h"
#include <ArduinoJson.h>

// External motor control functions (defined in main.cpp)
extern void setPumpSpeed(int percentage);
extern void setFanSpeed(int percentage);

// Global instance
ElectrowinningState electrowinningState;

ElectrowinningState::ElectrowinningState() {
    // Initialize menu state
    mMenuState = {LEVEL_STATUS, 0, 0, 0, false, 0};
    
    // Initialize default limits
    mProcessLimits.pHMin = 1.0;
    mProcessLimits.pHMax = 13.0;
    mProcessLimits.tempMin = 15.0;
    mProcessLimits.tempMax = 80.0;
    mProcessLimits.gasMin = 20.0;  // 20% - Auto-enable fan threshold
    mProcessLimits.gasMax = 80.0;  // 80% - Emergency override threshold
    mProcessLimits.maxVoltage = 30.0;
    mProcessLimits.maxCurrent = 20.0;
    mProcessLimits.maxPower = 600.0;
}

void ElectrowinningState::init(Xy6020* xy6020, Preferences* prefs) {
    mXy6020 = xy6020;
    mPreferences = prefs;
    loadFromPreferences();
}

void ElectrowinningState::loadFromPreferences() {
    if (!mPreferences) return;
    
    // Load profile count
    mProfileCount = mPreferences->getInt("profileCount", 1);
    if (mProfileCount > 10) mProfileCount = 10;
    if (mProfileCount < 1) mProfileCount = 1;
    
    // Load profiles
    for (int i = 0; i < mProfileCount; i++) {
        String key = "profile" + String(i);
        size_t len = mPreferences->getBytesLength(key.c_str());
        if (len == sizeof(UserProfile)) {
            mPreferences->getBytes(key.c_str(), &mProfiles[i], sizeof(UserProfile));
        } else {
            // Initialize default profile
            mProfiles[i].pHMin = 2.0;
            mProfiles[i].pHMax = 12.0;
            mProfiles[i].pumpSpeed = 50;
            mProfiles[i].pumpTime = 30;
            mProfiles[i].FanSpeed = 70;
            mProfiles[i].FanTime = 15;
            mProfiles[i].Volt = 12.0;
            mProfiles[i].Current = 5.0;
            mProfiles[i].nameIndex = i + 1;
            mProfiles[i].typeIndex = 0; // Gold
        }
    }
    
    // Load process limits
    mProcessLimits.pHMin = mPreferences->getFloat("pHMin", 1.0);
    mProcessLimits.pHMax = mPreferences->getFloat("pHMax", 13.0);
    mProcessLimits.tempMin = mPreferences->getFloat("tempMin", 15.0);
    mProcessLimits.tempMax = mPreferences->getFloat("tempMax", 80.0);
    mProcessLimits.gasMin = mPreferences->getFloat("gasMin", 20.0);  // Default 20%
    mProcessLimits.gasMax = mPreferences->getFloat("gasMax", 80.0);  // Default 80%
    mProcessLimits.maxVoltage = mPreferences->getFloat("maxVolt", 60.0); // XY6020 max voltage
    mProcessLimits.maxCurrent = mPreferences->getFloat("maxCurr", 20.0); // XY6020 max current
    mProcessLimits.maxPower = mPreferences->getFloat("maxPower", 600.0);
}

void ElectrowinningState::update() {
    unsigned long now = millis();
    
    // Update power data from XY6020
    updatePowerData();
    
    // Update sensor data (placeholder - implement actual sensor reading)
    if (now - mLastSensorUpdate > 1000) { // Update every second
        updateSensorData();
        mLastSensorUpdate = now;
    }
    
    // Update output control timing
    updateOutputControl();
    
    // Check safety limits
    checkSafetyLimits();
    
    // Update process status
    if (mProcessStatus.isRunning) {
        mProcessStatus.elapsedTime = now - mProcessStatus.startTime;
    }
    
    // Save to preferences periodically
    if (now - mLastSave > 30000) { // Save every 30 seconds
        saveToPreferences();
        mLastSave = now;
    }
}

void ElectrowinningState::updatePowerData() {
    if (!mXy6020) return;
    
    mPowerData.isConnected = mXy6020->isConnected();
    if (mPowerData.isConnected) {
        mPowerData.actualVoltage = mXy6020->actualVoltage();
        mPowerData.actualCurrent = mXy6020->actualCurrent();
        mPowerData.actualPower = mXy6020->actualPower();
        mPowerData.inputVoltage = mXy6020->inputVoltage();
        mPowerData.targetVoltage = mXy6020->targetVoltage();
        mPowerData.targetCurrent = mXy6020->maxCurrent();
        mPowerData.outputEnabled = mXy6020->outputEnabled();
    }
}

// External function declarations (defined in main.cpp)
extern float readMQ8HydrogenLevel();
extern float readPHSensor();
extern float readTemperatureSensor();
// Raw sensor reading functions (for electrowinning state)
extern float readRawMQ8HydrogenLevel();
extern float readRawPHSensor();
extern float readRawTemperatureSensor();
// Filtered sensor reading functions (smart automation)
extern float getFilteredPH();
extern float getFilteredGas();
extern float getFilteredTemperature();
extern float getFilteredPower();
extern float getFilteredEfficiency();
extern bool isSystemStableForOptimization();

void ElectrowinningState::updateSensorData() {
    // Use filtered sensor data for better stability and accuracy
    mSensorData.gasLevel = getFilteredGas();
    mSensorData.pH = getFilteredPH();
    mSensorData.temperature = getFilteredTemperature();

    // Update smart automation data
    mSensorData.filteredPower = getFilteredPower();
    mSensorData.filteredEfficiency = getFilteredEfficiency();
    mSensorData.systemStable = isSystemStableForOptimization();

    // Calculate noise reduction percentage
    float rawPH = readRawPHSensor();
    float rawGas = readRawMQ8HydrogenLevel();
    float rawTemp = readRawTemperatureSensor();

    float totalRawVariation = abs(rawPH - mSensorData.pH) +
                             abs(rawGas - mSensorData.gasLevel) +
                             abs(rawTemp - mSensorData.temperature);
    float maxVariation = 14.0 + 100.0 + 125.0; // Max possible variations
    mSensorData.noiseReduction = (totalRawVariation / maxVariation) * 100.0;
    mSensorData.noiseReduction = constrain(mSensorData.noiseReduction, 0.0, 100.0);

    mSensorData.lastUpdate = millis();
    mSensorData.isValid = true;

    // Check system stability for smart automation
    static bool lastStabilityState = false;
    bool currentStabilityState = mSensorData.systemStable;

    if (currentStabilityState != lastStabilityState) {
        Serial.printf("*** SYSTEM STABILITY CHANGE: %s -> %s ***\n",
                     lastStabilityState ? "STABLE" : "UNSTABLE",
                     currentStabilityState ? "STABLE" : "UNSTABLE");
        lastStabilityState = currentStabilityState;
    }

    // Debug output for sensor readings with raw vs filtered comparison
    static unsigned long lastSensorDebug = 0;
    if (millis() - lastSensorDebug > 5000) { // Debug every 5 seconds
        // Get raw sensor values for comparison
        float rawPH = readRawPHSensor();
        float rawGas = readRawMQ8HydrogenLevel();
        float rawTemp = readRawTemperatureSensor();

        // Get filtered values (already stored in mSensorData)
        float filteredPH = mSensorData.pH;
        float filteredGas = mSensorData.gasLevel;
        float filteredTemp = mSensorData.temperature;

        // Additional smart automation data
        float filteredPower = getFilteredPower();
        float filteredEfficiency = getFilteredEfficiency();
        bool systemStable = isSystemStableForOptimization();

        Serial.println("=== SENSOR DATA COMPARISON ===");
        Serial.printf("pH:      Raw=%.2f | Filtered=%.2f | Diff=%.2f\n",
                     rawPH, filteredPH, abs(rawPH - filteredPH));
        Serial.printf("Gas:     Raw=%.1f%% | Filtered=%.1f%% | Diff=%.1f%%\n",
                     rawGas, filteredGas, abs(rawGas - filteredGas));
        Serial.printf("Temp:    Raw=%.1f°C | Filtered=%.1f°C | Diff=%.1f°C\n",
                     rawTemp, filteredTemp, abs(rawTemp - filteredTemp));
        Serial.printf("Power:   Filtered=%.1fW | Efficiency=%.1f%% | Stable=%s\n",
                     filteredPower, filteredEfficiency, systemStable ? "YES" : "NO");

        // Gas level status
        if (mSensorData.gasLevel < mProcessLimits.gasMin) {
            Serial.println("  Gas Status: BELOW MIN - Fan will not activate");
        } else if (mSensorData.gasLevel > mProcessLimits.gasMax) {
            Serial.println("  Gas Status: ABOVE MAX - Fan will run at MAXIMUM speed!");
        } else {
            Serial.println("  Gas Status: NORMAL - Fan will run at configured speed");
        }

        lastSensorDebug = millis();
    }
}

void ElectrowinningState::updateOutputControl() {
    unsigned long now = millis();

    // Check pump timing
    if (mOutputControl.pumpEnabled && mOutputControl.pumpTime > 0) {
        unsigned long elapsed = (now - mOutputControl.pumpStartTime) / 60000; // minutes
        if (elapsed >= mOutputControl.pumpTime) {
            mOutputControl.pumpEnabled = false;
            Serial.println("Pump timer expired, stopping pump");
        }
    }

    // Gas-based fan control logic
    updateGasBasedFanControl();

    // Check fan timing (only if not overridden by gas control)
    if (mOutputControl.fanEnabled && mOutputControl.fanTime > 0 && !mGasControlActive) {
        unsigned long elapsed = (now - mOutputControl.fanStartTime) / 60000; // minutes
        if (elapsed >= mOutputControl.fanTime) {
            mOutputControl.fanEnabled = false;
            Serial.println("Fan timer expired, stopping fan");
        }
    }
}

void ElectrowinningState::updateGasBasedFanControl() {
    static bool lastGasControlActive = false;
    static int originalFanSpeed = 0;

    // Only apply gas control when process is running
    if (!mProcessStatus.isRunning) {
        mGasControlActive = false;
        return;
    }

    float gasLevel = mSensorData.gasLevel;

    if (gasLevel >= mProcessLimits.gasMax) {
        // Gas level ABOVE MAX - Override fan to maximum speed
        if (!mGasControlActive) {
            originalFanSpeed = mOutputControl.fanSpeed; // Save original speed
            Serial.printf("GAS ALERT: Level %.1f%% exceeds MAX %.1f%% - Fan override to 100%%\n",
                         gasLevel, mProcessLimits.gasMax);
        }
        mGasControlActive = true;
        setFanSpeed(100); // Maximum speed
        enableFan(true);

    } else if (gasLevel >= mProcessLimits.gasMin) {
        // Gas level ABOVE MIN but BELOW MAX - Use configured fan speed
        if (mGasControlActive && gasLevel < mProcessLimits.gasMax) {
            // Restore original fan speed
            Serial.printf("Gas level %.1f%% normalized - Restoring fan to %d%%\n",
                         gasLevel, originalFanSpeed);
            setFanSpeed(originalFanSpeed);
            mGasControlActive = false;
        }

        if (!mGasControlActive) {
            // Normal operation - fan runs at configured speed if enabled
            if (mOutputControl.fanEnabled) {
                // Fan already enabled, keep current speed
            } else {
                // Auto-enable fan when gas exceeds minimum
                Serial.printf("Gas level %.1f%% exceeds MIN %.1f%% - Auto-enabling fan\n",
                             gasLevel, mProcessLimits.gasMin);
                enableFan(true);
            }
        }

    } else {
        // Gas level BELOW MIN - Fan can be disabled (but respect manual control)
        if (mGasControlActive) {
            Serial.printf("Gas level %.1f%% below MIN %.1f%% - Gas control deactivated\n",
                         gasLevel, mProcessLimits.gasMin);
            mGasControlActive = false;
            // Don't auto-disable fan, let manual control handle it
        }
    }

    // Debug gas control status change
    if (mGasControlActive != lastGasControlActive) {
        Serial.printf("Gas control status changed: %s\n", mGasControlActive ? "ACTIVE" : "INACTIVE");
        lastGasControlActive = mGasControlActive;
    }
}

void ElectrowinningState::checkSafetyLimits() {
    bool safetyViolation = false;
    String errorMsg = "";
    
    // Check sensor limits
    if (mSensorData.isValid) {
        if (mSensorData.pH < mProcessLimits.pHMin || mSensorData.pH > mProcessLimits.pHMax) {
            safetyViolation = true;
            errorMsg += "pH out of range (" + String(mSensorData.pH) + "); ";
        }
        
        // TEMPERATURE EMERGENCY: Special handling - stop process but keep fan
        if (mSensorData.temperature > mProcessLimits.tempMax) {
            Serial.printf("TEMPERATURE EMERGENCY: %.1f°C exceeds max %.1f°C\n",
                         mSensorData.temperature, mProcessLimits.tempMax);

            if (mProcessStatus.isRunning) {
                // Stop XY6020 output
                enableOutput(false);

                // Stop pump
                enablePump(false);

                // Keep fan running at maximum speed for cooling
                setFanSpeed(100);
                enableFan(true);

                // Mark process as stopped
                mProcessStatus.isRunning = false;

                setError("Temperature emergency - process stopped, fan cooling active");
                Serial.println("EMERGENCY STOP: Temperature too high - fan cooling activated");
                return; // Exit early, don't trigger normal safety violation
            }
        } else if (mSensorData.temperature < mProcessLimits.tempMin) {
            safetyViolation = true;
            errorMsg += "Temperature too low (" + String(mSensorData.temperature) + "°C); ";
        }
        
        if (mSensorData.gasLevel > mProcessLimits.gasMax) {
            safetyViolation = true;
            errorMsg += "Gas level too high (" + String(mSensorData.gasLevel) + "); ";
        }
    }
    
    // Check power limits
    if (mPowerData.actualVoltage > mProcessLimits.maxVoltage) {
        safetyViolation = true;
        errorMsg += "Voltage too high (" + String(mPowerData.actualVoltage) + "V); ";
    }
    
    if (mPowerData.actualCurrent > mProcessLimits.maxCurrent) {
        safetyViolation = true;
        errorMsg += "Current too high (" + String(mPowerData.actualCurrent) + "A); ";
    }
    
    if (mPowerData.actualPower > mProcessLimits.maxPower) {
        safetyViolation = true;
        errorMsg += "Power too high (" + String(mPowerData.actualPower) + "W); ";
    }
    
    if (safetyViolation) {
        setError("Safety violation: " + errorMsg);
        if (mProcessStatus.isRunning) {
            stopProcess();
            Serial.println("Process stopped due to safety violation: " + errorMsg);
        }
    }
}

void ElectrowinningState::saveToPreferences() {
    if (!mPreferences) return;
    
    // Save profile count
    mPreferences->putInt("profileCount", mProfileCount);
    
    // Save profiles
    for (int i = 0; i < mProfileCount; i++) {
        String key = "profile" + String(i);
        mPreferences->putBytes(key.c_str(), &mProfiles[i], sizeof(UserProfile));
    }
    
    // Save process limits
    mPreferences->putFloat("pHMin", mProcessLimits.pHMin);
    mPreferences->putFloat("pHMax", mProcessLimits.pHMax);
    mPreferences->putFloat("tempMin", mProcessLimits.tempMin);
    mPreferences->putFloat("tempMax", mProcessLimits.tempMax);
    mPreferences->putFloat("gasMin", mProcessLimits.gasMin);
    mPreferences->putFloat("gasMax", mProcessLimits.gasMax);
    mPreferences->putFloat("maxVolt", mProcessLimits.maxVoltage);
    mPreferences->putFloat("maxCurr", mProcessLimits.maxCurrent);
    mPreferences->putFloat("maxPower", mProcessLimits.maxPower);
}

// Sensor data methods
void ElectrowinningState::setSensorData(float pH, float temp, float gas) {
    mSensorData.pH = pH;
    mSensorData.temperature = temp;
    mSensorData.gasLevel = gas;
    mSensorData.lastUpdate = millis();
    mSensorData.isValid = true;
}

// Output control methods
void ElectrowinningState::setPumpSpeed(int speed) {
    mOutputControl.pumpSpeed = constrain(speed, 0, 100);
    // Apply to hardware PWM
    ::setPumpSpeed(mOutputControl.pumpSpeed);
}

void ElectrowinningState::setFanSpeed(int speed) {
    mOutputControl.fanSpeed = constrain(speed, 0, 100);
    // Apply to hardware PWM
    ::setFanSpeed(mOutputControl.fanSpeed);
}

void ElectrowinningState::setPumpTime(int minutes) {
    mOutputControl.pumpTime = max(0, minutes);
}

void ElectrowinningState::setFanTime(int minutes) {
    mOutputControl.fanTime = max(0, minutes);
}

void ElectrowinningState::enablePump(bool enable) {
    if (enable && !mOutputControl.pumpEnabled) {
        mOutputControl.pumpStartTime = millis();
    }
    mOutputControl.pumpEnabled = enable;

    // Apply to hardware PWM
    if (enable) {
        ::setPumpSpeed(mOutputControl.pumpSpeed);
    } else {
        ::setPumpSpeed(0); // Turn off pump
    }
}

void ElectrowinningState::enableFan(bool enable) {
    if (enable && !mOutputControl.fanEnabled) {
        mOutputControl.fanStartTime = millis();
    }
    mOutputControl.fanEnabled = enable;

    // Apply to hardware PWM
    if (enable) {
        ::setFanSpeed(mOutputControl.fanSpeed);
    } else {
        ::setFanSpeed(0); // Turn off fan
    }
}

// Power control methods
void ElectrowinningState::setTargetVoltage(float voltage) {
    mPowerData.targetVoltage = constrain(voltage, 0.0, mProcessLimits.maxVoltage);
    if (mXy6020) {
        mXy6020->setTargetVoltage(mPowerData.targetVoltage);
    }
}

void ElectrowinningState::setTargetCurrent(float current) {
    mPowerData.targetCurrent = constrain(current, 0.0, mProcessLimits.maxCurrent);
    if (mXy6020) {
        mXy6020->setMaxCurrent(mPowerData.targetCurrent);
    }
}

void ElectrowinningState::enableOutput(bool enable) {
    Serial.printf("enableOutput(%s) - XY6020: %s, Connected: %s\n",
                 enable ? "true" : "false",
                 mXy6020 ? "OK" : "NULL",
                 mPowerData.isConnected ? "YES" : "NO");

    if (mXy6020) {
        mXy6020->setOutputEnabled(enable);
        Serial.printf("XY6020 setOutputEnabled(%s) called\n", enable ? "true" : "false");
    } else {
        Serial.println("ERROR: XY6020 pointer is NULL!");
    }
    mPowerData.outputEnabled = enable;
}

// Process control methods
void ElectrowinningState::startProcess(bool manualMode, int profileIndex) {
    // Use strict pre-start safety check
    if (!canStartProcess()) {
        setError("Cannot start process: pre-start safety conditions not met");
        return;
    }

    Serial.println("startProcess() - Setting isRunning = true");
    mProcessStatus.isRunning = true;
    mProcessStatus.isManualMode = manualMode;
    mProcessStatus.currentProfileIndex = profileIndex;
    mProcessStatus.startTime = millis();
    mProcessStatus.elapsedTime = 0;
    clearError();

    // Load profile if in auto mode
    if (!manualMode && profileIndex >= 0 && profileIndex < mProfileCount) {
        loadProfile(profileIndex);
    }

    // Enable XY6020 output to start the process
    enableOutput(true);

    Serial.println("Process started - Mode: " + String(manualMode ? "Manual" : "Auto"));
    Serial.printf("XY6020 Output enabled - Target: %.1fV, %.1fA\n", mPowerData.targetVoltage, mPowerData.targetCurrent);
}

void ElectrowinningState::stopProcess() {
    Serial.println("stopProcess() called - Setting isRunning = false");
    mProcessStatus.isRunning = false;

    // Stop all outputs
    enablePump(false);
    enableFan(false);
    enableOutput(false);

    Serial.println("Process stopped - All outputs disabled");
}

void ElectrowinningState::pauseProcess() {
    if (mProcessStatus.isRunning) {
        enableOutput(false);
        Serial.println("Process paused");
    }
}

void ElectrowinningState::resumeProcess() {
    if (mProcessStatus.isRunning && isSafeToOperate()) {
        enableOutput(true);
        Serial.println("Process resumed");
    }
}

// Profile management methods
const UserProfile& ElectrowinningState::getProfile(int index) const {
    if (index >= 0 && index < mProfileCount) {
        return mProfiles[index];
    }
    return mProfiles[0]; // Return first profile as default
}

void ElectrowinningState::setProfile(int index, const UserProfile& profile) {
    if (index >= 0 && index < 10) {
        mProfiles[index] = profile;
        if (index >= mProfileCount) {
            mProfileCount = index + 1;
        }
    }
}

void ElectrowinningState::loadProfile(int index) {
    if (index >= 0 && index < mProfileCount) {
        const UserProfile& profile = mProfiles[index];

        // Load sensor limits
        mProcessLimits.pHMin = profile.pHMin;
        mProcessLimits.pHMax = profile.pHMax;

        // Load output settings
        setPumpSpeed(profile.pumpSpeed);
        setPumpTime(profile.pumpTime);
        setFanSpeed(profile.FanSpeed);
        setFanTime(profile.FanTime);

        // Load power settings
        setTargetVoltage(profile.Volt);
        setTargetCurrent(profile.Current);

        Serial.println("Profile " + String(index) + " loaded");
    }
}

void ElectrowinningState::saveProfile(int index) {
    if (index >= 0 && index < 10) {
        UserProfile& profile = mProfiles[index];

        // Save current settings to profile
        profile.pHMin = mProcessLimits.pHMin;
        profile.pHMax = mProcessLimits.pHMax;
        profile.pumpSpeed = mOutputControl.pumpSpeed;
        profile.pumpTime = mOutputControl.pumpTime;
        profile.FanSpeed = mOutputControl.fanSpeed;
        profile.FanTime = mOutputControl.fanTime;
        profile.Volt = mPowerData.targetVoltage;
        profile.Current = mPowerData.targetCurrent;

        if (index >= mProfileCount) {
            mProfileCount = index + 1;
        }

        saveToPreferences();
        Serial.println("Profile " + String(index) + " saved");
    }
}

// Safety and limits
void ElectrowinningState::setLimits(const ProcessLimits& limits) {
    mProcessLimits = limits;
}

bool ElectrowinningState::isSafeToOperate() const {
    // Check if XY6020 is connected
    if (!mPowerData.isConnected) {
        Serial.println("Safety check failed: XY6020 not connected");
        return false;
    }

    // Check if sensor data is valid and within limits
    if (mSensorData.isValid) {
        // CRITICAL: pH Safety Check - Must be within range to start/continue
        if (mSensorData.pH < mProcessLimits.pHMin || mSensorData.pH > mProcessLimits.pHMax) {
            Serial.printf("CRITICAL: pH %.1f out of safe range [%.1f-%.1f] - STOPPING PROCESS\n",
                         mSensorData.pH, mProcessLimits.pHMin, mProcessLimits.pHMax);
            return false;
        }

        // Temperature monitoring during process
        if (mSensorData.temperature > mProcessLimits.tempMax) {
            Serial.printf("TEMPERATURE ALERT: %.1f°C exceeds max %.1f°C - STOPPING PROCESS (except fan)\n",
                         mSensorData.temperature, mProcessLimits.tempMax);
            return false;
        }

        if (mSensorData.gasLevel > mProcessLimits.gasMax) {
            Serial.printf("Safety check failed: Gas level %.1f exceeds max %.1f\n",
                         mSensorData.gasLevel, mProcessLimits.gasMax);
            return false;
        }
    } else {
        Serial.println("Safety check: Sensor data not valid, but allowing operation");
    }

    Serial.println("Safety check passed - safe to operate");
    return true;
}

bool ElectrowinningState::canStartProcess() const {
    // Pre-start safety checks - more strict than runtime checks
    if (!mSensorData.isValid) {
        Serial.println("Cannot start: Sensor data not valid");
        return false;
    }

    // CRITICAL: pH must be within safe range before starting
    if (mSensorData.pH < mProcessLimits.pHMin || mSensorData.pH > mProcessLimits.pHMax) {
        Serial.printf("CANNOT START: pH %.1f outside safe range [%.1f-%.1f]\n",
                     mSensorData.pH, mProcessLimits.pHMin, mProcessLimits.pHMax);
        // setError("pH out of safe range - cannot start electrowinning"); // Cannot call non-const from const function
        return false;
    }

    // Temperature should be within reasonable starting range
    if (mSensorData.temperature < mProcessLimits.tempMin || mSensorData.temperature > mProcessLimits.tempMax) {
        Serial.printf("Cannot start: Temperature %.1f°C outside range [%.1f-%.1f°C]\n",
                     mSensorData.temperature, mProcessLimits.tempMin, mProcessLimits.tempMax);
        return false;
    }

    // Gas level should be safe to start
    if (mSensorData.gasLevel > mProcessLimits.gasMax) {
        Serial.printf("Cannot start: Gas level %.1f%% exceeds max %.1f%%\n",
                     mSensorData.gasLevel, mProcessLimits.gasMax);
        return false;
    }

    Serial.println("Pre-start safety check PASSED - safe to start electrowinning");
    Serial.printf("Starting conditions - pH: %.1f, Temp: %.1f°C, Gas: %.1f%%\n",
                 mSensorData.pH, mSensorData.temperature, mSensorData.gasLevel);
    return true;
}

// Error handling
void ElectrowinningState::setError(const String& error) {
    mProcessStatus.lastError = error;
    mProcessStatus.errorCount++;
    Serial.println("ERROR: " + error);
}

void ElectrowinningState::clearError() {
    mProcessStatus.lastError = "";
}

// JSON serialization for web/MQTT
String ElectrowinningState::toJson() const {
    JsonDocument doc;

    // Sensor data
    JsonObject sensors = doc["sensors"];
    sensors["pH"] = mSensorData.pH;
    sensors["temperature"] = mSensorData.temperature;
    sensors["gasLevel"] = mSensorData.gasLevel;
    sensors["isValid"] = mSensorData.isValid;
    sensors["lastUpdate"] = mSensorData.lastUpdate;

    // Output control
    JsonObject outputs = doc["outputs"];
    outputs["pumpSpeed"] = mOutputControl.pumpSpeed;
    outputs["fanSpeed"] = mOutputControl.fanSpeed;
    outputs["pumpTime"] = mOutputControl.pumpTime;
    outputs["fanTime"] = mOutputControl.fanTime;
    outputs["pumpEnabled"] = mOutputControl.pumpEnabled;
    outputs["fanEnabled"] = mOutputControl.fanEnabled;

    // Power data
    JsonObject power = doc["power"];
    power["targetVoltage"] = mPowerData.targetVoltage;
    power["targetCurrent"] = mPowerData.targetCurrent;
    power["actualVoltage"] = mPowerData.actualVoltage;
    power["actualCurrent"] = mPowerData.actualCurrent;
    power["actualPower"] = mPowerData.actualPower;
    power["inputVoltage"] = mPowerData.inputVoltage;
    power["outputEnabled"] = mPowerData.outputEnabled;
    power["isConnected"] = mPowerData.isConnected;

    // Process status
    JsonObject status = doc["status"];
    status["isRunning"] = mProcessStatus.isRunning;
    status["isManualMode"] = mProcessStatus.isManualMode;
    status["currentProfileIndex"] = mProcessStatus.currentProfileIndex;
    status["elapsedTime"] = mProcessStatus.elapsedTime;
    status["lastError"] = mProcessStatus.lastError;
    status["errorCount"] = mProcessStatus.errorCount;

    String result;
    serializeJson(doc, result);
    return result;
}

bool ElectrowinningState::fromJson(const String& json) {
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, json);

    if (error) {
        setError("JSON parsing error: " + String(error.c_str()));
        return false;
    }

    // Parse commands from JSON
    if (doc.containsKey("commands")) {
        JsonObject commands = doc["commands"];

        if (commands.containsKey("startProcess")) {
            bool manual = commands["startProcess"]["manual"] | true;
            int profile = commands["startProcess"]["profile"] | -1;
            startProcess(manual, profile);
        }

        if (commands.containsKey("stopProcess") && commands["stopProcess"]) {
            stopProcess();
        }

        if (commands.containsKey("setPumpSpeed")) {
            setPumpSpeed(commands["setPumpSpeed"]);
        }

        if (commands.containsKey("setFanSpeed")) {
            setFanSpeed(commands["setFanSpeed"]);
        }

        if (commands.containsKey("setVoltage")) {
            setTargetVoltage(commands["setVoltage"]);
        }

        if (commands.containsKey("setCurrent")) {
            setTargetCurrent(commands["setCurrent"]);
        }

        if (commands.containsKey("enableOutput")) {
            enableOutput(commands["enableOutput"]);
        }
    }

    return true;
}

// Display helpers
String ElectrowinningState::getStatusString() const {
    if (mProcessStatus.isRunning) {
        return mProcessStatus.isManualMode ? "MANUAL" : "AUTO";
    } else {
        return "STOPPED";
    }
}

String ElectrowinningState::getElapsedTimeString() const {
    unsigned long seconds = mProcessStatus.elapsedTime / 1000;
    unsigned long minutes = seconds / 60;
    unsigned long hours = minutes / 60;

    seconds %= 60;
    minutes %= 60;

    return String(hours) + ":" +
           (minutes < 10 ? "0" : "") + String(minutes) + ":" +
           (seconds < 10 ? "0" : "") + String(seconds);
}
