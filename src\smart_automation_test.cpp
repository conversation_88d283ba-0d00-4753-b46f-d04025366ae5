#include "smart_automation_test.h"
#include "moving_average_filter.h"
#include "po_algorithm.h"
#include "efficiency_calculator.h"
#include <math.h>

SmartAutomationTest::SmartAutomationTest() {
    // Initialize with default configuration
    mConfig.enableVerboseOutput = true;
    mConfig.enablePerformanceTesting = true;
    mConfig.enableStressTest = false;
    mConfig.testIterations = 100;
    
    Serial.println("SmartAutomationTest: Initialized with default configuration");
}

void SmartAutomationTest::setConfiguration(const TestConfiguration& config) {
    mConfig = config;
    Serial.println("SmartAutomationTest: Configuration updated");
}

bool SmartAutomationTest::runAllTests() {
    Serial.println("\n=== SMART AUTOMATION COMPREHENSIVE TEST SUITE ===");
    Serial.println("Starting comprehensive testing of all smart automation components...\n");
    
    mResults = TestResults(); // Reset results
    mResults.startTime = millis();
    mResults.totalTests = 16; // Total number of individual tests
    
    // Run all test categories
    bool filterTestsPass = runFilterTests();
    bool poTestsPass = runPOAlgorithmTests();
    bool efficiencyTestsPass = runEfficiencyTests();
    bool integrationTestsPass = runIntegrationTests();
    
    // Optional stress test
    bool stressTestPass = true;
    if (mConfig.enableStressTest) {
        stressTestPass = runStressTest();
    }
    
    // Calculate final results
    mResults.endTime = millis();
    mResults.testDuration = mResults.endTime - mResults.startTime;
    mResults.successRate = (float)mResults.passedTests / mResults.totalTests * 100.0;
    mResults.isComplete = true;
    
    // Print summary
    printTestSummary();
    
    return allTestsPassed() && stressTestPass;
}

bool SmartAutomationTest::runFilterTests() {
    Serial.println("--- FILTER SYSTEM TESTS ---");
    
    mResults.filterInitialization = testFilterInitialization();
    mResults.filterAccuracy = testFilterAccuracy();
    mResults.filterStability = testFilterStability();
    mResults.filterPerformance = testFilterPerformance();
    
    int filterTestsPassed = mResults.filterInitialization + mResults.filterAccuracy + 
                           mResults.filterStability + mResults.filterPerformance;
    
    Serial.printf("Filter Tests: %d/4 passed\n\n", filterTestsPassed);
    return filterTestsPassed == 4;
}

bool SmartAutomationTest::testFilterInitialization() {
    printTestResult("Filter Initialization", false);
    
    try {
        // Test MovingAverageFilter initialization
        MovingAverageFilter maFilter(10);
        if (!maFilter.isStable()) {
            Serial.println("  ✓ MovingAverageFilter initialized correctly (not stable initially)");
        } else {
            Serial.println("  ✗ MovingAverageFilter should not be stable initially");
            return false;
        }
        
        // Test EMAFilter initialization
        EMAFilter emaFilter(0.1);
        if (emaFilter.getValue() == 0.0) {
            Serial.println("  ✓ EMAFilter initialized correctly (zero initial value)");
        } else {
            Serial.println("  ✗ EMAFilter should have zero initial value");
            return false;
        }
        
        // Test FilterManager initialization
        FilterManager filterManager;
        Serial.println("  ✓ FilterManager initialized successfully");
        
        mResults.passedTests++;
        printTestResult("Filter Initialization", true);
        return true;
        
    } catch (...) {
        Serial.println("  ✗ Exception during filter initialization");
        printTestResult("Filter Initialization", false);
        return false;
    }
}

bool SmartAutomationTest::testFilterAccuracy() {
    printTestResult("Filter Accuracy", false);
    
    MovingAverageFilter maFilter(5);
    EMAFilter emaFilter(0.2);
    
    // Test with known values
    float testValues[] = {10.0, 12.0, 8.0, 15.0, 11.0, 9.0, 13.0, 10.0};
    int numValues = sizeof(testValues) / sizeof(testValues[0]);
    
    float expectedMA = 0.0;
    for (int i = 0; i < numValues; i++) {
        maFilter.addSample(testValues[i]);
        emaFilter.addSample(testValues[i]);
        expectedMA += testValues[i];
    }
    expectedMA /= numValues; // Simple average for comparison
    
    float maResult = maFilter.getAverage();
    float emaResult = emaFilter.getValue();
    
    // Check if results are within tolerance
    bool maAccurate = compareFloats(maResult, expectedMA, mConfig.filterTolerance);
    bool emaReasonable = (emaResult > 8.0 && emaResult < 15.0); // Should be in reasonable range
    
    if (maAccurate && emaReasonable) {
        Serial.printf("  ✓ MA Filter: %.2f (expected ~%.2f)\n", maResult, expectedMA);
        Serial.printf("  ✓ EMA Filter: %.2f (within reasonable range)\n", emaResult);
        mResults.passedTests++;
        printTestResult("Filter Accuracy", true);
        return true;
    } else {
        Serial.printf("  ✗ MA Filter: %.2f (expected ~%.2f)\n", maResult, expectedMA);
        Serial.printf("  ✗ EMA Filter: %.2f (outside reasonable range)\n", emaResult);
        printTestResult("Filter Accuracy", false);
        return false;
    }
}

bool SmartAutomationTest::testFilterStability() {
    printTestResult("Filter Stability", false);
    
    MovingAverageFilter maFilter(10);
    
    // Add enough samples to reach stability
    for (int i = 0; i < 15; i++) {
        maFilter.addSample(10.0 + (i % 3)); // Small variations around 10
    }
    
    if (maFilter.isStable()) {
        Serial.println("  ✓ Filter reached stability after sufficient samples");
        mResults.passedTests++;
        printTestResult("Filter Stability", true);
        return true;
    } else {
        Serial.println("  ✗ Filter should be stable after sufficient samples");
        printTestResult("Filter Stability", false);
        return false;
    }
}

bool SmartAutomationTest::testFilterPerformance() {
    printTestResult("Filter Performance", false);
    
    unsigned long startTime = micros();
    
    MovingAverageFilter maFilter(20);
    EMAFilter emaFilter(0.1);
    
    // Performance test with many samples
    for (int i = 0; i < 1000; i++) {
        float value = 10.0 + sin(i * 0.1) * 2.0; // Sine wave data
        maFilter.addSample(value);
        emaFilter.addSample(value);
    }
    
    unsigned long endTime = micros();
    unsigned long duration = endTime - startTime;
    
    // Should complete in reasonable time (< 10ms for 1000 samples)
    if (duration < 10000) {
        Serial.printf("  ✓ Performance test: %lu μs for 1000 samples\n", duration);
        mResults.passedTests++;
        printTestResult("Filter Performance", true);
        return true;
    } else {
        Serial.printf("  ✗ Performance test: %lu μs (too slow)\n", duration);
        printTestResult("Filter Performance", false);
        return false;
    }
}

bool SmartAutomationTest::runPOAlgorithmTests() {
    Serial.println("--- P&O ALGORITHM TESTS ---");
    
    mResults.poInitialization = testPOInitialization();
    mResults.poStateMachine = testPOStateMachine();
    mResults.poOptimization = testPOOptimization();
    mResults.poSafety = testPOSafety();
    
    int poTestsPassed = mResults.poInitialization + mResults.poStateMachine + 
                       mResults.poOptimization + mResults.poSafety;
    
    Serial.printf("P&O Algorithm Tests: %d/4 passed\n\n", poTestsPassed);
    return poTestsPassed == 4;
}

bool SmartAutomationTest::testPOInitialization() {
    printTestResult("P&O Initialization", false);
    
    try {
        // Test P&O Algorithm initialization
        extern POAlgorithm poAlgorithm;
        
        // Check initial state
        if (!poAlgorithm.isEnabled()) {
            Serial.println("  ✓ P&O Algorithm starts in disabled state");
        } else {
            Serial.println("  ✗ P&O Algorithm should start disabled");
            return false;
        }
        
        // Test parameter access
        const auto& params = poAlgorithm.getParameters();
        if (params.minVoltage > 0 && params.maxVoltage > params.minVoltage) {
            Serial.printf("  ✓ P&O Parameters valid (V: %.1f-%.1fV)\n", params.minVoltage, params.maxVoltage);
        } else {
            Serial.println("  ✗ P&O Parameters invalid");
            return false;
        }
        
        mResults.passedTests++;
        printTestResult("P&O Initialization", true);
        return true;
        
    } catch (...) {
        Serial.println("  ✗ Exception during P&O initialization test");
        printTestResult("P&O Initialization", false);
        return false;
    }
}

bool SmartAutomationTest::testPOStateMachine() {
    printTestResult("P&O State Machine", false);
    
    extern POAlgorithm poAlgorithm;
    
    // Test state transitions
    poAlgorithm.disable();
    if (!poAlgorithm.isEnabled()) {
        Serial.println("  ✓ P&O disable command works");
    } else {
        Serial.println("  ✗ P&O disable command failed");
        return false;
    }
    
    poAlgorithm.enable();
    if (poAlgorithm.isEnabled()) {
        Serial.println("  ✓ P&O enable command works");
    } else {
        Serial.println("  ✗ P&O enable command failed");
        return false;
    }
    
    // Reset to safe state
    poAlgorithm.disable();
    
    mResults.passedTests++;
    printTestResult("P&O State Machine", true);
    return true;
}

bool SmartAutomationTest::testPOOptimization() {
    printTestResult("P&O Optimization Logic", false);
    
    // This is a simplified test - full optimization testing requires hardware
    extern POAlgorithm poAlgorithm;
    
    const auto& status = poAlgorithm.getStatus();
    
    // Check if status structure is properly initialized
    if (status.iterationCount >= 0 && status.currentStepSize > 0) {
        Serial.println("  ✓ P&O Status structure properly initialized");
        mResults.passedTests++;
        printTestResult("P&O Optimization Logic", true);
        return true;
    } else {
        Serial.println("  ✗ P&O Status structure not properly initialized");
        printTestResult("P&O Optimization Logic", false);
        return false;
    }
}

bool SmartAutomationTest::testPOSafety() {
    printTestResult("P&O Safety Systems", false);
    
    extern POAlgorithm poAlgorithm;
    const auto& params = poAlgorithm.getParameters();
    
    // Check safety parameters
    bool safetyParamsValid = (params.maxCurrentLimit > 0) && 
                            (params.maxPowerLimit > 0) && 
                            (params.maxGasLevel > 0) &&
                            (params.maxGasLevel <= 100.0);
    
    if (safetyParamsValid) {
        Serial.printf("  ✓ Safety limits configured (I:%.1fA, P:%.1fW, Gas:%.1f%%)\n", 
                     params.maxCurrentLimit, params.maxPowerLimit, params.maxGasLevel);
        mResults.passedTests++;
        printTestResult("P&O Safety Systems", true);
        return true;
    } else {
        Serial.println("  ✗ Safety parameters not properly configured");
        printTestResult("P&O Safety Systems", false);
        return false;
    }
}

// Utility methods
float SmartAutomationTest::generateRandomFloat(float min, float max) {
    return min + (float)random(10000) / 10000.0 * (max - min);
}

bool SmartAutomationTest::compareFloats(float a, float b, float tolerance) {
    return abs(a - b) <= (abs(a + b) / 2.0) * (tolerance / 100.0);
}

void SmartAutomationTest::printTestResult(const String& testName, bool result) {
    if (mConfig.enableVerboseOutput) {
        Serial.printf("[%s] %s\n", result ? "PASS" : "FAIL", testName.c_str());
    }
}

void SmartAutomationTest::printProgressBar(int current, int total) {
    if (!mConfig.enableVerboseOutput) return;
    
    int barWidth = 20;
    int progress = (current * barWidth) / total;
    
    Serial.print("[");
    for (int i = 0; i < barWidth; i++) {
        if (i < progress) Serial.print("=");
        else if (i == progress) Serial.print(">");
        else Serial.print(" ");
    }
    Serial.printf("] %d/%d\n", current, total);
}

bool SmartAutomationTest::runEfficiencyTests() {
    Serial.println("--- EFFICIENCY CALCULATOR TESTS ---");

    mResults.efficiencyInitialization = testEfficiencyInitialization();
    mResults.efficiencyAccuracy = testEfficiencyAccuracy();
    mResults.efficiencyTrend = testEfficiencyTrend();
    mResults.efficiencyStability = testEfficiencyStability();

    int efficiencyTestsPassed = mResults.efficiencyInitialization + mResults.efficiencyAccuracy +
                               mResults.efficiencyTrend + mResults.efficiencyStability;

    Serial.printf("Efficiency Calculator Tests: %d/4 passed\n\n", efficiencyTestsPassed);
    return efficiencyTestsPassed == 4;
}

bool SmartAutomationTest::testEfficiencyInitialization() {
    printTestResult("Efficiency Initialization", false);

    try {
        extern EfficiencyCalculator efficiencyCalculator;

        // Test parameter access
        const auto& params = efficiencyCalculator.getParameters();
        if (params.optimalGasLevel > 0 && params.maxGasLevel > params.optimalGasLevel) {
            Serial.printf("  ✓ Efficiency parameters valid (Gas: %.1f-%.1f%%)\n",
                         params.optimalGasLevel, params.maxGasLevel);
        } else {
            Serial.println("  ✗ Efficiency parameters invalid");
            return false;
        }

        // Test metrics access
        const auto& metrics = efficiencyCalculator.getMetrics();
        if (metrics.overallEfficiency >= 0.0 && metrics.overallEfficiency <= 100.0) {
            Serial.println("  ✓ Efficiency metrics structure valid");
        } else {
            Serial.println("  ✗ Efficiency metrics structure invalid");
            return false;
        }

        mResults.passedTests++;
        printTestResult("Efficiency Initialization", true);
        return true;

    } catch (...) {
        Serial.println("  ✗ Exception during efficiency initialization test");
        printTestResult("Efficiency Initialization", false);
        return false;
    }
}

bool SmartAutomationTest::testEfficiencyAccuracy() {
    printTestResult("Efficiency Accuracy", false);

    extern EfficiencyCalculator efficiencyCalculator;

    // Test with optimal values
    float optimalGas = 25.0;
    float optimalVoltage = 12.0;
    float optimalCurrent = 15.0;
    float optimalTemp = 30.0;
    float optimalPH = 7.0;

    efficiencyCalculator.calculateEfficiency(optimalGas, optimalVoltage, optimalCurrent, optimalTemp, optimalPH);
    float optimalEfficiency = efficiencyCalculator.getOverallEfficiency();

    // Test with poor values
    float poorGas = 80.0;
    float poorVoltage = 8.0;
    float poorCurrent = 5.0;
    float poorTemp = 50.0;
    float poorPH = 4.0;

    efficiencyCalculator.calculateEfficiency(poorGas, poorVoltage, poorCurrent, poorTemp, poorPH);
    float poorEfficiency = efficiencyCalculator.getOverallEfficiency();

    // Optimal should be significantly better than poor
    if (optimalEfficiency > poorEfficiency + 20.0) {
        Serial.printf("  ✓ Efficiency calculation accurate (Optimal: %.1f%%, Poor: %.1f%%)\n",
                     optimalEfficiency, poorEfficiency);
        mResults.passedTests++;
        printTestResult("Efficiency Accuracy", true);
        return true;
    } else {
        Serial.printf("  ✗ Efficiency calculation inaccurate (Optimal: %.1f%%, Poor: %.1f%%)\n",
                     optimalEfficiency, poorEfficiency);
        printTestResult("Efficiency Accuracy", false);
        return false;
    }
}

bool SmartAutomationTest::testEfficiencyTrend() {
    printTestResult("Efficiency Trend Analysis", false);

    extern EfficiencyCalculator efficiencyCalculator;
    efficiencyCalculator.reset();

    // Simulate improving efficiency trend
    for (int i = 0; i < 10; i++) {
        float gas = 50.0 - i * 2.0; // Decreasing gas (improving efficiency)
        efficiencyCalculator.calculateEfficiency(gas, 12.0, 15.0, 30.0, 7.0);
        delay(10); // Small delay to simulate real-time
    }

    float trend = efficiencyCalculator.getEfficiencyTrend();

    // Trend should be positive (improving)
    if (trend > 5.0) {
        Serial.printf("  ✓ Trend analysis working (Trend: %.1f%% - improving)\n", trend);
        mResults.passedTests++;
        printTestResult("Efficiency Trend Analysis", true);
        return true;
    } else {
        Serial.printf("  ✗ Trend analysis not working (Trend: %.1f%%)\n", trend);
        printTestResult("Efficiency Trend Analysis", false);
        return false;
    }
}

bool SmartAutomationTest::testEfficiencyStability() {
    printTestResult("Efficiency Stability Detection", false);

    extern EfficiencyCalculator efficiencyCalculator;
    efficiencyCalculator.reset();

    // Simulate stable efficiency
    for (int i = 0; i < 15; i++) {
        float gas = 30.0 + (i % 2) * 1.0; // Small variations
        efficiencyCalculator.calculateEfficiency(gas, 12.0, 15.0, 30.0, 7.0);
        delay(10);
    }

    float stability = efficiencyCalculator.getProcessStability();

    // Stability should be high (> 80%)
    if (stability > 80.0) {
        Serial.printf("  ✓ Stability detection working (Stability: %.1f%%)\n", stability);
        mResults.passedTests++;
        printTestResult("Efficiency Stability Detection", true);
        return true;
    } else {
        Serial.printf("  ✗ Stability detection not working (Stability: %.1f%%)\n", stability);
        printTestResult("Efficiency Stability Detection", false);
        return false;
    }
}

bool SmartAutomationTest::runIntegrationTests() {
    Serial.println("--- SYSTEM INTEGRATION TESTS ---");

    mResults.systemIntegration = testSystemIntegration();
    mResults.webApiIntegration = testWebApiIntegration();
    mResults.dataConsistency = testDataConsistency();
    mResults.performanceMetrics = testPerformanceMetrics();

    int integrationTestsPassed = mResults.systemIntegration + mResults.webApiIntegration +
                                mResults.dataConsistency + mResults.performanceMetrics;

    Serial.printf("Integration Tests: %d/4 passed\n\n", integrationTestsPassed);
    return integrationTestsPassed == 4;
}

bool SmartAutomationTest::testSystemIntegration() {
    printTestResult("System Integration", false);

    // Test if all components are properly integrated
    extern FilterManager filterManager;
    extern POAlgorithm poAlgorithm;
    extern EfficiencyCalculator efficiencyCalculator;

    // Test filter manager integration
    filterManager.updateFilters(25.0 * 12.0, 7.0, 25.0, 30.0, 75.0); // power, pH, gas, temp, efficiency
    float filteredGas = filterManager.getFilteredGas();

    // Test efficiency calculation integration
    efficiencyCalculator.calculateEfficiency(filteredGas, 12.0, 15.0, 30.0, 7.0);
    float efficiency = efficiencyCalculator.getOverallEfficiency();

    // Test P&O algorithm integration (without enabling it)
    const auto& poStatus = poAlgorithm.getStatus();

    if (filteredGas > 0 && efficiency > 0 && poStatus.currentStepSize > 0) {
        Serial.printf("  ✓ All components integrated (Gas: %.1f%%, Eff: %.1f%%, P&O: Ready)\n",
                     filteredGas, efficiency);
        mResults.passedTests++;
        printTestResult("System Integration", true);
        return true;
    } else {
        Serial.println("  ✗ Component integration failed");
        printTestResult("System Integration", false);
        return false;
    }
}

bool SmartAutomationTest::testWebApiIntegration() {
    printTestResult("Web API Integration", false);

    // Test if web API functions are accessible
    try {
        extern float getFilteredGas();
        extern float getFilteredEfficiency();

        float apiGas = getFilteredGas();
        float apiEfficiency = getFilteredEfficiency();

        if (apiGas >= 0 && apiEfficiency >= 0) {
            Serial.printf("  ✓ Web API functions accessible (Gas: %.1f%%, Eff: %.1f%%)\n",
                         apiGas, apiEfficiency);
            mResults.passedTests++;
            printTestResult("Web API Integration", true);
            return true;
        } else {
            Serial.println("  ✗ Web API functions not working");
            printTestResult("Web API Integration", false);
            return false;
        }
    } catch (...) {
        Serial.println("  ✗ Exception accessing Web API functions");
        printTestResult("Web API Integration", false);
        return false;
    }
}

bool SmartAutomationTest::testDataConsistency() {
    printTestResult("Data Consistency", false);

    // Test data consistency between components
    extern FilterManager filterManager;
    extern EfficiencyCalculator efficiencyCalculator;

    // Update with same data
    float testGas = 35.0;
    float testVoltage = 12.0;
    float testCurrent = 15.0;
    float testTemp = 30.0;
    float testPH = 7.0;

    filterManager.updateFilters(testVoltage * testCurrent, testPH, testGas, testTemp, 75.0); // power, pH, gas, temp, efficiency
    efficiencyCalculator.calculateEfficiency(testGas, testVoltage, testCurrent, testTemp, testPH);

    float filteredGas = filterManager.getFilteredGas();
    float gasEfficiency = efficiencyCalculator.getGasBasedEfficiency(testGas);

    // Data should be consistent (within reasonable range)
    if (abs(filteredGas - testGas) < 10.0 && gasEfficiency > 0) {
        Serial.printf("  ✓ Data consistency maintained (Input: %.1f%%, Filtered: %.1f%%)\n",
                     testGas, filteredGas);
        mResults.passedTests++;
        printTestResult("Data Consistency", true);
        return true;
    } else {
        Serial.printf("  ✗ Data inconsistency detected (Input: %.1f%%, Filtered: %.1f%%)\n",
                     testGas, filteredGas);
        printTestResult("Data Consistency", false);
        return false;
    }
}

bool SmartAutomationTest::testPerformanceMetrics() {
    printTestResult("Performance Metrics", false);

    // Test system performance
    if (!validateSystemMemory() || !validateSystemPerformance()) {
        printTestResult("Performance Metrics", false);
        return false;
    }

    Serial.println("  ✓ System performance within acceptable limits");
    mResults.passedTests++;
    printTestResult("Performance Metrics", true);
    return true;
}

void SmartAutomationTest::printTestSummary() {
    Serial.println("\n=== SMART AUTOMATION TEST SUMMARY ===");
    Serial.printf("Total Tests: %d\n", mResults.totalTests);
    Serial.printf("Passed Tests: %d\n", mResults.passedTests);
    Serial.printf("Success Rate: %.1f%%\n", mResults.successRate);
    Serial.printf("Test Duration: %lu ms\n", mResults.testDuration);
    Serial.printf("Overall Result: %s\n", allTestsPassed() ? "PASS" : "FAIL");

    Serial.println("\nComponent Results:");
    Serial.printf("  Filters: %s\n", (mResults.filterInitialization && mResults.filterAccuracy &&
                                     mResults.filterStability && mResults.filterPerformance) ? "PASS" : "FAIL");
    Serial.printf("  P&O Algorithm: %s\n", (mResults.poInitialization && mResults.poStateMachine &&
                                           mResults.poOptimization && mResults.poSafety) ? "PASS" : "FAIL");
    Serial.printf("  Efficiency Calculator: %s\n", (mResults.efficiencyInitialization && mResults.efficiencyAccuracy &&
                                                    mResults.efficiencyTrend && mResults.efficiencyStability) ? "PASS" : "FAIL");
    Serial.printf("  System Integration: %s\n", (mResults.systemIntegration && mResults.webApiIntegration &&
                                                 mResults.dataConsistency && mResults.performanceMetrics) ? "PASS" : "FAIL");
    Serial.println("=====================================\n");
}

// Static utility methods
bool SmartAutomationTest::validateSystemMemory() {
    size_t freeHeap = ESP.getFreeHeap();
    size_t minHeap = 50000; // Minimum 50KB free heap

    if (freeHeap >= minHeap) {
        Serial.printf("  ✓ Memory usage acceptable (%d bytes free)\n", freeHeap);
        return true;
    } else {
        Serial.printf("  ✗ Low memory warning (%d bytes free, minimum %d)\n", freeHeap, minHeap);
        return false;
    }
}

bool SmartAutomationTest::validateSystemPerformance() {
    // Simple performance test - measure loop time
    unsigned long startTime = micros();

    // Simulate typical system operations
    for (int i = 0; i < 100; i++) {
        float dummy = sin(i * 0.1) * cos(i * 0.05);
        (void)dummy; // Suppress unused variable warning
    }

    unsigned long endTime = micros();
    unsigned long duration = endTime - startTime;

    if (duration < 5000) { // Should complete in < 5ms
        Serial.printf("  ✓ System performance acceptable (%lu μs)\n", duration);
        return true;
    } else {
        Serial.printf("  ✗ System performance poor (%lu μs)\n", duration);
        return false;
    }
}

String SmartAutomationTest::getSystemInfo() {
    String info = "ESP32 Smart Automation System\n";
    info += "Free Heap: " + String(ESP.getFreeHeap()) + " bytes\n";
    info += "CPU Frequency: " + String(ESP.getCpuFreqMHz()) + " MHz\n";
    info += "Flash Size: " + String(ESP.getFlashChipSize()) + " bytes\n";
    return info;
}

// Individual test category methods for quick access
bool SmartAutomationTest::testMovingAverageFilters() {
    return testFilterInitialization() && testFilterAccuracy() &&
           testFilterStability() && testFilterPerformance();
}

bool SmartAutomationTest::testPOAlgorithm() {
    return testPOInitialization() && testPOStateMachine() &&
           testPOOptimization() && testPOSafety();
}

bool SmartAutomationTest::testEfficiencyCalculator() {
    return testEfficiencyInitialization() && testEfficiencyAccuracy() &&
           testEfficiencyTrend() && testEfficiencyStability();
}

bool SmartAutomationTest::runStressTest() {
    Serial.println("--- STRESS TEST ---");

    unsigned long startTime = millis();
    bool stressTestPassed = true;

    // Stress test with rapid updates
    for (int i = 0; i < mConfig.testIterations * 10; i++) {
        float gas = generateRandomFloat(10.0, 90.0);
        float voltage = generateRandomFloat(8.0, 15.0);
        float current = generateRandomFloat(5.0, 20.0);
        float temp = generateRandomFloat(20.0, 50.0);
        float pH = generateRandomFloat(5.0, 9.0);

        // Update all systems rapidly
        extern FilterManager filterManager;
        extern EfficiencyCalculator efficiencyCalculator;

        filterManager.updateFilters(voltage * current, pH, gas, temp, 50.0);
        efficiencyCalculator.calculateEfficiency(gas, voltage, current, temp, pH);

        // Check memory usage periodically
        if (i % 100 == 0) {
            if (ESP.getFreeHeap() < 30000) {
                Serial.printf("  ✗ Memory stress test failed at iteration %d (heap: %d)\n", i, ESP.getFreeHeap());
                stressTestPassed = false;
                break;
            }
        }

        // Small delay to prevent watchdog reset
        if (i % 50 == 0) {
            delay(1);
        }
    }

    unsigned long duration = millis() - startTime;

    if (stressTestPassed) {
        Serial.printf("  ✓ Stress test passed (%lu ms, %d iterations)\n", duration, mConfig.testIterations * 10);
    } else {
        Serial.printf("  ✗ Stress test failed (%lu ms)\n", duration);
    }

    return stressTestPassed;
}

bool SmartAutomationTest::allTestsPassed() {
    return (mResults.filterInitialization && mResults.filterAccuracy &&
            mResults.filterStability && mResults.filterPerformance &&
            mResults.poInitialization && mResults.poStateMachine &&
            mResults.poOptimization && mResults.poSafety &&
            mResults.efficiencyInitialization && mResults.efficiencyAccuracy &&
            mResults.efficiencyTrend && mResults.efficiencyStability &&
            mResults.systemIntegration && mResults.webApiIntegration &&
            mResults.dataConsistency && mResults.performanceMetrics);
}

void SmartAutomationTest::reset() {
    mResults = TestResults(); // Reset to default values
    Serial.println("SmartAutomationTest: Results reset");
}

const SmartAutomationTest::TestResults& SmartAutomationTest::getResults() const {
    return mResults;
}

float SmartAutomationTest::getSuccessRate() const {
    return mResults.successRate;
}
