#include "po_algorithm.h"
#include "moving_average_filter.h"
#include "electrowinning_state.h"
#include "xy6020.h"
#include <cmath>

POAlgorithm::POAlgorithm() {
    // Initialize with default parameters
    mParams = POParameters();
    mStatus = POStatus();
    
    mFilterManager = nullptr;
    mElectrowinningState = nullptr;
    mXy6020 = nullptr;
}

POAlgorithm::~POAlgorithm() {
    // Cleanup if needed
}

void POAlgorithm::init(FilterManager* filterManager, ElectrowinningState* electrowinningState, Xy6020* xy6020) {
    mFilterManager = filterManager;
    mElectrowinningState = electrowinningState;
    mXy6020 = xy6020;
    
    // Initialize status
    mStatus.state = POState::PO_DISABLED;
    mStatus.currentStepSize = mParams.initialStepSize;
    
    Serial.println("P&O Algorithm initialized");
}

void POAlgorithm::setParameters(const POParameters& params) {
    mParams = params;
    mStatus.currentStepSize = mParams.initialStepSize;
    Serial.println("P&O Algorithm parameters updated");
}

void POAlgorithm::enable() {
    if (!mFilterManager || !mElectrowinningState || !mXy6020) {
        Serial.println("ERROR: P&O Algorithm dependencies not initialized");
        return;
    }
    
    mStatus.state = POState::WAITING_STABILITY;
    mStatus.algorithmStartTime = millis();
    mStatus.iterationCount = 0;
    mStatus.convergenceCounter = 0;
    mStatus.safetyLimitTriggered = false;
    mStatus.totalEnergyGain = 0.0;
    
    Serial.println("P&O Algorithm ENABLED - Waiting for system stability");
}

void POAlgorithm::disable() {
    mStatus.state = POState::PO_DISABLED;
    Serial.println("P&O Algorithm DISABLED");
}

void POAlgorithm::reset() {
    mStatus = POStatus();
    mStatus.currentStepSize = mParams.initialStepSize;
    Serial.println("P&O Algorithm RESET");
}

void POAlgorithm::pause() {
    if (mStatus.state != POState::PO_DISABLED) {
        mStatus.state = POState::WAITING_STABILITY;
        Serial.println("P&O Algorithm PAUSED");
    }
}

void POAlgorithm::resume() {
    if (mStatus.state != POState::PO_DISABLED) {
        Serial.println("P&O Algorithm RESUMED");
    }
}

void POAlgorithm::update() {
    if (mStatus.state == POState::PO_DISABLED) {
        return;
    }
    
    // Check safety limits first
    if (!checkSafetyLimits()) {
        mStatus.state = POState::SAFETY_HOLD;
        return;
    }
    
    // Clear safety hold if limits are OK
    if (mStatus.state == POState::SAFETY_HOLD) {
        mStatus.state = POState::WAITING_STABILITY;
        mStatus.safetyLimitTriggered = false;
        Serial.println("P&O: Safety limits cleared, resuming");
    }
    
    unsigned long currentTime = millis();
    
    switch (mStatus.state) {
        case POState::WAITING_STABILITY:
            if (checkSystemStability()) {
                mStatus.state = POState::INITIALIZING;
                Serial.println("P&O: System stable, initializing");
            } else if (currentTime - mStatus.algorithmStartTime > mParams.stabilityTimeout) {
                Serial.println("P&O: Stability timeout, disabling");
                disable();
            }
            break;
            
        case POState::INITIALIZING:
            // Record initial conditions
            mStatus.currentVoltage = mXy6020->actualVoltage();
            mStatus.currentEfficiency = calculateEfficiency();
            mStatus.currentPower = calculatePower();
            mStatus.previousVoltage = mStatus.currentVoltage;
            mStatus.previousEfficiency = mStatus.currentEfficiency;
            mStatus.previousPower = mStatus.currentPower;
            mStatus.maxEfficiencyFound = mStatus.currentEfficiency;
            mStatus.optimalVoltage = mStatus.currentVoltage;
            
            mStatus.state = POState::PERTURBING;
            mStatus.lastPerturbTime = currentTime;
            Serial.printf("P&O: Initialized - V=%.1fV, Eff=%.1f%%, P=%.1fW\n",
                         mStatus.currentVoltage, mStatus.currentEfficiency, mStatus.currentPower);
            break;
            
        case POState::PERTURBING:
            if (currentTime - mStatus.lastPerturbTime >= mParams.perturbInterval) {
                // Apply perturbation
                float newVoltage = mStatus.currentVoltage;
                
                if (mStatus.direction == PODirection::NONE) {
                    // First perturbation - try increasing
                    mStatus.direction = PODirection::INCREASE;
                    newVoltage += mStatus.currentStepSize;
                } else {
                    // Continue in current direction or reverse based on efficiency
                    if (mStatus.currentEfficiency > mStatus.previousEfficiency) {
                        // Efficiency improved, continue in same direction
                        if (mStatus.direction == PODirection::INCREASE) {
                            newVoltage += mStatus.currentStepSize;
                        } else {
                            newVoltage -= mStatus.currentStepSize;
                        }
                    } else {
                        // Efficiency decreased, reverse direction
                        mStatus.direction = (mStatus.direction == PODirection::INCREASE) ? 
                                          PODirection::DECREASE : PODirection::INCREASE;
                        if (mStatus.direction == PODirection::INCREASE) {
                            newVoltage += mStatus.currentStepSize;
                        } else {
                            newVoltage -= mStatus.currentStepSize;
                        }
                    }
                }
                
                // Apply voltage limits
                newVoltage = constrain(newVoltage, mParams.minVoltage, mParams.maxVoltage);
                applyVoltageChange(newVoltage);
                
                mStatus.state = POState::OBSERVING;
                mStatus.lastObserveTime = currentTime;
                mStatus.iterationCount++;
                
                Serial.printf("P&O: Perturbation %d - V: %.1f->%.1fV (Step: %.2fV, Dir: %s)\n",
                             mStatus.iterationCount, mStatus.currentVoltage, newVoltage,
                             mStatus.currentStepSize, 
                             (mStatus.direction == PODirection::INCREASE) ? "UP" : "DOWN");
            }
            break;
            
        case POState::OBSERVING:
            if (currentTime - mStatus.lastObserveTime >= mParams.observeInterval) {
                // Record previous values
                mStatus.previousVoltage = mStatus.currentVoltage;
                mStatus.previousEfficiency = mStatus.currentEfficiency;
                mStatus.previousPower = mStatus.currentPower;
                
                // Measure new values
                mStatus.currentVoltage = mXy6020->actualVoltage();
                mStatus.currentEfficiency = calculateEfficiency();
                mStatus.currentPower = calculatePower();
                
                // Update maximum efficiency found
                if (mStatus.currentEfficiency > mStatus.maxEfficiencyFound) {
                    mStatus.maxEfficiencyFound = mStatus.currentEfficiency;
                    mStatus.optimalVoltage = mStatus.currentVoltage;
                }
                
                // Check for convergence
                float efficiencyChange = abs(mStatus.currentEfficiency - mStatus.previousEfficiency);
                if (efficiencyChange < mParams.convergenceThreshold) {
                    mStatus.convergenceCounter++;
                } else {
                    mStatus.convergenceCounter = 0;
                }
                
                // Update step size based on progress
                updateStepSize();
                
                logProgress();
                
                // Check convergence or max iterations
                if (mStatus.convergenceCounter >= mParams.convergenceCount) {
                    mStatus.state = POState::CONVERGED;
                    Serial.printf("P&O: CONVERGED after %d iterations at V=%.1fV, Eff=%.1f%%\n",
                                 mStatus.iterationCount, mStatus.optimalVoltage, mStatus.maxEfficiencyFound);
                } else if (mStatus.iterationCount >= mParams.maxIterations) {
                    mStatus.state = POState::CONVERGED;
                    Serial.printf("P&O: Max iterations reached. Best: V=%.1fV, Eff=%.1f%%\n",
                                 mStatus.optimalVoltage, mStatus.maxEfficiencyFound);
                } else {
                    mStatus.state = POState::PERTURBING;
                    mStatus.lastPerturbTime = currentTime;
                }
            }
            break;
            
        case POState::CONVERGED:
            // Stay converged, monitor for significant changes
            if (currentTime % 10000 == 0) { // Check every 10 seconds
                float currentEff = calculateEfficiency();
                if (abs(currentEff - mStatus.maxEfficiencyFound) > mParams.convergenceThreshold * 2) {
                    Serial.println("P&O: Significant change detected, restarting optimization");
                    mStatus.state = POState::WAITING_STABILITY;
                    mStatus.convergenceCounter = 0;
                }
            }
            break;
            
        case POState::SAFETY_HOLD:
            // Wait in safety hold until limits are cleared
            break;
            
        default:
            break;
    }
}

bool POAlgorithm::checkSystemStability() {
    if (!mFilterManager) return false;
    return mFilterManager->isSystemStable();
}

bool POAlgorithm::checkSafetyLimits() {
    if (!mElectrowinningState || !mXy6020) return false;
    
    const auto& sensorData = mElectrowinningState->getSensorData();
    float current = mXy6020->actualCurrent();
    float power = calculatePower();
    
    // Check current limit
    if (current > mParams.maxCurrentLimit) {
        mStatus.safetyLimitTriggered = true;
        mStatus.lastSafetyReason = "Current limit exceeded: " + String(current) + "A";
        return false;
    }
    
    // Check power limit
    if (power > mParams.maxPowerLimit) {
        mStatus.safetyLimitTriggered = true;
        mStatus.lastSafetyReason = "Power limit exceeded: " + String(power) + "W";
        return false;
    }
    
    // Check gas level
    if (sensorData.gasLevel > mParams.maxGasLevel) {
        mStatus.safetyLimitTriggered = true;
        mStatus.lastSafetyReason = "Gas level too high: " + String(sensorData.gasLevel) + "%";
        return false;
    }
    
    // Check pH range
    if (sensorData.pH < mParams.minPHLevel || sensorData.pH > mParams.maxPHLevel) {
        mStatus.safetyLimitTriggered = true;
        mStatus.lastSafetyReason = "pH out of range: " + String(sensorData.pH);
        return false;
    }
    
    return true;
}

float POAlgorithm::calculateEfficiency() {
    if (!mElectrowinningState) return 0.0;
    
    const auto& sensorData = mElectrowinningState->getSensorData();
    
    // Base efficiency reduced by gas level impact
    float efficiency = mParams.baseEfficiency * (1.0 - (sensorData.gasLevel * mParams.gasImpactFactor));
    
    // Additional factors can be added here (temperature, pH, etc.)
    
    return constrain(efficiency, 0.0, 100.0);
}

float POAlgorithm::calculatePower() {
    if (!mXy6020) return 0.0;
    return mXy6020->actualVoltage() * mXy6020->actualCurrent();
}

void POAlgorithm::updateStepSize() {
    float efficiencyChange = abs(mStatus.currentEfficiency - mStatus.previousEfficiency);
    
    if (efficiencyChange > mParams.convergenceThreshold * 2) {
        // Large change, increase step size for faster convergence
        mStatus.currentStepSize = min(mStatus.currentStepSize * 1.2f, mParams.maxStepSize);
    } else if (efficiencyChange < mParams.convergenceThreshold * 0.5f) {
        // Small change, decrease step size for precision
        mStatus.currentStepSize = max(mStatus.currentStepSize * 0.8f, mParams.minStepSize);
    }
}

void POAlgorithm::applyVoltageChange(float newVoltage) {
    if (mXy6020) {
        mXy6020->setTargetVoltage(newVoltage);
    }
}

void POAlgorithm::logProgress() {
    Serial.printf("P&O Progress: Iter=%d, V=%.1fV, Eff=%.1f%% (Δ%.2f%%), P=%.1fW, Step=%.2fV\n",
                 mStatus.iterationCount, mStatus.currentVoltage, mStatus.currentEfficiency,
                 mStatus.currentEfficiency - mStatus.previousEfficiency,
                 mStatus.currentPower, mStatus.currentStepSize);
}

float POAlgorithm::getEfficiencyGain() const {
    if (mStatus.iterationCount == 0) return 0.0;

    // Calculate efficiency gain from start to current best
    float initialEfficiency = mParams.baseEfficiency * (1.0 - (50.0 * mParams.gasImpactFactor)); // Assume 50% initial gas
    return mStatus.maxEfficiencyFound - initialEfficiency;
}

void POAlgorithm::setVoltageRange(float minV, float maxV) {
    mParams.minVoltage = minV;
    mParams.maxVoltage = maxV;
    Serial.printf("P&O: Voltage range set to %.1f-%.1fV\n", minV, maxV);
}

void POAlgorithm::setStepSize(float initialStep, float minStep, float maxStep) {
    mParams.initialStepSize = initialStep;
    mParams.minStepSize = minStep;
    mParams.maxStepSize = maxStep;
    mStatus.currentStepSize = initialStep;
    Serial.printf("P&O: Step size range set to %.2f-%.2fV (initial: %.2fV)\n", minStep, maxStep, initialStep);
}

void POAlgorithm::setConvergenceThreshold(float threshold, int count) {
    mParams.convergenceThreshold = threshold;
    mParams.convergenceCount = count;
    Serial.printf("P&O: Convergence set to %.2f%% threshold, %d consecutive readings\n", threshold, count);
}

void POAlgorithm::setSafetyLimits(float maxCurrent, float maxPower, float maxGas) {
    mParams.maxCurrentLimit = maxCurrent;
    mParams.maxPowerLimit = maxPower;
    mParams.maxGasLevel = maxGas;
    Serial.printf("P&O: Safety limits set - Current: %.1fA, Power: %.1fW, Gas: %.1f%%\n",
                 maxCurrent, maxPower, maxGas);
}

void POAlgorithm::getStatusString(char* buffer, int bufferSize) {
    const char* stateNames[] = {"PO_DISABLED", "WAITING_STABILITY", "INITIALIZING",
                               "PERTURBING", "OBSERVING", "CONVERGED", "SAFETY_HOLD"};
    const char* directionNames[] = {"NONE", "INCREASE", "DECREASE"};

    snprintf(buffer, bufferSize,
             "State: %s | Dir: %s | Iter: %d | V: %.1fV | Eff: %.1f%% | Step: %.2fV | Conv: %d/%d",
             stateNames[(int)mStatus.state], directionNames[(int)mStatus.direction],
             mStatus.iterationCount, mStatus.currentVoltage, mStatus.currentEfficiency,
             mStatus.currentStepSize, mStatus.convergenceCounter, mParams.convergenceCount);
}

void POAlgorithm::getPerformanceReport(char* buffer, int bufferSize) {
    float runtime = (millis() - mStatus.algorithmStartTime) / 1000.0; // seconds
    float efficiencyGain = getEfficiencyGain();

    snprintf(buffer, bufferSize,
             "=== P&O PERFORMANCE REPORT ===\n"
             "Runtime: %.1fs | Iterations: %d\n"
             "Optimal Voltage: %.1fV | Max Efficiency: %.1f%%\n"
             "Efficiency Gain: %.1f%% | Current State: %s\n"
             "Safety Triggered: %s | Last Reason: %s",
             runtime, mStatus.iterationCount,
             mStatus.optimalVoltage, mStatus.maxEfficiencyFound,
             efficiencyGain,
             mStatus.state == POState::CONVERGED ? "CONVERGED" : "OPTIMIZING",
             mStatus.safetyLimitTriggered ? "YES" : "NO",
             mStatus.lastSafetyReason.c_str());
}

void POAlgorithm::printDebugInfo() {
    Serial.println("=== P&O ALGORITHM DEBUG INFO ===");

    const char* stateNames[] = {"PO_DISABLED", "WAITING_STABILITY", "INITIALIZING",
                               "PERTURBING", "OBSERVING", "CONVERGED", "SAFETY_HOLD"};
    const char* directionNames[] = {"NONE", "INCREASE", "DECREASE"};

    Serial.printf("State: %s\n", stateNames[(int)mStatus.state]);
    Serial.printf("Direction: %s\n", directionNames[(int)mStatus.direction]);
    Serial.printf("Iteration: %d/%d\n", mStatus.iterationCount, mParams.maxIterations);
    Serial.printf("Convergence: %d/%d\n", mStatus.convergenceCounter, mParams.convergenceCount);

    Serial.println("\n--- Current Values ---");
    Serial.printf("Voltage: %.2fV (Range: %.1f-%.1fV)\n", mStatus.currentVoltage, mParams.minVoltage, mParams.maxVoltage);
    Serial.printf("Efficiency: %.2f%% (Previous: %.2f%%)\n", mStatus.currentEfficiency, mStatus.previousEfficiency);
    Serial.printf("Power: %.1fW\n", mStatus.currentPower);
    Serial.printf("Step Size: %.2fV (Range: %.2f-%.2fV)\n", mStatus.currentStepSize, mParams.minStepSize, mParams.maxStepSize);

    Serial.println("\n--- Best Results ---");
    Serial.printf("Optimal Voltage: %.2fV\n", mStatus.optimalVoltage);
    Serial.printf("Max Efficiency: %.2f%%\n", mStatus.maxEfficiencyFound);
    Serial.printf("Efficiency Gain: %.2f%%\n", getEfficiencyGain());

    Serial.println("\n--- Safety Status ---");
    Serial.printf("Safety Triggered: %s\n", mStatus.safetyLimitTriggered ? "YES" : "NO");
    if (mStatus.safetyLimitTriggered) {
        Serial.printf("Last Safety Reason: %s\n", mStatus.lastSafetyReason.c_str());
    }

    Serial.println("\n--- Timing ---");
    unsigned long runtime = millis() - mStatus.algorithmStartTime;
    Serial.printf("Runtime: %lu ms (%.1fs)\n", runtime, runtime / 1000.0);
    Serial.printf("Last Perturbation: %lu ms ago\n", millis() - mStatus.lastPerturbTime);
    Serial.printf("Last Observation: %lu ms ago\n", millis() - mStatus.lastObserveTime);

    Serial.println("================================");
}
