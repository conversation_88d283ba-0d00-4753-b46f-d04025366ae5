#include "webserver_esp32.h"
#include "data_logger.h"
#include "electrowinning_state.h"
#include "po_algorithm.h"
#include "efficiency_calculator.h"
#include "smart_automation_test.h"

#include <ArduinoJson.h>

// External data logger instance
extern DataLogger dataLogger;

// External function declarations (defined in main.cpp)
extern void enablePOAlgorithm();
extern void disablePOAlgorithm();
extern void resetPOAlgorithm();
extern void pausePOAlgorithm();
extern void resumePOAlgorithm();
extern void printPOStatus();

// Efficiency Calculator function declarations
extern void printEfficiencyStatus();
extern void resetEfficiencyCalculator();
extern String getEfficiencyReport();
extern float getGasBasedEfficiency();
extern float getPowerEfficiency();
extern float getThermalEfficiency();
extern float getPHEfficiency();
extern float getEfficiencyTrend();
extern float getProcessStability();

// Smart Automation Testing function declarations
extern void runSmartAutomationTests();
extern void runQuickSystemCheck();
extern void printSmartAutomationStatus();
extern SmartAutomationTest smartTest;



XyWebServer::XyWebServer(Xy6020 *xy_obj, Settings &config)
    : mXy(xy_obj), mConfig(config) {
  server = new WebServer(80);
}

void XyWebServer::init(bool admin_mode) {
  Serial.print("MAC: ");
  Serial.println(WiFi.macAddress());
  Serial.print("Got IP: ");
  Serial.println(WiFi.localIP());

  server->on("/style.css", std::bind(&XyWebServer::handleStyleCss, this));
  if (admin_mode) {
    server->on("/", std::bind(&XyWebServer::handleSettings, this));
    server->on("/index.html", std::bind(&XyWebServer::handleRoot, this));
  } else {
    server->on("/", std::bind(&XyWebServer::handleRoot, this));
    server->on("/index.html", std::bind(&XyWebServer::handleRoot, this));
  }
  server->on("/settings.html", std::bind(&XyWebServer::handleSettings, this));
  server->on("/charts.html", std::bind(&XyWebServer::handleCharts, this));
  server->on("/logic.js", std::bind(&XyWebServer::handleLogicJs, this));
  server->on("/segment-display.js",
                std::bind(&XyWebServer::handleSegmentDisplayJs, this));
  server->on("/charts.js", std::bind(&XyWebServer::handleChartsJs, this));
  server->onNotFound(std::bind(&XyWebServer::handleNotFound, this));
  server->on("/control", HTTP_GET, std::bind(&XyWebServer::handleControlGet, this));
  server->on("/control", HTTP_POST, std::bind(&XyWebServer::handleControlPost, this));
  server->on("/control", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  server->on("/config", HTTP_GET, std::bind(&XyWebServer::handleSettingsGet, this));
  server->on("/config", HTTP_POST, std::bind(&XyWebServer::handleSettingsPost, this));
  server->on("/config", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  server->on("/wifi-status", HTTP_GET, std::bind(&XyWebServer::handleWifiStatus, this));
  server->on("/wifi-status", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // Electrowinning endpoints
  server->on("/electrowinning", HTTP_GET, std::bind(&XyWebServer::handleElectrowinningGet, this));
  server->on("/electrowinning", HTTP_POST, std::bind(&XyWebServer::handleElectrowinningPost, this));
  server->on("/electrowinning", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // Data logging endpoints - integrated with existing endpoints
  server->on("/datalogger", HTTP_GET, std::bind(&XyWebServer::handleDataLoggerGet, this));
  server->on("/datalogger", HTTP_POST, std::bind(&XyWebServer::handleDataLoggerPost, this));
  server->on("/datalogger", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // Smart automation endpoints
  server->on("/smart-automation", HTTP_GET, std::bind(&XyWebServer::handleSmartAutomationGet, this));
  server->on("/smart-automation", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // P&O Algorithm endpoints
  server->on("/po-algorithm", HTTP_GET, std::bind(&XyWebServer::handlePOAlgorithmGet, this));
  server->on("/po-algorithm", HTTP_POST, std::bind(&XyWebServer::handlePOAlgorithmPost, this));
  server->on("/po-algorithm", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // Efficiency Calculator endpoints
  server->on("/efficiency", HTTP_GET, std::bind(&XyWebServer::handleEfficiencyGet, this));
  server->on("/efficiency", HTTP_POST, std::bind(&XyWebServer::handleEfficiencyPost, this));
  server->on("/efficiency", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // Smart Automation Testing endpoints
  server->on("/test", HTTP_GET, std::bind(&XyWebServer::handleTestGet, this));
  server->on("/test", HTTP_POST, std::bind(&XyWebServer::handleTestPost, this));
  server->on("/test", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // Debug info
  Serial.println("Routes registered:");
  Serial.println(" - GET /");
  Serial.println(" - GET /index.html");
  Serial.println(" - GET /settings.html");
  Serial.println(" - GET /charts.html");
  Serial.println(" - GET /style.css");
  Serial.println(" - GET /logic.js");
  Serial.println(" - GET /segment-display.js");
  Serial.println(" - GET /charts.js");
  Serial.println(" - GET /control");
  Serial.println(" - POST /control");
  Serial.println(" - OPTIONS /control");
  Serial.println(" - GET /config");
  Serial.println(" - POST /config");
  Serial.println(" - OPTIONS /config");
  Serial.println(" - GET /wifi-status");
  Serial.println(" - OPTIONS /wifi-status");
  Serial.println(" - GET /electrowinning");
  Serial.println(" - POST /electrowinning");
  Serial.println(" - OPTIONS /electrowinning");
  Serial.println(" - GET /datalogger");
  Serial.println(" - POST /datalogger");
  Serial.println(" - GET /smart-automation");
  Serial.println(" - GET /po-algorithm");
  Serial.println(" - POST /po-algorithm");
  Serial.println(" - GET /efficiency");
  Serial.println(" - POST /efficiency");
  Serial.println(" - OPTIONS /datalogger");
  server->begin();
  Serial.println("HTTP server started");
}

void XyWebServer::task() { server->handleClient(); }

void XyWebServer::handleNotFound() {
  Serial.println("handleNotFound called");
  Serial.print("URI: ");
  Serial.println(server->uri());
  Serial.print("Method: ");
  Serial.println(server->method() == HTTP_GET ? "GET" : (server->method() == HTTP_POST ? "POST" : "OTHER"));
  Serial.print("Arguments: ");
  Serial.println(server->args());

  for (int i = 0; i < server->args(); i++) {
    Serial.print(server->argName(i));
    Serial.print(": ");
    Serial.println(server->arg(i));
  }

  server->send(404, "text/plain", "Not found");
}

void XyWebServer::handleRoot() { server->send(200, "text/html", html__index); }

void XyWebServer::handleSettings() {
  server->send(200, "text/html", html__settings);
}

void XyWebServer::handleStyleCss() {
  server->send(200, "text/css", css__style);
}

void XyWebServer::handleLogicJs() {
  server->send(200, "text/javascript", js__logic);
}

void XyWebServer::handleSegmentDisplayJs() {
  server->send(200, "text/javascript", js__segmentdisplay);
}

void XyWebServer::handleCharts() {
  server->send(200, "text/html", html__charts);
}

void XyWebServer::handleChartsJs() {
  server->send(200, "text/javascript", js__charts);
}

void XyWebServer::handleCORS() {
  Serial.println("handleCORS called");
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type, X-Requested-With");
  server->sendHeader("Access-Control-Max-Age", "86400");
  server->send(204);
}

void XyWebServer::handleWifiStatus() {
  Serial.println("handleWifiStatus called");

  String status = "Not Connected";
  String ip = "-";
  String ssid = "-";

  if (WiFi.status() == WL_CONNECTED) {
    status = "Connected";
    ip = WiFi.localIP().toString();
    ssid = WiFi.SSID();
  }

  String response = "{";
  response += "\"status\":\"" + status + "\",";
  response += "\"ip\":\"" + ip + "\",";
  response += "\"ssid\":\"" + ssid + "\"";
  response += "}";

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->send(200, "application/json", response);
}

void XyWebServer::handleSettingsGet() {
  String str;
  auto &cfg = mConfig.data();

  String mqtt_server;
  if (strlen(cfg.mqtt_broker_host) > 0) {
    mqtt_server = String(cfg.mqtt_broker_host);
  } else {
    mqtt_server = IPAddress(cfg.mqtt_broker).toString();
  }

  str += "{\"ssid\" : \"" + String(cfg.wifi_ssid) + "\"," +
         "\"use-static-ip\": " + cfg.use_static_ip + ",\"static-ip\":\"" +
         IPAddress(cfg.static_ip).toString() + "\",\"subnet\": \"" +
         IPAddress(cfg.subnet).toString() + "\",\"gateway\": \"" +
         IPAddress(cfg.gateway).toString() + "\",\"mqtt-server\": \"" +
         mqtt_server + "\",\"mqtt-port\": \"" +
         cfg.mqtt_port + "\",\"mqtt-user\": \"" + cfg.mqtt_user +
         "\", \"mqtt-id\": \"" + cfg.mqtt_id +
         "\", \"mqtt-pub-topic\": \"" + cfg.mqtt_pub_topic +
         "\", \"mqtt-sub-topic\": \"" + cfg.mqtt_sub_topic +
         "\",\"zero-feed-in\": " + cfg.zero_feed_in + ",\"smi-topic\": \"" +
         cfg.smi_topic + "\",\"sm-name\": \"" + cfg.sm_name +
         "\", \"enable-input-limits\":" + cfg.enable_input_limits +
         ", \"switch-off-voltage\": \"" + cfg.switch_off_voltage +
         "\", \"switch-on-voltage\": \"" + cfg.switch_on_voltage + "\"}";

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->send(200, "application/json", str);
}

void XyWebServer::handleControlGet() {
  char str[256];
  bool connected = mXy->isConnected();

  if (connected) {
    // Device is connected, send actual values
    sprintf(str,
            "{"
            "\"voltage\": %0.2f,"
            "\"current\": %0.2f,"
            "\"power\": %0.1f,"
            "\"output\": %d,"
            "\"tvoltage\": %0.2f,"
            "\"tcurrent\": %0.2f,"
            "\"tpower\": %0.1f,"
            "\"ivoltage\": %0.1f,"
            "\"pH\": %0.2f,"
            "\"temperature\": %0.1f,"
            "\"gasLevel\": %0.1f,"
            "\"connected\": 1"
            "}",
            mXy->actualVoltage(), mXy->actualCurrent(), mXy->actualPower(),
            mXy->outputEnabled(), mXy->targetVoltage(), mXy->maxCurrent(),
            mXy->maxPower(), mXy->inputVoltage(),
            electrowinningState.getSensorData().pH,
            electrowinningState.getSensorData().temperature,
            electrowinningState.getSensorData().gasLevel);
  } else {
    // Device is not connected, send zeros with connected=0
    sprintf(str,
            "{"
            "\"voltage\": 0.00,"
            "\"current\": 0.00,"
            "\"power\": 0.0,"
            "\"output\": 0,"
            "\"tvoltage\": 0.00,"
            "\"tcurrent\": 0.00,"
            "\"tpower\": 0.0,"
            "\"ivoltage\": 0.0,"
            "\"pH\": 7.00,"
            "\"temperature\": 25.0,"
            "\"gasLevel\": 0.0,"
            "\"connected\": 0"
            "}");
  }

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->send(200, "application/json", str);
}

void XyWebServer::handleControlPost() {
  Serial.println("Control set request received.");
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Content-Type", "application/json");

  // Check if XY6020L is connected
  if (!mXy->isConnected()) {
    Serial.println("Error: Cannot process control request - XY6020L not connected");
    server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"XY6020L not connected\"}");
    return;
  }

  for (int a = 0; a < server->args(); ++a) {
    auto param = server->argName(a);
    auto val_str = server->arg(a);
    float value = val_str.toFloat();
    Serial.printf_P("%s=%0.2f\n", param.c_str(), value);
    bool ret = false;

    if (param == "voltage") {
      Serial.printf_P("Setting target voltage to %.2fV\n", value);
      ret = mXy->setTargetVoltage(value);
      if (!ret) {
        Serial.println("Failed to set target voltage");
        server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid voltage value\"}");
        return;
      }
    } else if (param == "current") {
      Serial.printf_P("Setting max current to %.2fA\n", value);
      ret = mXy->setMaxCurrent(value);
      if (!ret) {
        Serial.println("Failed to set max current");
        server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid current value\"}");
        return;
      }
    } else if (param == "max-power") {
      Serial.printf_P("Setting max power to %.1fW\n", value);
      ret = mXy->setMaxPower(value);
      mConfig.store();
      if (!ret) {
        Serial.println("Failed to set max power");
        server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid power value\"}");
        return;
      }
    } else if (param == "output") {
      if (val_str.length()) {
        bool outputState = (value > 0.01);
        Serial.printf_P("Setting output to %s\n", outputState ? "ON" : "OFF");
        mXy->setOutputEnabled(outputState);
        ret = true;
      }
    } else if (param == "reset") {
      Serial.println("Restarting ESP32...");
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Restarting\"}");
      delay(100);
      ESP.restart();
      return;
    } else {
      Serial.printf_P("Unknown parameter: %s\n", param.c_str());
      server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown parameter\"}");
      return;
    }
  }

  // Send success response
  server->send(200, "application/json", "{\"status\":\"OK\"}");

  // Force a delay to allow the response to be sent before any potential issues
  delay(50);
}

void XyWebServer::handleSettingsPost() {
  Serial.println("handleSettingsPost called");
  Serial.print("Number of args: ");
  Serial.println(server->args());

  for (int i = 0; i < server->args(); i++) {
    Serial.print(server->argName(i));
    Serial.print(": ");
    Serial.println(server->arg(i));
  }

  // Get the raw POST data
  String postBody = server->arg("plain");
  Serial.print("Raw POST data: ");
  Serial.println(postBody);

  JsonDocument doc;
  DeserializationError err = deserializeJson(doc, postBody);

  if (err) {
    Serial.print("deserializeJson() failed: ");
    Serial.println(err.c_str());
    server->sendHeader("Access-Control-Allow-Origin", "*");
    server->send(400, "text/plain", String("JSON parsing failed: ") + err.c_str());
    return;
  }

  auto &cfg = mConfig.data();
  if (err == DeserializationError::Ok) {
    if (!doc["ssid"].isUnbound()) {
      String str = doc["ssid"];
      strncpy(cfg.wifi_ssid, str.c_str(), 128);
    }
    if (!doc["wifi-password"].isUnbound()) {
      String str = doc["wifi-password"];
      if (str.length()) {
        strncpy(cfg.wifi_password, str.c_str(), 128);
      }
    }

    cfg.use_static_ip = doc["use-static-ip"] | cfg.use_static_ip;

    if (!doc["static-ip"].isUnbound()) {
      String str = doc["static-ip"];
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.static_ip = addr;
      }
    }

    if (!doc["subnet"].isUnbound()) {
      String str = doc["subnet"];
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.subnet = addr;
      }
    }

    if (!doc["gateway"].isUnbound()) {
      String str = doc["gateway"];
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.gateway = addr;
      }
    }

    if (!doc["mqtt-server"].isUnbound()) {
      String str = doc["mqtt-server"];
      // Coba parse sebagai IP address
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.mqtt_broker = addr;
        // Jika berhasil di-parse sebagai IP, kosongkan host string
        cfg.mqtt_broker_host[0] = '\0';
      } else {
        // Jika bukan IP address, simpan sebagai host string
        strncpy(cfg.mqtt_broker_host, str.c_str(), 128);
        // Reset IP address
        cfg.mqtt_broker = 0;
      }
    }

    cfg.mqtt_port = doc["mqtt-port"] | cfg.mqtt_port;

    if (!doc["mqtt-user"].isUnbound()) {
      String str = doc["mqtt-user"];
      strncpy(cfg.mqtt_user, str.c_str(), 128);
    }

    if (!doc["mqtt-pass"].isUnbound()) {
      String str = doc["mqtt-pass"];
      if (str.length()) {
        strncpy(cfg.mqtt_password, str.c_str(), 128);
      }
    }

    if (!doc["mqtt-id"].isUnbound()) {
      String str = doc["mqtt-id"];
      strncpy(cfg.mqtt_id, str.c_str(), 128);
    }

    if (!doc["mqtt-pub-topic"].isUnbound()) {
      String str = doc["mqtt-pub-topic"];
      strncpy(cfg.mqtt_pub_topic, str.c_str(), 128);
    }

    if (!doc["mqtt-sub-topic"].isUnbound()) {
      String str = doc["mqtt-sub-topic"];
      strncpy(cfg.mqtt_sub_topic, str.c_str(), 128);
    }

    cfg.zero_feed_in = doc["zero-feed-in"] | cfg.zero_feed_in;

    if (!doc["smi-topic"].isUnbound()) {
      String str = doc["smi-topic"];
      strncpy(cfg.smi_topic, str.c_str(), 128);
    }
    if (!doc["sm-name"].isUnbound()) {
      String str = doc["sm-name"];
      strncpy(cfg.sm_name, str.c_str(), 128);
    }

    cfg.enable_input_limits =
        doc["enable-input-limits"] | cfg.enable_input_limits;

    if (!doc["switch-off-voltage"].isUnbound()) {
      String str = doc["switch-off-voltage"];
      cfg.switch_off_voltage = str.toFloat();
    }
    if (!doc["switch-on-voltage"].isUnbound()) {
      String str = doc["switch-on-voltage"];
      cfg.switch_on_voltage = str.toFloat();
    }

    mConfig.store();
  }

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Content-Type", "application/json");
  server->send(200, "application/json", "{\"status\":\"OK\"}");

  // Check if WiFi settings were changed
  bool wifiSettingsChanged = false;
  if (!doc["ssid"].isUnbound() || !doc["wifi-password"].isUnbound()) {
    wifiSettingsChanged = true;
  }

  // If WiFi settings were changed, restart ESP32 after a short delay
  if (wifiSettingsChanged) {
    Serial.println("WiFi settings changed. Restarting in 2 seconds...");
    delay(500); // Give time for the HTTP response to be sent
    ESP.restart();
  }
}

void XyWebServer::handleElectrowinningGet() {
  Serial.println("Electrowinning status request received.");
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Content-Type", "application/json");

  // Get electrowinning status as JSON
  String json = electrowinningState.toJson();

  server->send(200, "application/json", json);
}

void XyWebServer::handleElectrowinningPost() {
  Serial.println("Electrowinning control request received.");
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Content-Type", "application/json");

  // Check if we have a JSON body
  if (server->hasArg("plain")) {
    String body = server->arg("plain");
    Serial.println("Received JSON: " + body);

    bool success = electrowinningState.fromJson(body);

    if (success) {
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Command processed successfully\"}");
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid command or JSON format\"}");
    }
  } else {
    // Handle URL parameters for backward compatibility
    bool hasCommand = false;

    for (int a = 0; a < server->args(); ++a) {
      auto param = server->argName(a);
      auto val_str = server->arg(a);

      Serial.printf("Electrowinning param: %s=%s\n", param.c_str(), val_str.c_str());

      if (param == "start_process") {
        bool manual = val_str == "manual";
        electrowinningState.startProcess(manual, -1);
        hasCommand = true;
      } else if (param == "stop_process" && val_str == "true") {
        electrowinningState.stopProcess();
        hasCommand = true;
      } else if (param == "pump_speed") {
        int speed = val_str.toInt();
        electrowinningState.setPumpSpeed(speed);
        hasCommand = true;
      } else if (param == "fan_speed") {
        int speed = val_str.toInt();
        electrowinningState.setFanSpeed(speed);
        hasCommand = true;
      } else if (param == "target_voltage") {
        float voltage = val_str.toFloat();
        electrowinningState.setTargetVoltage(voltage);
        hasCommand = true;
      } else if (param == "target_current") {
        float current = val_str.toFloat();
        electrowinningState.setTargetCurrent(current);
        hasCommand = true;
      } else if (param == "enable_output") {
        bool enable = val_str == "true" || val_str == "1";
        electrowinningState.enableOutput(enable);
        hasCommand = true;
      } else if (param == "enable_pump") {
        bool enable = val_str == "true" || val_str == "1";
        electrowinningState.enablePump(enable);
        hasCommand = true;
      } else if (param == "enable_fan") {
        bool enable = val_str == "true" || val_str == "1";
        electrowinningState.enableFan(enable);
        hasCommand = true;
      }
    }

    if (hasCommand) {
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Commands processed successfully\"}");
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"No valid commands found\"}");
    }
  }
}

// Data Logger Endpoints Implementation
void XyWebServer::handleDataLoggerGet() {
  // Handle different actions based on query parameters
  String action = server->arg("action");

  server->sendHeader("Access-Control-Allow-Origin", "*");

  if (action == "config") {
    // Return current data logger configuration
    String configJson = dataLogger.getConfigAsJSON();
    server->send(200, "application/json", configJson);
  }
  else if (action == "logs") {
    // Return logged data as JSON
    int limit = 50; // Default limit
    int offset = 0; // Default offset

    if (server->hasArg("limit")) {
      limit = server->arg("limit").toInt();
      limit = constrain(limit, 1, 1000);
    }

    if (server->hasArg("offset")) {
      offset = server->arg("offset").toInt();
      offset = max(0, offset);
    }

    String logsJson = dataLogger.getLogsAsJSON(limit, offset);
    server->send(200, "application/json", logsJson);
  }
  else if (action == "download") {
    // Download logs directly to browser
    String format = server->hasArg("format") ? server->arg("format") : "csv";

    if (format == "json") {
      // Send JSON data directly to browser
      String jsonData = dataLogger.getLogsAsJSON(1000, 0); // Get all data
      String filename = "logs_" + String(millis()) + ".json";

      server->sendHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
      server->sendHeader("Content-Type", "application/json");
      server->send(200, "application/json", jsonData);
    } else {
      // Send CSV data directly to browser
      String csvData = dataLogger.getLogsAsCSV(); // Get all data as CSV
      String filename = "logs_" + String(millis()) + ".csv";

      server->sendHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
      server->sendHeader("Content-Type", "text/csv");
      server->send(200, "text/csv", csvData);
    }
  }
  else {
    // Default: return status
    JsonDocument doc;
    doc["enabled"] = dataLogger.isEnabled();
    doc["entries"] = dataLogger.getEntryCount();
    doc["total_logged"] = dataLogger.getTotalLoggedEntries();
    doc["last_log"] = dataLogger.getLastLogTime();

    String result;
    serializeJson(doc, result);
    server->send(200, "application/json", result);
  }
}

void XyWebServer::handleDataLoggerPost() {
  // Handle configuration updates and commands
  server->sendHeader("Access-Control-Allow-Origin", "*");

  if (server->hasArg("plain")) {
    String body = server->arg("plain");
    bool success = dataLogger.setConfigFromJSON(body);

    if (success) {
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Configuration updated\"}");
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid configuration\"}");
    }
  } else {
    // Handle URL encoded commands
    String action = server->arg("action");

    if (action == "clear") {
      dataLogger.clearBuffer();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Buffer cleared\"}");
    } else if (action == "save") {
      bool success = dataLogger.saveToFile();
      if (success) {
        server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Data saved\"}");
      } else {
        server->send(500, "application/json", "{\"status\":\"FAIL\",\"message\":\"Save failed\"}");
      }
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown action\"}");
    }
  }
}

void XyWebServer::handleSmartAutomationGet() {
  // Add CORS headers
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type");

  // Get electrowinning state data
  extern ElectrowinningState electrowinningState;
  const auto& sensorData = electrowinningState.getSensorData();

  // Get additional smart automation data
  extern float getFilteredPower();
  extern float getFilteredEfficiency();
  extern bool isSystemStableForOptimization();
  extern float readRawPHSensor();
  extern float readRawMQ8HydrogenLevel();
  extern float readRawTemperatureSensor();

  // Create JSON response
  JsonDocument doc;

  // Filtered sensor data
  JsonObject filtered = doc["filtered"].to<JsonObject>();
  filtered["pH"] = sensorData.pH;
  filtered["temperature"] = sensorData.temperature;
  filtered["gasLevel"] = sensorData.gasLevel;
  filtered["power"] = sensorData.filteredPower;
  filtered["efficiency"] = sensorData.filteredEfficiency;

  // Raw sensor data for comparison
  JsonObject raw = doc["raw"].to<JsonObject>();
  raw["pH"] = readRawPHSensor();
  raw["temperature"] = readRawTemperatureSensor();
  raw["gasLevel"] = readRawMQ8HydrogenLevel();

  // System status
  JsonObject status = doc["status"].to<JsonObject>();
  status["systemStable"] = sensorData.systemStable;
  status["noiseReduction"] = sensorData.noiseReduction;
  status["lastUpdate"] = sensorData.lastUpdate;
  status["isValid"] = sensorData.isValid;

  // Filter performance metrics
  JsonObject metrics = doc["metrics"].to<JsonObject>();
  metrics["pHDifference"] = abs(raw["pH"].as<float>() - filtered["pH"].as<float>());
  metrics["tempDifference"] = abs(raw["temperature"].as<float>() - filtered["temperature"].as<float>());
  metrics["gasDifference"] = abs(raw["gasLevel"].as<float>() - filtered["gasLevel"].as<float>());

  // Timestamp
  doc["timestamp"] = millis();

  String response;
  serializeJson(doc, response);
  server->send(200, "application/json", response);
}

void XyWebServer::handlePOAlgorithmGet() {
  // Add CORS headers
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type");

  // Get P&O Algorithm instance
  extern POAlgorithm poAlgorithm;
  const auto& status = poAlgorithm.getStatus();
  const auto& params = poAlgorithm.getParameters();

  // Create JSON response
  JsonDocument doc;

  // Algorithm status
  JsonObject statusObj = doc["status"].to<JsonObject>();
  const char* stateNames[] = {"PO_DISABLED", "WAITING_STABILITY", "INITIALIZING",
                             "PERTURBING", "OBSERVING", "CONVERGED", "SAFETY_HOLD"};
  const char* directionNames[] = {"NONE", "INCREASE", "DECREASE"};

  statusObj["state"] = stateNames[(int)status.state];
  statusObj["direction"] = directionNames[(int)status.direction];
  statusObj["enabled"] = poAlgorithm.isEnabled();
  statusObj["converged"] = poAlgorithm.isConverged();
  statusObj["safetyTriggered"] = poAlgorithm.isSafetyTriggered();
  statusObj["lastSafetyReason"] = status.lastSafetyReason;

  // Current values
  JsonObject current = doc["current"].to<JsonObject>();
  current["voltage"] = status.currentVoltage;
  current["efficiency"] = status.currentEfficiency;
  current["power"] = status.currentPower;
  current["stepSize"] = status.currentStepSize;

  // Progress metrics
  JsonObject progress = doc["progress"].to<JsonObject>();
  progress["iterationCount"] = status.iterationCount;
  progress["maxIterations"] = params.maxIterations;
  progress["convergenceCounter"] = status.convergenceCounter;
  progress["convergenceCount"] = params.convergenceCount;

  // Best results
  JsonObject best = doc["best"].to<JsonObject>();
  best["optimalVoltage"] = poAlgorithm.getOptimalVoltage();
  best["maxEfficiency"] = poAlgorithm.getMaxEfficiencyFound();
  best["efficiencyGain"] = poAlgorithm.getEfficiencyGain();

  // Parameters
  JsonObject paramsObj = doc["parameters"].to<JsonObject>();
  paramsObj["minVoltage"] = params.minVoltage;
  paramsObj["maxVoltage"] = params.maxVoltage;
  paramsObj["convergenceThreshold"] = params.convergenceThreshold;
  paramsObj["maxCurrentLimit"] = params.maxCurrentLimit;
  paramsObj["maxPowerLimit"] = params.maxPowerLimit;
  paramsObj["maxGasLevel"] = params.maxGasLevel;

  // Timing
  JsonObject timing = doc["timing"].to<JsonObject>();
  unsigned long runtime = millis() - status.algorithmStartTime;
  timing["runtime"] = runtime;
  timing["runtimeSeconds"] = runtime / 1000.0;
  timing["lastPerturbTime"] = status.lastPerturbTime;
  timing["lastObserveTime"] = status.lastObserveTime;

  // Timestamp
  doc["timestamp"] = millis();

  String response;
  serializeJson(doc, response);
  server->send(200, "application/json", response);
}

void XyWebServer::handlePOAlgorithmPost() {
  // Add CORS headers
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type");

  // Get P&O Algorithm instance
  extern POAlgorithm poAlgorithm;

  if (server->hasArg("plain")) {
    // Handle JSON commands
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, server->arg("plain"));

    if (error) {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid JSON\"}");
      return;
    }

    String action = doc["action"];

    if (action == "enable") {
      poAlgorithm.enable();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm enabled\"}");
    } else if (action == "disable") {
      poAlgorithm.disable();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm disabled\"}");
    } else if (action == "reset") {
      poAlgorithm.reset();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm reset\"}");
    } else if (action == "pause") {
      poAlgorithm.pause();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm paused\"}");
    } else if (action == "resume") {
      poAlgorithm.resume();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm resumed\"}");
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown action\"}");
    }
  } else {
    // Handle URL encoded commands
    String action = server->arg("action");

    if (action == "enable") {
      poAlgorithm.enable();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm enabled\"}");
    } else if (action == "disable") {
      poAlgorithm.disable();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm disabled\"}");
    } else if (action == "reset") {
      poAlgorithm.reset();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"P&O Algorithm reset\"}");
    } else if (action == "status") {
      // Return detailed status
      poAlgorithm.printDebugInfo();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Status printed to serial\"}");
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown action\"}");
    }
  }
}

void XyWebServer::handleEfficiencyGet() {
  // Add CORS headers
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type");

  // Get Efficiency Calculator instance
  extern EfficiencyCalculator efficiencyCalculator;
  const auto& metrics = efficiencyCalculator.getMetrics();
  const auto& params = efficiencyCalculator.getParameters();

  // Create JSON response
  JsonDocument doc;

  // Overall efficiency metrics
  JsonObject overall = doc["overall"].to<JsonObject>();
  overall["efficiency"] = metrics.overallEfficiency;
  overall["grade"] = EfficiencyCalculator::getEfficiencyGrade(metrics.overallEfficiency);
  overall["description"] = EfficiencyCalculator::getEfficiencyDescription(metrics.overallEfficiency);
  overall["trend"] = metrics.efficiencyTrend;
  overall["stability"] = metrics.processStability;

  // Component efficiencies
  JsonObject components = doc["components"].to<JsonObject>();
  components["gas"] = metrics.gasBasedEfficiency;
  components["power"] = metrics.powerEfficiency;
  components["thermal"] = metrics.thermalEfficiency;
  components["pH"] = metrics.pHEfficiency;

  // Component weights
  JsonObject weights = doc["weights"].to<JsonObject>();
  weights["gas"] = params.gasWeight * 100.0;
  weights["power"] = params.powerWeight * 100.0;
  weights["thermal"] = params.thermalWeight * 100.0;
  weights["pH"] = params.pHWeight * 100.0;

  // Performance indicators
  JsonObject performance = doc["performance"].to<JsonObject>();
  performance["gasImpactFactor"] = metrics.gasImpactFactor;
  performance["powerUtilization"] = metrics.powerUtilization;
  performance["processStability"] = metrics.processStability;

  // Optimal ranges
  JsonObject optimal = doc["optimal"].to<JsonObject>();
  optimal["gasLevel"] = params.optimalGasLevel;
  optimal["powerMin"] = params.optimalPowerRange[0];
  optimal["powerMax"] = params.optimalPowerRange[1];
  optimal["tempMin"] = params.optimalTempRange[0];
  optimal["tempMax"] = params.optimalTempRange[1];
  optimal["pHMin"] = params.optimalPHRange[0];
  optimal["pHMax"] = params.optimalPHRange[1];

  // Status
  JsonObject status = doc["status"].to<JsonObject>();
  status["valid"] = metrics.isValid;
  status["lastUpdate"] = metrics.lastUpdate;
  status["uptime"] = millis();

  // Timestamp
  doc["timestamp"] = millis();

  String response;
  serializeJson(doc, response);
  server->send(200, "application/json", response);
}

void XyWebServer::handleEfficiencyPost() {
  // Add CORS headers
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type");

  // Get Efficiency Calculator instance
  extern EfficiencyCalculator efficiencyCalculator;

  if (server->hasArg("plain")) {
    // Handle JSON commands
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, server->arg("plain"));

    if (error) {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid JSON\"}");
      return;
    }

    String action = doc["action"];

    if (action == "reset") {
      efficiencyCalculator.reset();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Efficiency Calculator reset\"}");
    } else if (action == "debug") {
      efficiencyCalculator.printDebugInfo();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Debug info printed to serial\"}");
    } else if (action == "report") {
      String report = efficiencyCalculator.getEfficiencyReport();
      JsonDocument responseDoc;
      responseDoc["status"] = "OK";
      responseDoc["report"] = report;
      String response;
      serializeJson(responseDoc, response);
      server->send(200, "application/json", response);
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown action\"}");
    }
  } else {
    // Handle URL encoded commands
    String action = server->arg("action");

    if (action == "reset") {
      efficiencyCalculator.reset();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Efficiency Calculator reset\"}");
    } else if (action == "debug") {
      efficiencyCalculator.printDebugInfo();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Debug info printed to serial\"}");
    } else if (action == "report") {
      String report = efficiencyCalculator.getEfficiencyReport();
      JsonDocument responseDoc;
      responseDoc["status"] = "OK";
      responseDoc["report"] = report;
      String response;
      serializeJson(responseDoc, response);
      server->send(200, "application/json", response);
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown action\"}");
    }
  }
}

void XyWebServer::handleTestGet() {
  // Add CORS headers
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type");

  // Get Smart Automation Test instance
  extern SmartAutomationTest smartTest;
  const auto& results = smartTest.getResults();

  // Create JSON response
  JsonDocument doc;

  // Test results summary
  JsonObject summary = doc["summary"].to<JsonObject>();
  summary["totalTests"] = results.totalTests;
  summary["passedTests"] = results.passedTests;
  summary["successRate"] = results.successRate;
  summary["isComplete"] = results.isComplete;
  summary["testDuration"] = results.testDuration;

  // Individual test results
  JsonObject tests = doc["tests"].to<JsonObject>();

  JsonObject filters = tests["filters"].to<JsonObject>();
  filters["initialization"] = results.filterInitialization;
  filters["accuracy"] = results.filterAccuracy;
  filters["stability"] = results.filterStability;
  filters["performance"] = results.filterPerformance;

  JsonObject po = tests["poAlgorithm"].to<JsonObject>();
  po["initialization"] = results.poInitialization;
  po["stateMachine"] = results.poStateMachine;
  po["optimization"] = results.poOptimization;
  po["safety"] = results.poSafety;

  JsonObject efficiency = tests["efficiency"].to<JsonObject>();
  efficiency["initialization"] = results.efficiencyInitialization;
  efficiency["accuracy"] = results.efficiencyAccuracy;
  efficiency["trend"] = results.efficiencyTrend;
  efficiency["stability"] = results.efficiencyStability;

  JsonObject integration = tests["integration"].to<JsonObject>();
  integration["systemIntegration"] = results.systemIntegration;
  integration["webApiIntegration"] = results.webApiIntegration;
  integration["dataConsistency"] = results.dataConsistency;
  integration["performanceMetrics"] = results.performanceMetrics;

  // System information
  JsonObject system = doc["system"].to<JsonObject>();
  system["freeHeap"] = ESP.getFreeHeap();
  system["cpuFreq"] = ESP.getCpuFreqMHz();
  system["uptime"] = millis();
  system["flashSize"] = ESP.getFlashChipSize();

  // Timestamps
  doc["timestamp"] = millis();
  doc["startTime"] = results.startTime;
  doc["endTime"] = results.endTime;

  String response;
  serializeJson(doc, response);
  server->send(200, "application/json", response);
}

void XyWebServer::handleTestPost() {
  // Add CORS headers
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type");

  // Get Smart Automation Test instance
  extern SmartAutomationTest smartTest;

  if (server->hasArg("plain")) {
    // Handle JSON commands
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, server->arg("plain"));

    if (error) {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid JSON\"}");
      return;
    }

    String action = doc["action"];

    if (action == "run-all") {
      // Run comprehensive tests (async)
      runSmartAutomationTests();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Comprehensive tests started\"}");
    } else if (action == "quick-check") {
      // Run quick system check
      runQuickSystemCheck();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Quick system check completed\"}");
    } else if (action == "status") {
      // Print status to serial
      printSmartAutomationStatus();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Status printed to serial\"}");
    } else if (action == "reset") {
      // Reset test results
      smartTest.reset();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Test results reset\"}");
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown action\"}");
    }
  } else {
    // Handle URL encoded commands
    String action = server->arg("action");

    if (action == "run-all") {
      runSmartAutomationTests();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Comprehensive tests started\"}");
    } else if (action == "quick-check") {
      runQuickSystemCheck();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Quick system check completed\"}");
    } else if (action == "status") {
      printSmartAutomationStatus();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Status printed to serial\"}");
    } else if (action == "reset") {
      smartTest.reset();
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Test results reset\"}");
    } else {
      server->send(400, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown action\"}");
    }
  }
}
