#include "efficiency_calculator.h"
#include <math.h>

EfficiencyCalculator::EfficiencyCalculator() {
    // Initialize history buffer
    for (int i = 0; i < 20; i++) {
        mEfficiencyHistory[i] = 0.0;
    }
    
    // Initialize smoothed values
    mSmoothedGasEfficiency = 0.0;
    mSmoothedPowerEfficiency = 0.0;
    mSmoothedThermalEfficiency = 0.0;
    mSmoothedPHEfficiency = 0.0;
    
    Serial.println("EfficiencyCalculator: Initialized with default parameters");
}

void EfficiencyCalculator::setParameters(const EfficiencyParameters& params) {
    mParams = params;
    
    // Validate weighting factors
    float totalWeight = mParams.gasWeight + mParams.powerWeight + 
                       mParams.thermalWeight + mParams.pHWeight;
    
    if (abs(totalWeight - 1.0) > 0.01) {
        Serial.printf("EfficiencyCalculator: Warning - Weight sum %.3f != 1.0, normalizing\n", totalWeight);
        mParams.gasWeight /= totalWeight;
        mParams.powerWeight /= totalWeight;
        mParams.thermalWeight /= totalWeight;
        mParams.pHWeight /= totalWeight;
    }
    
    Serial.println("EfficiencyCalculator: Parameters updated");
}

void EfficiencyCalculator::calculateEfficiency(float gasLevel, float voltage, float current, 
                                             float temperature, float pH) {
    unsigned long currentTime = millis();
    
    // Calculate individual efficiency components
    mMetrics.gasBasedEfficiency = calculateGasBasedEfficiency(gasLevel);
    mMetrics.powerEfficiency = calculatePowerEfficiency(voltage, current, voltage * current);
    mMetrics.thermalEfficiency = calculateThermalEfficiency(temperature);
    mMetrics.pHEfficiency = calculatePHEfficiency(pH);
    
    // Apply smoothing to reduce noise
    applySmoothing();
    
    // Calculate weighted overall efficiency
    mMetrics.overallEfficiency = (mSmoothedGasEfficiency * mParams.gasWeight) +
                                (mSmoothedPowerEfficiency * mParams.powerWeight) +
                                (mSmoothedThermalEfficiency * mParams.thermalWeight) +
                                (mSmoothedPHEfficiency * mParams.pHWeight);
    
    mMetrics.overallEfficiency = constrainEfficiency(mMetrics.overallEfficiency);
    
    // Update efficiency history and calculate trend
    updateEfficiencyHistory(mMetrics.overallEfficiency);
    mMetrics.efficiencyTrend = calculateEfficiencyTrend();
    
    // Calculate additional metrics
    mMetrics.gasImpactFactor = (100.0 - gasLevel) / 100.0; // Higher gas = lower impact factor
    mMetrics.powerUtilization = (voltage * current) / mParams.maxPowerLimit * 100.0;
    mMetrics.processStability = calculateProcessStability();
    
    // Update metadata
    mMetrics.lastUpdate = currentTime;
    mMetrics.isValid = true;
    
    // Debug output every 30 seconds
    static unsigned long lastDebug = 0;
    if (currentTime - lastDebug > 30000) {
        Serial.printf("Efficiency: Overall=%.1f%% (Gas=%.1f%%, Power=%.1f%%, Temp=%.1f%%, pH=%.1f%%) Trend=%.1f%%\n",
                     mMetrics.overallEfficiency, mMetrics.gasBasedEfficiency, 
                     mMetrics.powerEfficiency, mMetrics.thermalEfficiency, 
                     mMetrics.pHEfficiency, mMetrics.efficiencyTrend);
        lastDebug = currentTime;
    }
}

float EfficiencyCalculator::calculateGasBasedEfficiency(float gasLevel) {
    // Gas level is the primary efficiency indicator
    // Lower gas levels indicate better efficiency (less waste gas)
    
    if (gasLevel <= mParams.optimalGasLevel) {
        // Optimal or better gas level
        return 100.0 - (gasLevel * mParams.gasImpactFactor * 10.0);
    } else if (gasLevel <= mParams.maxGasLevel) {
        // Acceptable gas level with reduced efficiency
        float excessGas = gasLevel - mParams.optimalGasLevel;
        float maxExcess = mParams.maxGasLevel - mParams.optimalGasLevel;
        float efficiencyReduction = (excessGas / maxExcess) * 50.0; // Up to 50% reduction
        return 100.0 - (mParams.optimalGasLevel * mParams.gasImpactFactor * 10.0) - efficiencyReduction;
    } else {
        // Dangerous gas level - very low efficiency
        return 10.0; // Minimum efficiency for safety
    }
}

float EfficiencyCalculator::calculatePowerEfficiency(float voltage, float current, float power) {
    // Power efficiency based on optimal power range
    float optimalMin = mParams.optimalPowerRange[0];
    float optimalMax = mParams.optimalPowerRange[1];
    
    if (power >= optimalMin && power <= optimalMax) {
        // Within optimal range
        return 100.0 * mParams.powerEfficiencyFactor;
    } else if (power < optimalMin) {
        // Under-powered - reduced efficiency
        float ratio = power / optimalMin;
        return ratio * 100.0 * mParams.powerEfficiencyFactor;
    } else {
        // Over-powered - diminishing returns
        float excess = power - optimalMax;
        float maxExcess = mParams.maxPowerLimit - optimalMax;
        float efficiencyLoss = (excess / maxExcess) * 30.0; // Up to 30% loss
        return (100.0 - efficiencyLoss) * mParams.powerEfficiencyFactor;
    }
}

float EfficiencyCalculator::calculateThermalEfficiency(float temperature) {
    return applyOptimalRange(temperature, mParams.optimalTempRange[0], 
                           mParams.optimalTempRange[1], mParams.tempImpactFactor);
}

float EfficiencyCalculator::calculatePHEfficiency(float pH) {
    return applyOptimalRange(pH, mParams.optimalPHRange[0], 
                           mParams.optimalPHRange[1], mParams.pHImpactFactor);
}

float EfficiencyCalculator::applyOptimalRange(float value, float optimalMin, float optimalMax, float impactFactor) {
    if (value >= optimalMin && value <= optimalMax) {
        return 100.0; // Optimal efficiency
    } else {
        float deviation = 0.0;
        if (value < optimalMin) {
            deviation = optimalMin - value;
        } else {
            deviation = value - optimalMax;
        }
        
        float efficiencyLoss = deviation * impactFactor * 100.0;
        return constrainEfficiency(100.0 - efficiencyLoss);
    }
}

float EfficiencyCalculator::calculateEfficiencyTrend() {
    if (mHistoryCount < 3) return 0.0; // Need at least 3 samples
    
    // Calculate linear trend using least squares method
    float sumX = 0.0, sumY = 0.0, sumXY = 0.0, sumX2 = 0.0;
    int sampleCount = min(mHistoryCount, mParams.trendSampleCount);
    
    for (int i = 0; i < sampleCount; i++) {
        int index = (mHistoryIndex - i - 1 + 20) % 20;
        float x = i;
        float y = mEfficiencyHistory[index];
        
        sumX += x;
        sumY += y;
        sumXY += x * y;
        sumX2 += x * x;
    }
    
    float slope = (sampleCount * sumXY - sumX * sumY) / (sampleCount * sumX2 - sumX * sumX);
    
    // Convert slope to percentage trend (-100 to +100)
    return constrain(slope * 10.0, -100.0, 100.0);
}

float EfficiencyCalculator::calculateProcessStability() {
    if (mHistoryCount < 5) return 0.0;
    
    // Calculate standard deviation of recent efficiency values
    float mean = 0.0;
    int sampleCount = min(mHistoryCount, 10);
    
    for (int i = 0; i < sampleCount; i++) {
        int index = (mHistoryIndex - i - 1 + 20) % 20;
        mean += mEfficiencyHistory[index];
    }
    mean /= sampleCount;
    
    float variance = 0.0;
    for (int i = 0; i < sampleCount; i++) {
        int index = (mHistoryIndex - i - 1 + 20) % 20;
        float diff = mEfficiencyHistory[index] - mean;
        variance += diff * diff;
    }
    variance /= sampleCount;
    
    float stdDev = sqrt(variance);
    
    // Convert to stability percentage (lower std dev = higher stability)
    return constrainEfficiency(100.0 - (stdDev * 2.0));
}

void EfficiencyCalculator::updateEfficiencyHistory(float efficiency) {
    mEfficiencyHistory[mHistoryIndex] = efficiency;
    mHistoryIndex = (mHistoryIndex + 1) % 20;
    if (mHistoryCount < 20) mHistoryCount++;
}

void EfficiencyCalculator::applySmoothing() {
    float alpha = mParams.trendSmoothingFactor;
    
    mSmoothedGasEfficiency = alpha * mMetrics.gasBasedEfficiency + (1.0 - alpha) * mSmoothedGasEfficiency;
    mSmoothedPowerEfficiency = alpha * mMetrics.powerEfficiency + (1.0 - alpha) * mSmoothedPowerEfficiency;
    mSmoothedThermalEfficiency = alpha * mMetrics.thermalEfficiency + (1.0 - alpha) * mSmoothedThermalEfficiency;
    mSmoothedPHEfficiency = alpha * mMetrics.pHEfficiency + (1.0 - alpha) * mSmoothedPHEfficiency;
}

// Individual efficiency getters
float EfficiencyCalculator::getGasBasedEfficiency(float gasLevel) {
    return calculateGasBasedEfficiency(gasLevel);
}

float EfficiencyCalculator::getPowerEfficiency(float voltage, float current) {
    return calculatePowerEfficiency(voltage, current, voltage * current);
}

float EfficiencyCalculator::getThermalEfficiency(float temperature) {
    return calculateThermalEfficiency(temperature);
}

float EfficiencyCalculator::getPHEfficiency(float pH) {
    return calculatePHEfficiency(pH);
}

void EfficiencyCalculator::reset() {
    // Reset all metrics
    mMetrics = EfficiencyMetrics();
    
    // Clear history
    for (int i = 0; i < 20; i++) {
        mEfficiencyHistory[i] = 0.0;
    }
    mHistoryIndex = 0;
    mHistoryCount = 0;
    
    // Reset smoothed values
    mSmoothedGasEfficiency = 0.0;
    mSmoothedPowerEfficiency = 0.0;
    mSmoothedThermalEfficiency = 0.0;
    mSmoothedPHEfficiency = 0.0;
    
    Serial.println("EfficiencyCalculator: Reset completed");
}

void EfficiencyCalculator::printDebugInfo() {
    Serial.println("=== EFFICIENCY CALCULATOR DEBUG ===");
    Serial.printf("Overall Efficiency: %.2f%%\n", mMetrics.overallEfficiency);
    Serial.printf("  Gas-based: %.2f%% (weight: %.1f%%)\n", mMetrics.gasBasedEfficiency, mParams.gasWeight * 100);
    Serial.printf("  Power: %.2f%% (weight: %.1f%%)\n", mMetrics.powerEfficiency, mParams.powerWeight * 100);
    Serial.printf("  Thermal: %.2f%% (weight: %.1f%%)\n", mMetrics.thermalEfficiency, mParams.thermalWeight * 100);
    Serial.printf("  pH: %.2f%% (weight: %.1f%%)\n", mMetrics.pHEfficiency, mParams.pHWeight * 100);
    Serial.printf("Trend: %.1f%% | Stability: %.1f%%\n", mMetrics.efficiencyTrend, mMetrics.processStability);
    Serial.printf("Gas Impact: %.3f | Power Util: %.1f%%\n", mMetrics.gasImpactFactor, mMetrics.powerUtilization);
    Serial.printf("History Count: %d | Valid: %s\n", mHistoryCount, mMetrics.isValid ? "YES" : "NO");
    Serial.println("===================================");
}

String EfficiencyCalculator::getEfficiencyReport() {
    String report = "=== EFFICIENCY REPORT ===\n";
    report += "Overall: " + String(mMetrics.overallEfficiency, 1) + "% (" + getEfficiencyGrade(mMetrics.overallEfficiency) + ")\n";
    report += "Components:\n";
    report += "  Gas: " + String(mMetrics.gasBasedEfficiency, 1) + "%\n";
    report += "  Power: " + String(mMetrics.powerEfficiency, 1) + "%\n";
    report += "  Thermal: " + String(mMetrics.thermalEfficiency, 1) + "%\n";
    report += "  pH: " + String(mMetrics.pHEfficiency, 1) + "%\n";
    report += "Trend: " + String(mMetrics.efficiencyTrend, 1) + "%\n";
    report += "Stability: " + String(mMetrics.processStability, 1) + "%\n";
    return report;
}

String EfficiencyCalculator::getEfficiencyGrade(float efficiency) {
    if (efficiency >= 90.0) return "A+";
    else if (efficiency >= 80.0) return "A";
    else if (efficiency >= 70.0) return "B";
    else if (efficiency >= 60.0) return "C";
    else if (efficiency >= 50.0) return "D";
    else return "F";
}

String EfficiencyCalculator::getEfficiencyDescription(float efficiency) {
    if (efficiency >= 90.0) return "Excellent";
    else if (efficiency >= 80.0) return "Very Good";
    else if (efficiency >= 70.0) return "Good";
    else if (efficiency >= 60.0) return "Fair";
    else if (efficiency >= 50.0) return "Poor";
    else return "Critical";
}
