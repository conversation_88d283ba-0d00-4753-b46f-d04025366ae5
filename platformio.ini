; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps =
	; XY6020 WiFi control interface dependencies
	emelianov/modbus-esp8266@^4.1.0
	knolleary/PubSubClient@^2.8
	bblanchon/<PERSON>rd<PERSON><PERSON><PERSON><PERSON>@^7.0.4
	khoih-prog/ESP_MultiResetDetector@^1.3.2
	; Electrowinning onsite operation dependencies
    igorantolic/Ai Esp32 Rotary Encoder@^1.7
    marcoschwartz/LiquidCrystal_I2C@^1.1.4
    ; DS18B20 Temperature Sensor
    paulstoffregen/OneWire@^2.3.7
    milesburton/DallasTemperature@^3.11.0
monitor_speed = 115200
build_flags =
	-D ESP32
